import { ReactNode } from 'react';
import { ColumnCustomTdProps, ColumnCustomTrProps, ColumnRender, DataKey, DisableDraggable, Key } from './Types';
import { TableTdProps, TableTrProps } from '@mantine/core';
import { CSSProperties } from 'styled-components';

export function renderCell<T extends object>(record: T, render: ColumnRender<T>): ReactNode {
  if (typeof render === 'function') {
    return render(record);
  }
  return record[render] as ReactNode;
}

export function getDataKey<T extends object>(record: T, dataKey: DataKey<T>): Key {
  if (typeof dataKey === 'function') {
    return dataKey(record);
  }
  return record[dataKey] as Key;
}

export function getCustomTdProps<T extends object>(record: T, index: number, customTdProps?: ColumnCustomTdProps<T>): TableTdProps | undefined {
  if (typeof customTdProps === 'function') {
    return customTdProps(record, index);
  }
  return customTdProps;
}

export function getCustomTrProps<T extends object>(record: T, index: number, customTrProps?: ColumnCustomTrProps<T>): TableTrProps | undefined {
  if (typeof customTrProps === 'function') {
    return customTrProps(record, index);
  }
  return customTrProps;
}

export function getFirstPageItemIndex(rowPerPage: number, pageNumber: number) {
  return rowPerPage * (pageNumber - 1) + 1;
}

export function getLastPageItemIndex(rowPerPage: number, pageNumber: number) {
  return rowPerPage * pageNumber;
}

export function getItemIndex(rowPerPage: number, pageNumber: number, itemIndex: number) {
  return getFirstPageItemIndex(rowPerPage, pageNumber) + itemIndex;
}

export function getDisableDraggable<T extends object>(record: T, disableDraggable: DisableDraggable<T>): boolean {
  if (typeof disableDraggable === 'function') {
    return disableDraggable(record);
  }
  return disableDraggable;
}

export function getDefaultBgColor(index: number): CSSProperties['backgroundColor'] {
  return index % 2 === 0 ? 'var(--table-striped-color)' : 'transparent';
}
