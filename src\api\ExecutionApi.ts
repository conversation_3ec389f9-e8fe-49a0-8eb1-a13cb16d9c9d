import { createPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { PaginationModel } from '@models/PaginationModel';
import { ExecutionSchema } from '@core/schema/Execution';
import { ExecutionModel } from '@models/ExecutionModel';
import { z } from 'zod';
import { ExecuteScriptModel } from '@models/ExecuteScriptModel';
import { ExecuteScriptSchema } from '@core/schema/ExecuteScript';

export class ExecutionApi {
  static findAllWithPaging(pagination: PaginationModel) {
    return createRequest({
      url: BaseURL.execution,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(ExecutionSchema)),
    });
  }
  static findById(id: string) {
    return createRequest({
      url: `${BaseURL.execution}/:id`,
      method: 'GET',
      pathVariable: { id },
      schema: createResponseSchema(ExecutionSchema),
    });
  }
  static findByIdWithVariable(id: string) {
    return createRequest({
      url: `${BaseURL.execution}/:id/with-envs`,
      method: 'GET',
      pathVariable: { id },
      schema: createResponseSchema(ExecutionSchema),
    });
  }
  static createOrUpdate(executionModel: ExecutionModel) {
    return createRequest({
      url: BaseURL.execution,
      method: 'POST',
      data: executionModel,
      schema: createResponseSchema(ExecutionSchema),
    });
  }
  static deleteById(id: string) {
    return createRequest({
      url: `${BaseURL.execution}/:id`,
      method: 'DELETE',
      pathVariable: { id },
    });
  }
  static findAllByExecutionGroupId(executionGroupId: string) {
    return createRequest({
      url: BaseURL.execution,
      method: 'GET',
      params: { executionGroupId },
      schema: createResponseSchema(z.array(ExecutionSchema)),
    });
  }
  static findAllWithPermissionByExecutionGroupId(searchParam: { executionGroupId: string; search?: string }) {
    return createRequest({
      url: `${BaseURL.execution}/with-permissions`,
      method: 'GET',
      params: searchParam,
      schema: createResponseSchema(z.array(ExecutionSchema)),
    });
  }
  static execute(request: ExecuteScriptModel) {
    return createRequest({
      url: `${BaseURL.execution}/execute`,
      method: 'POST',
      data: request,
      schema: createResponseSchema(ExecuteScriptSchema),
    });
  }
}
