import { z } from 'zod';

const PaginationSqlCommandRequestSchema = z.object({
  page: z.number(),
  size: z.number(),
});
export type PaginationSqlCommandRequest = z.infer<typeof PaginationSqlCommandRequestSchema>;
export const QuerySqlExecuteModelSchema = z.object({
  id: z.number(),
  params: z.record(z.string(), z.any()),
  paginationRequest: PaginationSqlCommandRequestSchema.optional(),
});
export type QuerySqlExecuteModel = z.infer<typeof QuerySqlExecuteModelSchema>;
