.root {
  position: relative;
  background-color: white;
  width: fit-content;
  padding: calc(var(--mantine-spacing-xs) / 2);
  border: 1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-4));
}

.control {
  padding: calc(var(--mantine-spacing-xs) / 2);
  color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-2));
  font-size: var(--mantine-font-size-sm);
  transition: color 100ms ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
    background-color: light-dark(var(--mantine-color-gray-1), var(--mantine-color-dark-7));
  }

  &[data-active] {
    color: var(--mantine-color-white);
  }
}

.controlLabel {
  position: relative;
  z-index: 1;
}

.indicator {
  background-color: var(--mantine-primary-color-filled);
  border-radius: var(--mantine-radius-sm);
}
.navContent{
  border: 1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-4));
  border-left: none;
  background-color: white;
  width: 250px;
  padding: calc(var(--mantine-spacing-xs) / 2);
  overflow: auto;
}
.alertAmount{
  font-weight: 600;
  font-size: 10px;
  line-height: 14px;
  border-radius: 100px;
  color: white;
  padding: calc(var(--mantine-spacing-xs) / 2);
  min-width: 25px;
  text-align: center;
  text-wrap: nowrap;
}
.treeTitle{
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
}
.accordionContent{
  padding: calc(var(--mantine-spacing-xs) / 2);
  cursor: pointer;
  &:hover{
    background-color: var(--mantine-color-gray-2);
  }
}
.accordionItem{
  background-color: white;
}
.multipleInput{
  max-height: 60px;
  overflow: auto;
}