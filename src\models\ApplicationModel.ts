import { PaginationRequest } from '@api/Type';
import { z } from 'zod';
import { SERVICE_APPLICATION_NAME_REGEX } from '@common/constants/RegexConstant';
import { SERVICE_SELECT_MESSAGE_ERROR } from '@core/message/MesageConstant';
export type ApplicationPaginationRequest = PaginationRequest & {
  name?: string;
  withDeleted?: boolean;
  serviceIds?: string[];
};

export const ApplicationModelSchema = z.object({
  id: z.string(),
  name: z.string().trim().min(1),
  description: z.string().nullish(),
  serviceId: z.string({ message: SERVICE_SELECT_MESSAGE_ERROR }).regex(SERVICE_APPLICATION_NAME_REGEX, { message: SERVICE_SELECT_MESSAGE_ERROR }),
});

export type ApplicationModel = z.infer<typeof ApplicationModelSchema>;
