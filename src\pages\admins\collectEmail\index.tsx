import React, { useState, useMemo, useRef, useCallback, useEffect } from 'react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { ActionIcon, Box, Flex, Switch } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { IconEye, IconPlus, IconTrash } from '@tabler/icons-react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { IconEdit } from '@tabler/icons-react';
import { SortType } from '@common/constants/SortType';
import { PaginationRequest } from '@api/Type';
import useMutate from '@core/hooks/useMutate';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { createSearchParams, useNavigate } from 'react-router-dom';
import { BaseCollectEmailConfig, CollectEmailConfig } from '@core/schema/CollectEmailConfig';
import { CollectEmailConfigApi } from '@api/CollectEmailConfigApi';
import { CollectEmailConfigAction, CollectEmailOperatorEnum } from '@common/constants/CollectEmailConfigConstant';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { IconCopyPlus } from '@tabler/icons-react';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { optionCollectEmailConfigTypes } from '@common/constants/CollectEmailConfigTypeConstant';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST } from '../modifyAlertConfig/Constants';
import { QueryBuilderField } from '@components/queryBuilder';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { PreviewCondition } from '../../../components/queryBuilder/PreviewCondition';
import { RuleGroupType } from 'react-querybuilder';
import { baseFields } from './Fields';

export const CollectEmailConfigPage = () => {
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const navigate = useNavigate();
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const [fields, setFields] = useState<QueryBuilderField[]>(baseFields);
  const columns = useMemo<ColumnType<BaseCollectEmailConfig>[]>(
    () => [
      { title: 'Name', name: 'name', width: '10%' },
      { title: 'Description', name: 'description', width: '10%' },
      {
        title: 'Alert type',
        name: 'type',
        width: '10%',
        customRender: (value) => optionCollectEmailConfigTypes.find((e) => e.value === value)?.label ?? value,
      },
      { title: 'Email', name: 'emailConfigEmail', width: '20%' },
      { title: 'Interval', name: 'emailConfigIntervalTime', width: '5%' },
      {
        title: 'Condition',
        name: 'ruleGroup',
        width: '40%',
        sortable: false,
        customRender: (value) => (
          <PreviewCondition fields={fields} operators={Object.values(CollectEmailOperatorEnum)} value={value as RuleGroupType} />
        ),
      },
    ],
    [fields],
  );
  // API
  const { data: listCollectEmailConfig, refetch: refetchList } = useFetch(CollectEmailConfigApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const { mutate: activeOrInactive } = useMutate(CollectEmailConfigApi.activeOrInactive, {
    successNotification: 'Update status of config successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
  });
  const { mutate: deleteByIdMutate } = useMutate(CollectEmailConfigApi.deleteById, {
    successNotification: 'Deleted successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });
  const { data: listCustomObject } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), {
    placeholderData: (prev) => prev,
  });
  useEffect(() => {
    if (listCustomObject?.data) {
      const customObjecFields: QueryBuilderField[] =
        listCustomObject?.data.content.map((customObj) => ({
          name: customObj.id.toString(),
          label: customObj.name,
          operators: Object.values(QueryBuilderOperatorEnum),
        })) ?? [];

      setFields((prev) => [...prev, ...customObjecFields]);
    }
  }, [listCustomObject?.data]);
  //Function update table affected
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<CollectEmailConfig>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const handleChangeURL = useCallback(
    (param: number, value: string | null) => {
      navigate({
        pathname: `${ROUTE_PATH.EMAIL_COLLECT}/${param}`,
        search: createSearchParams({
          action: value || CollectEmailConfigAction.CREATE,
        }).toString(),
      });
    },
    [navigate],
  );

  const tableViewListRolesProps: KanbanTableProps<BaseCollectEmailConfig> = useMemo(() => {
    return {
      columns: columns,
      data: listCollectEmailConfig?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listCollectEmailConfig?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.emailCollectEdit]}>
                <KanbanTooltip label='Edit'>
                  <Switch
                    checked={data.active}
                    onClick={() => {
                      activeOrInactive(data.id);
                    }}
                  />
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailCollectView]}>
                <KanbanTooltip label='View'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      handleChangeURL(data.id, CollectEmailConfigAction.VIEW);
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailCollectEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      handleChangeURL(data.id, CollectEmailConfigAction.UPDATE);
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailCollectCreate]}>
                <KanbanTooltip label='Clone'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      handleChangeURL(data.id, CollectEmailConfigAction.COPY);
                    }}>
                    <IconCopyPlus />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailCollectDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    activeOrInactive,
    columns,
    deleteByIdMutate,
    handleChangeURL,
    handleUpdateTablePagination,
    listCollectEmailConfig?.data?.content,
    listCollectEmailConfig?.data?.totalElements,
    tableAffected,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of collect email config'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.emailCollectCreate]}>
            <Flex direction='row' gap='xs' align='center'>
              <KanbanButton
                size='xs'
                onClick={() => {
                  handleChangeURL(0, CollectEmailConfigAction.CREATE);
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
    </Box>
  );
};
export default CollectEmailConfigPage;
