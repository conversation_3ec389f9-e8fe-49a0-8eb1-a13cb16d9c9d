REACT_APP_NAME="Monitoring tool"
REACT_APP_FULLNAME=MBMONITOR
REACT_APP_DESCRIPTION="MB MONITORING"
REACT_APP_DEPLOY_URL=http://localhost:9000
REACT_APP_MBMONITOR_API_URL=http://localhost:9000
######Config for remote url service
REACT_APP_MBMONITOR_API_URL_REMOTE=https://mbmonitor.tanzu-uat.mbbank.com.vn
#Split by ",", ex: server,external-mail
REACT_APP_MONITOR_API_SERVICE_REMOTE=
######End Config for remote url service

REACT_APP_MBMONITOR_API_WEBHOOK_COLLECT=http://localhost:9000/api/external-webhook/v1/collect-data
#Keycloak config
REACT_APP_KEYCLOAK_URL=https://keycloak-internal-uat.mbbank.com.vn/auth
REACT_APP_KEYCLOAK_REALM=internal
REACT_APP_KEYCLOAK_CLIENT_ID=mbmonitor-frontend
REACT_APP_KEYCLOAK_ENABLED=true
#8MB 8 * 1024 * 1024 
MAX_FILE_ATTACH_SIZE=8388608
#10MB 8 * 1024 * 1024 
MAX_EMAIL_ATTACH_SIZE=********
WHY_DID_YOU_RERENDER_ENABLE=false
GENERATE_SOURCEMAP=true
#8*60*1000: 8m
REFRESH_TOKEN_INTERVAL=480000