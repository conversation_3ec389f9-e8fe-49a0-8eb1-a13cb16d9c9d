import { createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { TeamsConfigSchema } from '@core/schema/Teams';
import { TeamsModel, TeamsSendMessageModel, TeamsSendMessageSchema } from '@models/TeamsModel';

export class TeamsAlertConfigApi {
  static findConfig() {
    return createRequest({
      url: `${BaseURL.teamsAlert}`,
      method: 'GET',
      schema: createResponseSchema(TeamsConfigSchema),
    });
  }

  static saveConfig(config: TeamsModel) {
    return createRequest({
      url: `${BaseURL.teamsAlert}`,
      method: 'POST',
      data: config,
    });
  }

  static triggerJob() {
    return createRequest({
      url: `${BaseURL.teamsAlert}/trigger-job`,
      method: 'POST',
    });
  }

  static sendMessage(data: TeamsSendMessageModel) {
    return createRequest({
      url: `${BaseURL.teamsAlert}/send`,
      method: 'POST',
      data: data,
      schema: createResponseSchema(TeamsSendMessageSchema),
    });
  }
}
