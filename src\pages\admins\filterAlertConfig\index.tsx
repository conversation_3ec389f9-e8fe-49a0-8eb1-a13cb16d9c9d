import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ActionIcon, Box, Flex, Tooltip } from '@mantine/core';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanSwitch,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { IconEdit, IconEye } from '@tabler/icons-react';
import { IconPlus } from '@tabler/icons-react';
import { createSearchParams, useNavigate } from 'react-router-dom';
import classes from './GroupConfigStyle.module.css';
import { IconTrash } from '@tabler/icons-react';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { AclPermission } from '@models/AclPermission';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST, FilterAlertConfigAction } from './Constants';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { BaseFilterAlertConfig } from '@core/schema/FilterAlertConfig';
import { FilterAlertConfigApi } from '@api/FilterAlertConfigApi';
import { PaginationRequest } from '@api/Type';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { SortType } from '@common/constants/SortType';
import Table from '@components/table';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { QueryBuilderField } from '@components/queryBuilder';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { baseFields } from '../collectEmail/Fields';
import { PreviewCondition } from '@components/queryBuilder/PreviewCondition';
import { CollectEmailOperatorEnum } from '@common/constants/CollectEmailConfigConstant';
import { RuleGroupType } from 'react-querybuilder';

const ActionColumn = ({
  filterAlertConfig,
  handleChangeURL,
  refetch,
}: {
  filterAlertConfig: BaseFilterAlertConfig;
  refetch: () => void;
  handleChangeURL: (param: number, value: string | null) => void;
}) => {
  const { mutate: deleteMutate } = useMutate(FilterAlertConfigApi.delete, {
    successNotification: 'Delete Filter Alert config success',
    errorNotification: 'Delete Filter Alert config failed!',
    confirm: getDefaultDeleteConfirmMessage(),
    onSuccess: () => refetch(),
  });
  const { mutate: updateActiveMutate } = useMutate(FilterAlertConfigApi.updateActive, {
    successNotification: `${filterAlertConfig.active ? 'Inactive' : 'Active'} Filter Alert config success`,
    errorNotification: `${filterAlertConfig.active ? 'Inactive' : 'Active'} Filter Alert config failed!`,
    onSuccess: () => refetch(),
  });
  return (
    <Flex gap='xs' align='center'>
      <GuardComponent requirePermissions={[AclPermission.filterAlertConfigEdit, AclPermission.filterAlertConfigView]}>
        <Tooltip label={filterAlertConfig.active ? 'Active' : 'Inactive'}>
          <KanbanSwitch
            checked={filterAlertConfig.active || false}
            onChange={
              isAnyPermissions([AclPermission.filterAlertConfigEdit])
                ? (event) => updateActiveMutate({ id: filterAlertConfig.id, active: event.target.checked })
                : undefined
            }
          />
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.filterAlertConfigView]}>
        <Tooltip label='View Config'>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              handleChangeURL(filterAlertConfig.id, FilterAlertConfigAction.VIEW);
            }}>
            <IconEye />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.filterAlertConfigEdit]}>
        <Tooltip label='Edit Config'>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              handleChangeURL(filterAlertConfig.id, FilterAlertConfigAction.UPDATE);
            }}>
            <IconEdit />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.filterAlertConfigDelete]}>
        <ActionIcon
          variant='transparent'
          color='red'
          onClick={() =>
            deleteMutate(filterAlertConfig.id, {
              confirm: getDefaultDeleteConfirmMessage(filterAlertConfig.name),
            })
          }>
          <IconTrash width={20} height={24} />
        </ActionIcon>
      </GuardComponent>
    </Flex>
  );
};

const FilterAlertConfigPage = () => {
  const [fields, setFields] = useState<QueryBuilderField[]>(baseFields);
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const { data, refetch } = useFetch(FilterAlertConfigApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const navigate = useNavigate();
  const columns = useMemo<ColumnType<BaseFilterAlertConfig>[]>(
    () => [
      {
        title: 'Name',
        name: 'name',
      },
      {
        title: 'Description',
        name: 'description',
      },
      {
        title: 'Condition',
        name: 'ruleGroup',
        width: '40%',
        sortable: false,
        customRender: (value) => (
          <PreviewCondition fields={fields} operators={Object.values(CollectEmailOperatorEnum)} value={value as RuleGroupType} />
        ),
      },
    ],
    [fields],
  );

  const handleChangeURL = useCallback(
    (param: number, value: string | null) => {
      navigate({
        pathname: `${ROUTE_PATH.FILTER_ALERT_CONFIG}/${param}`,
        search: createSearchParams({
          action: value || FilterAlertConfigAction.CREATE,
        }).toString(),
      });
    },
    [navigate],
  );

  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<BaseFilterAlertConfig>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );

  const tableViewListRolesProps: KanbanTableProps<BaseFilterAlertConfig> = useMemo(() => {
    return {
      columns: columns,
      data: data?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: data?.data?.totalElements ?? 0,
        onTableAffected: handleUpdateTablePagination,
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return <ActionColumn handleChangeURL={handleChangeURL} filterAlertConfig={data} refetch={refetch} />;
        },
      },
    };
  }, [columns, data?.data?.content, data?.data?.totalElements, handleChangeURL, handleUpdateTablePagination, refetch]);
  const { data: listCustomObject } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), {
    placeholderData: (prev) => prev,
  });
  useEffect(() => {
    if (listCustomObject?.data) {
      const customObjecFields: QueryBuilderField[] =
        listCustomObject?.data.content.map((customObj) => ({
          name: customObj.id.toString(),
          label: customObj.name,
          operators: Object.values(QueryBuilderOperatorEnum),
        })) ?? [];

      setFields((prev) => [...prev, ...customObjecFields]);
    }
  }, [listCustomObject?.data]);
  return (
    <Box className={classes.groupConfigWrapper}>
      <HeaderTitleComponent
        title='Filter Alert Config'
        rightSection={
          <Flex gap='md'>
            <GuardComponent requirePermissions={[AclPermission.filterAlertConfigCreate]}>
              <KanbanButton
                onClick={() => {
                  handleChangeURL(0, FilterAlertConfigAction.CREATE);
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
    </Box>
  );
};

export default FilterAlertConfigPage;
