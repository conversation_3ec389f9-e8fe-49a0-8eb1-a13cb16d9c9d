import React, { useContext } from 'react';
import { TaskStatusEnum, TaskStatusLabel, TaskTypeEnum, TaskTypeLabel } from '@common/constants/TaskConstants';
import { Box, ComboboxData, SimpleGrid } from '@mantine/core';
import { KanbanInput, KanbanMultiSelect } from 'kanban-design-system';
import { Controller, useForm } from 'react-hook-form';
import { useDebouncedCallback } from '@mantine/hooks';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { zodResolver } from '@hookform/resolvers/zod';
import { FilterTaskModelSchema } from '@models/FilterTaskModel';
import { EventPageContext } from '../EventPageContext';
import { TABLE_INPUT_MAX_LENGTH } from '@common/constants/ValidationConstant';
import UserMultipleSelect from '@components/UserMultipleSelect';

const TaskTypeOptions: ComboboxData = Object.keys(TaskTypeEnum).map((key) => ({ value: key, label: TaskTypeLabel[key as TaskTypeEnum] }));
const TaskStatusOptions: ComboboxData = Object.keys(TaskStatusEnum).map((key) => ({ value: key, label: TaskStatusLabel[key as TaskStatusEnum] }));

const TaskFilter = () => {
  const { filterValue, setFilterValue } = useContext(EventPageContext);
  const { control, getValues, register } = useForm({
    defaultValues: filterValue,
    resolver: zodResolver(FilterTaskModelSchema),
  });
  const changeFilterFormValueDebounced = useDebouncedCallback(() => {
    setFilterValue({ ...getValues(), dateRanges: filterValue.dateRanges });
  }, DEFAULT_DEBOUNCE_TIME);

  return (
    <SimpleGrid spacing='xs' cols={3}>
      <KanbanInput
        placeholder='Search'
        {...register('search')}
        onChange={(event) => {
          register('search').onChange(event);
          changeFilterFormValueDebounced();
        }}
        mb={0}
        maxLength={TABLE_INPUT_MAX_LENGTH}
      />
      <Controller
        control={control}
        name='types'
        render={({ field }) => (
          <KanbanMultiSelect
            placeholder={field.value?.length === 0 ? 'Select type' : undefined}
            data={TaskTypeOptions}
            style={{ flex: 1 }}
            {...field}
            onChange={(value) => {
              field.onChange(value);
              changeFilterFormValueDebounced();
            }}
            mb={0}
          />
        )}
      />
      <Controller
        control={control}
        name='statuses'
        render={({ field }) => (
          <KanbanMultiSelect
            placeholder={field.value?.length === 0 ? 'Select status' : undefined}
            data={TaskStatusOptions}
            style={{ flex: 1 }}
            {...field}
            onChange={(value) => {
              field.onChange(value);
              changeFilterFormValueDebounced();
            }}
            mb={0}
          />
        )}
      />
      <Controller
        control={control}
        name='creatorUsers'
        render={({ field }) => (
          <Box flex={1}>
            <UserMultipleSelect
              placeholder='Select creator'
              value={field.value}
              onChange={(value) => {
                field.onChange(value);
                changeFilterFormValueDebounced();
              }}
            />
          </Box>
        )}
      />
      <Controller
        control={control}
        name='assigneeUsers'
        render={({ field }) => (
          <Box flex={1}>
            <UserMultipleSelect
              placeholder='Select assignee'
              value={field.value}
              onChange={(value) => {
                field.onChange(value);
                changeFilterFormValueDebounced();
              }}
            />
          </Box>
        )}
      />
    </SimpleGrid>
  );
};

export default TaskFilter;
