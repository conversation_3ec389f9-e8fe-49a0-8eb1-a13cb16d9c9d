export enum AlertGroupOutputEnum {
  LASTED_ALERT = 'LASTED_ALERT',
  HIGHEST_PRIORITY = 'HIGHEST_PRIORITY',
  LOWHEST_PRIORITY = 'LOWEST_PRIORITY',
  CUSTOM = 'CUSTOM',
}

export const ALERT_GROUP_OUTPUT_LABEL: { [key in AlertGroupOutputEnum]: string } = {
  [AlertGroupOutputEnum.LASTED_ALERT]: 'Lastest Alert',
  [AlertGroupOutputEnum.HIGHEST_PRIORITY]: 'Highest Alert with priority',
  [AlertGroupOutputEnum.LOWHEST_PRIORITY]: 'Lowest Alert with priority',
  [AlertGroupOutputEnum.CUSTOM]: 'Custom',
};

export enum AlertGroupConfigTypeEnum {
  SAME_OBJECT_VALUE = 'SAME_OBJECT_VALUE',
  MULTIPLE_CONDITION = 'MULTIPLE_CONDITION',
}

export const ALERT_GROUP_CONFIG_TYPE_LABEL: {
  [key in AlertGroupConfigTypeEnum]: string;
} = {
  [AlertGroupConfigTypeEnum.MULTIPLE_CONDITION]: 'Group by multi condition',
  [AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE]: 'Group have the same object value',
};
