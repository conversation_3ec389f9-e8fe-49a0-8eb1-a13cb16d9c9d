import { DESCRIPTION_MAX_LENGTH, QUERY_SQL_NAME_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const QuerySqlModelSchema = z.object({
  id: z.number().optional(),
  name: z.string().trim().min(1).max(QUERY_SQL_NAME_MAX_LENGTH),
  description: z.string().max(DESCRIPTION_MAX_LENGTH).optional(),
  command: z.string().min(1),
  groupQuerySqlId: z.number().min(1),
  databaseConnectionId: z.number().min(1),
});
export type QuerySqlModel = z.infer<typeof QuerySqlModelSchema>;
