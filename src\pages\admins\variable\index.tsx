import { Variable<PERSON>pi } from '@api/VariableApi';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { SortType } from '@common/constants/SortType';
import GuardComponent from '@components/GuardComponent';
import { DEFAULT_DEBOUNCE_TIME } from '@components/InfiniteScrollAccordion';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { Variable } from '@core/schema/Variable';
import { PaginationModel } from '@models/PaginationModel';
import { IconTrash } from '@tabler/icons-react';
import { ColumnType, KanbanIconButton, KanbanTableProps, KanbanTooltip, TableAffactedSafeType } from 'kanban-design-system';
import React, { useCallback, useMemo, useState } from 'react';
import { Box, Flex } from '@mantine/core';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { HIDDEN_VARIABLE_PLACEHOLDER } from '@common/constants/ExecutionConstants';
import { AclPermission } from '@models/AclPermission';
import UpdateVariableButton from './UpdateVariableButton';
import CreateVariableButton from './CreateVariableButton';

export const COLUMNS: ColumnType<Variable>[] = [
  {
    title: 'Name',
    name: 'name',
    width: '30%',
  },
  {
    title: 'Description',
    name: 'description',
    width: '30%',
  },
  {
    title: 'Value',
    name: 'value',
    width: '40%',
    customRender: (_, data) => {
      if (data.hidden) {
        return HIDDEN_VARIABLE_PLACEHOLDER;
      }
      return data.value;
    },
    sortable: false,
  },
];

const VariablePage = () => {
  const [tableAffected, setTableAffected] = useState<PaginationModel>(DEFAULT_PAGINATION_REQUEST);
  const { data: variableData, refetch } = useFetch(VariableApi.findAllWithPaging(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<Variable>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const { mutate: deleteMutate } = useMutate(VariableApi.deleteById, {
    successNotification: 'Delete Execution Variable successfully.',
    onSuccess: () => refetch(),
    confirm: { enable: true, title: '' },
  });

  const tableViewListRolesProps: KanbanTableProps<Variable> = useMemo(() => {
    return {
      columns: COLUMNS,
      data: variableData?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: variableData?.data?.totalElements ?? 0,
        onTableAffected: handleUpdateTablePagination,
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.variableView, AclPermission.variableEdit]}>
                <UpdateVariableButton variable={data} onUpdateSuccess={refetch} />
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.variableDelete]}>
                <KanbanTooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => deleteMutate(data.id, { confirm: getDefaultDeleteConfirmMessage(data.name) })}>
                    <IconTrash color='red' />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [deleteMutate, variableData?.data?.content, variableData?.data?.totalElements, handleUpdateTablePagination, refetch]);

  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of variable'
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <GuardComponent requirePermissions={[AclPermission.variableCreate]}>
              <CreateVariableButton onCreateSuccess={refetch} />
            </GuardComponent>
          </Flex>
        }
      />
      <Table {...tableViewListRolesProps} />
    </Box>
  );
};

export default VariablePage;
