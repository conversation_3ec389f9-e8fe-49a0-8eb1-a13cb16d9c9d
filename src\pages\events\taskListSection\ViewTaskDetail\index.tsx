import Modal from '@components/Modal';
import { ActionIcon, Grid, Stack, Tabs } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconEye } from '@tabler/icons-react';
import { KanbanButton, KanbanText } from 'kanban-design-system';
import React, { useCallback, useContext, useEffect } from 'react';
import { TaskApi } from '@api/TaskApi';
import useFetch from '@core/hooks/useFetch';
import { TaskTypeEnum } from '@common/constants/TaskConstants';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TaskModel, TaskModelSchema } from '@models/TaskModel';
import dayjs from 'dayjs';
import InformationTab from './InformationTab';
import FieldTitle from '@pages/events/FieldTitle';
import TaskStatus from '../TaskStatus';
import AssignHistoryTab from './AssignHistoryTab';
import useMutate from '@core/hooks/useMutate';
import { EventPageContext } from '@pages/events/EventPageContext';
import { refetchEventPageData } from '../Utilts';
import { IconEdit } from '@tabler/icons-react';
import CompleteTaskButton from '../CompleteTaskButton';

interface Props {
  taskId: number;
  editable: boolean;
}

const ViewTaskDetailButton = ({ editable, taskId }: Props) => {
  const [opended, { close, open }] = useDisclosure();
  const { data: taskData } = useFetch(TaskApi.findByTaskId(taskId), { enabled: opended });
  const form = useForm<TaskModel>({ mode: 'onChange', resolver: zodResolver(TaskModelSchema) });
  const {
    formState: { isValid },
    reset,
  } = form;
  const { calendarMode, filterValue } = useContext(EventPageContext);
  useEffect(() => {
    const task = taskData?.data;
    if (task) {
      reset({
        ...task,
        startTime: task.startTime ? dayjs(task.startTime).format() : undefined,
        endTime: task.endTime ? dayjs(task.endTime).format() : undefined,
        handoverUsers: [],
      });
    }
  }, [reset, taskData?.data]);

  const { mutate: updateTaskMutate } = useMutate(TaskApi.createOrUpdate, {
    onSuccess: () => {
      refetchEventPageData(calendarMode, filterValue);
      close();
    },
  });
  const onCompleteSuccess = useCallback(() => {
    refetchEventPageData(calendarMode, filterValue);
    close();
  }, [calendarMode, close, filterValue]);
  const { mutate: completeTaskMutate } = useMutate(TaskApi.completeTask, {
    onSuccess: onCompleteSuccess,
    confirm: {
      title: 'Complete task',
      children: (
        <KanbanText>
          Are you sure to complete task <b>{taskData?.data?.name || ''}</b>?
        </KanbanText>
      ),
    },
  });
  return (
    <>
      <ActionIcon variant='transparent' onClick={open}>
        {editable ? <IconEdit size={18} /> : <IconEye size={18} />}
      </ActionIcon>
      {taskData?.data?.type === TaskTypeEnum.TASK && (
        <Modal
          onClose={() => {
            close();
          }}
          opened={opended}
          size='xl'
          title='Task detail'
          actions={
            <>
              {taskData?.data && <CompleteTaskButton task={taskData.data} onClick={() => completeTaskMutate(taskData.data?.id || 0)} />}
              {editable && (
                <KanbanButton onClick={() => updateTaskMutate(form.getValues())} disabled={!isValid}>
                  Save
                </KanbanButton>
              )}
            </>
          }>
          <Stack>
            <Grid gutter='sm'>
              <Grid.Col span={2}>
                <FieldTitle title='Creator' />
              </Grid.Col>
              <Grid.Col span={10}>{taskData?.data?.createdBy}</Grid.Col>
              <Grid.Col span={2}>
                <FieldTitle title='Status' />
              </Grid.Col>
              <Grid.Col span={10}>{taskData?.data && <TaskStatus status={taskData?.data?.status} />}</Grid.Col>
              <Grid.Col span={2}>
                <FieldTitle title='Assignee' />
              </Grid.Col>
              <Grid.Col span={10}>{taskData?.data?.currentAssigneeUserName}</Grid.Col>
            </Grid>
            <Tabs defaultValue='information'>
              <Tabs.List>
                <Tabs.Tab value='information'>Information</Tabs.Tab>
                <Tabs.Tab value='history'>History</Tabs.Tab>
              </Tabs.List>
              <Tabs.Panel value='information'>
                <InformationTab form={form} task={taskData?.data} editable={editable} />
              </Tabs.Panel>
              <Tabs.Panel value='history'>
                <AssignHistoryTab task={taskData?.data} />
              </Tabs.Panel>
            </Tabs>
          </Stack>
        </Modal>
      )}
    </>
  );
};

export default ViewTaskDetailButton;
