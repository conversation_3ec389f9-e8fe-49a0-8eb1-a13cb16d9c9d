import { createResponseSchema, ResponseData } from '@core/schema';
import { RequestConfig } from '../core/api/BaseApi';
import { BaseURL } from '@common/constants/BaseUrl';
import { DatabaseConnection, DatabaseConnectionSchema } from '@core/schema/DatabaseConnection';
import { DatabaseConnectionModel } from '@models/DatabaseConnectionModel';
import { z } from 'zod';

export class DatabaseConnectionApi {
  static findAll(searchParam?: { orderBy?: string }): RequestConfig<ResponseData<DatabaseConnection[]>> {
    return {
      url: BaseURL.databaseConnection,
      method: 'GET',
      params: searchParam,
      schema: createResponseSchema(z.array(DatabaseConnectionSchema)),
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<string>> {
    return {
      url: `${BaseURL.databaseConnection}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    };
  }

  static deleteBatch(ids: number[]): RequestConfig<ResponseData<string>> {
    return {
      url: `${BaseURL.databaseConnection}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    };
  }

  static findById(id: number): RequestConfig<ResponseData<DatabaseConnection>> {
    return {
      url: `${BaseURL.databaseConnection}/:id`,
      method: 'GET',
      schema: createResponseSchema(DatabaseConnectionSchema),
      pathVariable: {
        id,
      },
    };
  }

  static save(body: DatabaseConnectionModel): RequestConfig<ResponseData<DatabaseConnection>> {
    return {
      url: BaseURL.databaseConnection,
      method: 'POST',
      data: body,
    };
  }

  static testConnection(body: DatabaseConnectionModel): RequestConfig<ResponseData<boolean>> {
    return {
      url: `${BaseURL.databaseConnection}/test-connection`,
      method: 'POST',
      data: body,
    };
  }

  static activeById(id: number): RequestConfig<ResponseData<string>> {
    return {
      url: `${BaseURL.databaseConnection}/${id}/status`,
      method: 'PUT',
      params: { active: true },
    };
  }

  static inactiveById(id: number): RequestConfig<ResponseData<string>> {
    return {
      url: `${BaseURL.databaseConnection}/${id}/status`,
      method: 'PUT',
      params: { active: false },
    };
  }
}
