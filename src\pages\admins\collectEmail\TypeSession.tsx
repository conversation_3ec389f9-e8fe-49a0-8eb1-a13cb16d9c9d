import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Flex } from '@mantine/core';
import { ABSENCE_INTERVAL_MIN, CollectEmailConfigModel } from '@models/CollectEmailConfigModel';
import { KanbanSelect } from 'kanban-design-system';
import { CollectEmailConfigTypeEnum, optionCollectEmailConfigTypes } from '@common/constants/CollectEmailConfigTypeConstant';
import { CollectEmailContentTypeEnum } from '@common/constants/CollectEmailConfigConstant';

interface Props {
  form: UseFormReturn<CollectEmailConfigModel>;
  isViewMode?: boolean;
}

const TypeSession = ({ form, isViewMode }: Props) => {
  const { control, setValue } = form;

  return (
    <Flex direction='column' gap='sm'>
      <form>
        <Controller
          name='type'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanSelect
              required
              disabled={isViewMode}
              label='Alert Type'
              data={optionCollectEmailConfigTypes}
              {...field}
              onChange={(val) => {
                field.onChange(val);
                setValue('content', '{}');
                setValue('contentValue', '');
                if (CollectEmailConfigTypeEnum.EVENT_BASE_ALERT === val) {
                  setValue('absenceInterval', undefined, { shouldValidate: true });
                  setValue('alertRepeatInterval', undefined, { shouldValidate: true });
                  setValue('contentType', CollectEmailContentTypeEnum.SAME_BODY, { shouldValidate: true });
                } else {
                  setValue('absenceInterval', ABSENCE_INTERVAL_MIN, { shouldValidate: true });
                  setValue('alertRepeatInterval', ABSENCE_INTERVAL_MIN, { shouldValidate: true });
                  setValue('contentType', undefined, { shouldValidate: true });
                }
              }}
              error={fieldState.error?.message}
            />
          )}
        />
      </form>
    </Flex>
  );
};

export default TypeSession;
