import { PermissionActionEnum } from './PermissionAction';

export enum PermissionModuleEnum {
  ALERT_GROUP_CONFIG = 'ALERT_GROUP_CONFIG',
  APPLICATION_MANAGEMENT = 'APPLICATION_MANAGEMENT',
  CUSTOM_OBJECT = 'CUSTOM_OBJECT',
  DASHBOARD = 'DASHBOARD',
  DATABASE_COLLECT = 'DATABASE_COLLECT',
  DATABASE_CONNECTION = 'DATABASE_CONNECTION',
  EMAIL_COLLECT = 'EMAIL_COLLECT',
  EMAIL_CONNECTION = 'EMAIL_CONNECTION',
  EMAIL_PARTNER_CONFIG = 'EMAIL_PARTNER_CONFIG',
  EMAIL_TEMPLATE_CONFIG = 'EMAIL_TEMPLATE_CONFIG',
  FILTER_ALERT_CONFIG = 'FILTER_ALERT_CONFIG',
  MAINTENANCE_TIME_CONFIG = 'MAINTENANCE_TIME_CONFIG',
  MODIFY_ALERT_CONFIG = 'MODIFY_ALERT_CONFIG',
  MONITOR_ALERT = 'MONITOR_ALERT',
  PRIORITY_CONFIG = 'PRIORITY_CONFIG',
  REPORT = 'REPORT',
  ROLE_MANAGEMENT = 'ROLE_MANAGEMENT',
  SEND_EMAIL = 'SEND_EMAIL',
  SERVICE_MANAGEMENT = 'SERVICE_MANAGEMENT',
  SUPPER_ADMIN = 'SUPPER_ADMIN',
  TASK = 'TASK',
  TELEGRAM_ALERT_CONFIG = 'TELEGRAM_ALERT_CONFIG',
  UNKNOWN = 'UNKNOWN',
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  WEBHOOK_CONFIG = 'WEBHOOK_CONFIG',
  SYSLOG = 'SYSLOG',
  DATABASE_THRESHOLD_CONFIG = 'DATABASE_THRESHOLD_CONFIG',
  TEAMS_ALERT_CONFIG = 'TEAMS_ALERT_CONFIG',
  EXECUTION = 'EXECUTION',
  EXECUTION_GROUP = 'EXECUTION_GROUP',
  VARIABLE = 'VARIABLE',
  EXECUTION_HISTORY = 'EXECUTION_HISTORY',
  RUN_EXECUTION = 'RUN_EXECUTION',
}

export enum PermissionGroupModuleEnum {
  ALERT_CONFIG = 'Alert Config',
  EVENT = 'Event',
  CUSTOM_OBJECT = 'Custom Object',
  DATABASE_CONFIG = 'Database Config',
  EMAIL_CONFIG = 'Email Config',
  INPUT_DATA_CONFIG = 'Input Data Config',
  USER_MANAGEMENT = 'User Management',
  SYSLOG = 'System Log',
  TEAMS_ALERT_CONFIG = 'Teams Config',
  EXECUTION = 'Execution',
}

export const LIST_MODULE_BY_GROUP: Record<PermissionGroupModuleEnum, PermissionModuleEnum[]> = {
  [PermissionGroupModuleEnum.ALERT_CONFIG]: [
    PermissionModuleEnum.ALERT_GROUP_CONFIG,
    PermissionModuleEnum.SERVICE_MANAGEMENT,
    PermissionModuleEnum.APPLICATION_MANAGEMENT,
    PermissionModuleEnum.REPORT,
    PermissionModuleEnum.PRIORITY_CONFIG,
    PermissionModuleEnum.MONITOR_ALERT,
    PermissionModuleEnum.MAINTENANCE_TIME_CONFIG,
    PermissionModuleEnum.TELEGRAM_ALERT_CONFIG,
    PermissionModuleEnum.FILTER_ALERT_CONFIG,
    PermissionModuleEnum.MODIFY_ALERT_CONFIG,
    PermissionModuleEnum.TEAMS_ALERT_CONFIG,
  ],
  [PermissionGroupModuleEnum.CUSTOM_OBJECT]: [PermissionModuleEnum.CUSTOM_OBJECT],
  [PermissionGroupModuleEnum.DATABASE_CONFIG]: [PermissionModuleEnum.DATABASE_CONNECTION, PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG],
  [PermissionGroupModuleEnum.EMAIL_CONFIG]: [
    PermissionModuleEnum.EMAIL_CONNECTION,
    PermissionModuleEnum.EMAIL_PARTNER_CONFIG,
    PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG,
    PermissionModuleEnum.SEND_EMAIL,
  ],
  [PermissionGroupModuleEnum.INPUT_DATA_CONFIG]: [
    PermissionModuleEnum.WEBHOOK_CONFIG,
    PermissionModuleEnum.DATABASE_COLLECT,
    PermissionModuleEnum.EMAIL_COLLECT,
  ],
  [PermissionGroupModuleEnum.USER_MANAGEMENT]: [PermissionModuleEnum.USER_MANAGEMENT, PermissionModuleEnum.ROLE_MANAGEMENT],
  [PermissionGroupModuleEnum.EVENT]: [PermissionModuleEnum.TASK],
  [PermissionGroupModuleEnum.SYSLOG]: [PermissionModuleEnum.SYSLOG],
  [PermissionGroupModuleEnum.TEAMS_ALERT_CONFIG]: [PermissionModuleEnum.TEAMS_ALERT_CONFIG],
  [PermissionGroupModuleEnum.EXECUTION]: [
    PermissionModuleEnum.EXECUTION,
    PermissionModuleEnum.EXECUTION_GROUP,
    PermissionModuleEnum.VARIABLE,
    PermissionModuleEnum.EXECUTION_HISTORY,
    PermissionModuleEnum.RUN_EXECUTION,
  ],
};

export const PERMISSION_MODULE_LABEL = {
  [PermissionModuleEnum.ALERT_GROUP_CONFIG]: 'Alert group config',
  [PermissionModuleEnum.APPLICATION_MANAGEMENT]: 'Application Management',
  [PermissionModuleEnum.CUSTOM_OBJECT]: 'Custom Object',
  [PermissionModuleEnum.DASHBOARD]: 'Dashboard',
  [PermissionModuleEnum.DATABASE_COLLECT]: 'Database Collection Config',
  [PermissionModuleEnum.DATABASE_CONNECTION]: 'Database Connection Config',
  [PermissionModuleEnum.EMAIL_COLLECT]: 'Email Collection Config',
  [PermissionModuleEnum.EMAIL_CONNECTION]: 'Email Connection',
  [PermissionModuleEnum.EMAIL_PARTNER_CONFIG]: 'Email Partner Config',
  [PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG]: 'Email Template Config',
  [PermissionModuleEnum.FILTER_ALERT_CONFIG]: 'Filter Alert Config',
  [PermissionModuleEnum.MAINTENANCE_TIME_CONFIG]: 'Maintenance Time Config',
  [PermissionModuleEnum.MODIFY_ALERT_CONFIG]: 'Modify Alert Config',
  [PermissionModuleEnum.MONITOR_ALERT]: 'Monitor Alert',
  [PermissionModuleEnum.PRIORITY_CONFIG]: 'Priority Config',
  [PermissionModuleEnum.REPORT]: 'Report',
  [PermissionModuleEnum.ROLE_MANAGEMENT]: 'Role Management',
  [PermissionModuleEnum.SEND_EMAIL]: 'Send Email',
  [PermissionModuleEnum.SERVICE_MANAGEMENT]: 'Service Management',
  [PermissionModuleEnum.SUPPER_ADMIN]: 'SUPPER_ADMIN',
  [PermissionModuleEnum.TASK]: 'Event',
  [PermissionModuleEnum.TELEGRAM_ALERT_CONFIG]: 'Telegram Alert Config',
  [PermissionModuleEnum.UNKNOWN]: 'UNKNOWN',
  [PermissionModuleEnum.USER_MANAGEMENT]: 'User Management',
  [PermissionModuleEnum.WEBHOOK_CONFIG]: 'Config Webhook',
  [PermissionModuleEnum.SYSLOG]: 'System Log',
  [PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG]: 'Database Threshold Config',
  [PermissionModuleEnum.TEAMS_ALERT_CONFIG]: 'Teams Config',
  [PermissionModuleEnum.EXECUTION]: 'Execution',
  [PermissionModuleEnum.EXECUTION_GROUP]: 'Execution Group',
  [PermissionModuleEnum.VARIABLE]: 'Variable',
  [PermissionModuleEnum.RUN_EXECUTION]: 'Run Execution',
  [PermissionModuleEnum.EXECUTION_HISTORY]: 'Execution History',
};

export const LIST_PERMISSION_BY_MODULE = {
  [PermissionModuleEnum.ALERT_GROUP_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.APPLICATION_MANAGEMENT]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
    PermissionActionEnum.EXPORT,
  ],
  [PermissionModuleEnum.CUSTOM_OBJECT]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.DASHBOARD]: [PermissionActionEnum.VIEW, PermissionActionEnum.EDIT, PermissionActionEnum.DELETE, PermissionActionEnum.CREATE],
  [PermissionModuleEnum.DATABASE_COLLECT]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.DATABASE_CONNECTION]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.EMAIL_COLLECT]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.EMAIL_CONNECTION]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.EMAIL_PARTNER_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.FILTER_ALERT_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.MAINTENANCE_TIME_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.MODIFY_ALERT_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.MONITOR_ALERT]: [PermissionActionEnum.VIEW, PermissionActionEnum.ACKNOWLEDGE, PermissionActionEnum.COMMENT],
  [PermissionModuleEnum.PRIORITY_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],

  [PermissionModuleEnum.REPORT]: [PermissionActionEnum.VIEW, PermissionActionEnum.EXPORT],
  [PermissionModuleEnum.ROLE_MANAGEMENT]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.SEND_EMAIL]: [PermissionActionEnum.SEND_EMAIL],
  [PermissionModuleEnum.SERVICE_MANAGEMENT]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
    PermissionActionEnum.EXPORT,
  ],
  [PermissionModuleEnum.SUPPER_ADMIN]: [],
  [PermissionModuleEnum.TASK]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.ASSIGN,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
    PermissionActionEnum.EDIT,
  ],
  [PermissionModuleEnum.TELEGRAM_ALERT_CONFIG]: [PermissionActionEnum.CONFIG],
  [PermissionModuleEnum.UNKNOWN]: [],
  [PermissionModuleEnum.USER_MANAGEMENT]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.WEBHOOK_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.SYSLOG]: [PermissionActionEnum.VIEW, PermissionActionEnum.EXPORT],
  [PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.TEAMS_ALERT_CONFIG]: [PermissionActionEnum.CONFIG, PermissionActionEnum.SEND],
  [PermissionModuleEnum.EXECUTION]: [PermissionActionEnum.VIEW, PermissionActionEnum.EDIT, PermissionActionEnum.DELETE, PermissionActionEnum.CREATE],
  [PermissionModuleEnum.EXECUTION_GROUP]: [
    PermissionActionEnum.VIEW,
    PermissionActionEnum.EDIT,
    PermissionActionEnum.DELETE,
    PermissionActionEnum.CREATE,
  ],
  [PermissionModuleEnum.VARIABLE]: [PermissionActionEnum.VIEW, PermissionActionEnum.EDIT, PermissionActionEnum.DELETE, PermissionActionEnum.CREATE],
  [PermissionModuleEnum.RUN_EXECUTION]: [PermissionActionEnum.EXECUTE],
  [PermissionModuleEnum.EXECUTION_HISTORY]: [PermissionActionEnum.VIEW],
};
