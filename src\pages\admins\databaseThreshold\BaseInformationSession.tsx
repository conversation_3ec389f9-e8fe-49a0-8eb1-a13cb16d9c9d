import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Flex } from '@mantine/core';
import { KanbanInput } from 'kanban-design-system';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { DESCRIPTION_MAX_LENGTH, NAME_MAX_LENGTH, SQL_COMMAND_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { DatabaseThresholdConfigModel } from '@models/DatabaseThresholdConfigModel';
import SQLEditor from './SqlEditor';

interface Props {
  form: UseFormReturn<DatabaseThresholdConfigModel>;
  isViewMode?: boolean;
}

const BaseInformationSession = ({ form, isViewMode }: Props) => {
  const { clearErrors, control, setError } = form;

  return (
    <Flex direction='column' gap='sm'>
      <form>
        <Controller
          name='name'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanInput
              label='Config name'
              disabled={isViewMode}
              description={getMaxLengthMessage(NAME_MAX_LENGTH)}
              maxLength={NAME_MAX_LENGTH}
              required
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Controller
          name='description'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanInput
              disabled={isViewMode}
              label='Description'
              description={getMaxLengthMessage(DESCRIPTION_MAX_LENGTH)}
              maxLength={DESCRIPTION_MAX_LENGTH}
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Controller
          control={control}
          name='sqlCommand'
          render={({ field: { onChange, value }, fieldState }) => (
            <SQLEditor
              value={value}
              required
              disable={isViewMode}
              label='SQL Command'
              error={fieldState.error?.message}
              onChange={onChange}
              onFormatError={(err) => {
                if (err) {
                  setError('sqlCommand', { message: err, type: 'required' });
                } else {
                  clearErrors('sqlCommand');
                }
              }}
              maxLength={SQL_COMMAND_MAX_LENGTH}
            />
          )}
        />
      </form>
    </Flex>
  );
};

export default BaseInformationSession;
