import React from 'react';
import { Notification } from '@core/schema/Notification';
import { NotificationData, notifications } from '@mantine/notifications';
import toast from 'react-hot-toast';
import { ActionIcon, Flex } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import NotificationItem from '@components/Notification/NotificationItem';
import { refetchRequest } from './QueryUtils';
import { NotificationApi } from '@api/NotificationApi';

const DEFAULT_NOTIFICATION_PROPS: Omit<NotificationData, 'message'> = {
  radius: 'md',
  withBorder: true,
  withCloseButton: true,
};

export const NotificationSuccess = (data: NotificationData) => {
  notifications.show({
    color: 'primary',
    bg: 'primary.1',
    title: 'Success',
    ...DEFAULT_NOTIFICATION_PROPS,
    ...data,
  });
};
export const NotificationError = (data: NotificationData) => {
  notifications.show({
    color: 'red',
    bg: 'red.1',
    title: 'Error',
    ...DEFAULT_NOTIFICATION_PROPS,
    ...data,
  });
};
export const NotificationWarning = (data: NotificationData) => {
  notifications.show({
    color: 'yellow',
    bg: 'yellow.1',
    title: 'Warning',
    ...DEFAULT_NOTIFICATION_PROPS,
    ...data,
  });
};

export const showCustomNotification = (notifcation: Notification) => {
  toast.custom((t) => (
    <Flex w={400}>
      <NotificationItem
        notification={notifcation}
        onReadNotificationSuccess={() => {
          refetchRequest(NotificationApi.getUnreadCount());
        }}
        leftAction={
          <ActionIcon
            variant='subtle'
            size='sm'
            onClick={(event) => {
              event.stopPropagation();
              toast.dismiss(t.id);
            }}>
            <IconX size={14} />
          </ActionIcon>
        }
      />
    </Flex>
  ));
};
