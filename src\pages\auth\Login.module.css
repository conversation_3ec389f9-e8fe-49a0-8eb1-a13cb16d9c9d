.container {
  position: relative;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  transition-duration: 1s;
}

.backgroundImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("/public/images/bg.png");
  background-size: cover;
  background-position: center;
  z-index: -1;
}

.content {
  display: flex;
  width: 100%;
  max-width: 65%;
  min-height: 85%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  font-family: Arial, sans-serif;
}

.leftImage {
  position: relative; 
  width: 50%;
  background-image: url('/public/images/bg_login.png');
  background-position: center;
  background-size: contain;      
  background-repeat: no-repeat; 
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.leftSide {
  width: 50%;
  padding: 20px;
}

.rightSide {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px;
  transition: all 0.3s ease;
}

.logoContainer {
  display: flex;
  align-items: center;   
  justify-content: flex-start;  
  margin-bottom: 30px;
}

.logoImg {
  width: 135px;           
  height: 100px;         
  background-image: url('/public/images/logo_mb.png');
  background-size: contain; 
  background-position: center;
  background-repeat: no-repeat;
  margin-right: 20px;
}

.systemName {
  font-size: 2rem;   
  color: var(--mantine-color-indigo-7);       
  font-weight: bold;      
  line-height: 100px;      
  display: inline-block;
  vertical-align: middle;   
}

.inputField {
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  border: 1px solid var(--mantine-color-white-3);
  border-radius: 5px;
  transition: all 0.2s ease;
}

.inputField:focus {
  border-color: var(--mantine-color-indigo-7);
  outline: none;
}

.overlayText {
  position: absolute; 
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--mantine-color-indigo-7);
  font-size: 18px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
  white-space: nowrap;
  font-family: verdana;
  z-index: 10;
}
.box {
  position: absolute;
  top: 10px;
  width: 100%;
  text-align: center; 
}
.checkBox {
   font-size: 35%; 
   width: 65%; 
   margin-bottom: 35px;
}
@media (max-width: 1100px) {
  .content {
    flex-direction: column;
    height: auto;
  }

  .leftImage {
    display: none;
  }

  .rightSide {
    width: 100%;
    padding: 30px;
  }

  .logoContainer {
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .logoImg {
    margin: 0 0 20px 0;
  }

  .systemName {
    font-size: 1.5rem;
    line-height: 1.2;
  }
}

@media (max-width: 480px) {
  .rightSide {
    padding: 20px;
  }
}
