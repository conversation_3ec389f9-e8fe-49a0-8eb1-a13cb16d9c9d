import { BaseURL } from '@common/constants/BaseUrl';
import { AlertPriorityConfigSchema, createResponseSchema } from '@core/schema';
import { AlertPriorityConfigModel } from '@models/AlertPriorityConfigModel';
import { z } from 'zod';
import { createRequest } from './Utils';
import { AlertGroupStatusEnum } from '@common/constants/AlertGroupStatusConstant';
import { AlertPriorityConfigDependenciesSchema } from '@core/schema/AlertPriorityConfigDependencies';
import { PriorityConfigUpdatePositionRequest } from './Type';

export class AlertPriorityConfigApi {
  static findAll(searchParam?: { withDeleted?: boolean; search?: string }) {
    return createRequest({
      url: BaseURL.priority,
      method: 'GET',
      schema: createResponseSchema(z.array(AlertPriorityConfigSchema)),
      params: searchParam,
    });
  }
  static findById(id: number, searchParam?: { withRawValue?: boolean }) {
    return createRequest({
      url: `${BaseURL.priority}/:id`,
      method: 'GET',
      schema: createResponseSchema(AlertPriorityConfigSchema),
      pathVariable: {
        id,
      },
      params: searchParam,
    });
  }
  static save(data: AlertPriorityConfigModel) {
    return createRequest({
      url: BaseURL.priority,
      method: 'POST',
      schema: createResponseSchema(AlertPriorityConfigSchema),
      data,
    });
  }
  static delete(id: number) {
    return createRequest({
      url: `${BaseURL.priority}/:id`,
      method: 'DELETE',
      schema: createResponseSchema(z.string()),
      pathVariable: {
        id,
      },
    });
  }
  static updatePosition(requestBody: PriorityConfigUpdatePositionRequest) {
    return createRequest({
      url: `${BaseURL.priority}/position`,
      method: 'PUT',
      schema: createResponseSchema(z.array(AlertPriorityConfigSchema)),
      data: requestBody,
    });
  }
  static findAllByAlertStatus(alertGroupStatus: AlertGroupStatusEnum) {
    return createRequest({
      url: `${BaseURL.priority}`,
      method: 'GET',
      schema: createResponseSchema(z.array(AlertPriorityConfigSchema)),
      params: {
        alertGroupStatus,
      },
    });
  }
  static findAllDependenciesById(id: number) {
    return createRequest({
      url: `${BaseURL.priority}/:id/dependencies`,
      method: 'GET',
      schema: createResponseSchema(AlertPriorityConfigDependenciesSchema),
      pathVariable: {
        id,
      },
    });
  }
}
