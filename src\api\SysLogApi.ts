import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createCursorPageSchema, createResponseSchema } from '@core/schema';
import { SysLogSchema } from '@core/schema/SysLog';
import { SysLogFilterModel } from '@models/SysLogFilterModel';
import { SysLogCursorSchema } from '@core/schema/SysLogCursor';

export class SysLogApi {
  static findAll(request: Omit<SysLogFilterModel, 'rangeType'>) {
    return createRequest({
      url: `${BaseURL.sysLog}`,
      method: 'GET',
      params: request,
      schema: createResponseSchema(createCursorPageSchema(SysLogSchema, SysLogCursorSchema)),
    });
  }
}
