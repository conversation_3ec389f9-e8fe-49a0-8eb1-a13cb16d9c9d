import { Box, Code, Flex, Stack, Title } from '@mantine/core';
import React from 'react';
import EmptyBox from './EmptyBox';

export const ResultSession = ({ bgColor, color, content, title }: { title: string; content: React.ReactNode; bgColor: string; color: string }) => {
  return (
    <Box style={{ border: '1px solid var(--mantine-color-gray-3)', borderRadius: 'var(--mantine-radius-xs)', overflow: 'hidden', flex: 1 }}>
      <Box bg={bgColor}>
        <Title order={5} c={color} p='xs' bg='gray.3'>
          {title}
        </Title>
      </Box>
      <Box bg='gray.2' p='xs' h='100%' style={{ overflow: 'auto' }}>
        <Code block c='gray.8' bg='transparent'>
          {content}
        </Code>
      </Box>
    </Box>
  );
};

const PythonResult = ({ error, response }: { response?: string; error?: string }) => {
  if (!response && !error) {
    return (
      <Box>
        <Stack gap='xs' flex={1} align='center' justify='center' h='100%'>
          <EmptyBox />
          <Title order={4} c='var(--mantine-color-gray-5)'>
            No Result
          </Title>
        </Stack>
      </Box>
    );
  }
  return (
    <Flex gap='xs' h='100%'>
      {response && <ResultSession title='Result' bgColor='white' color='green.9' content={response} />}
      {error && <ResultSession title='Error' bgColor='white' color='red.9' content={error} />}
    </Flex>
  );
};

export default PythonResult;
