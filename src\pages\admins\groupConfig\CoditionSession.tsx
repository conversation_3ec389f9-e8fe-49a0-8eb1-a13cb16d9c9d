import React, { use<PERSON>emo } from 'react';
import { AlertGroupConfigModel } from '@models/AlertGroupConfigModel';
import { ActionIcon, Box, ComboboxData, Flex, Stack, Title } from '@mantine/core';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST, DEFAULT_GROUP_CONDITION, DEFAULT_GROUP_CONDTION_NAME, DEFAULT_RULE } from './Constants';
import QueryBuilderComponent, { QueryBuilderField } from '@components/queryBuilder';
import { Controller, useFieldArray, UseFormReturn, useWatch } from 'react-hook-form';
import useFetch from '@core/hooks/useFetch';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { ALERT_GROUP_CONFIG_TYPE_LABEL, AlertGroupConfigTypeEnum } from '@common/constants/AlertGroupConfigConstants';
import { KanbanSelect } from 'kanban-design-system';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { RuleGroupType, RuleType } from 'react-querybuilder';
import { QueryRuleGroupTypeModel } from '@models/RuleGroupTypeModel';
import { IconPlus } from '@tabler/icons-react';
import { IconTrash } from '@tabler/icons-react';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import MultipleSelect from '@components/MultipleSelect';

interface Props {
  form: UseFormReturn<AlertGroupConfigModel>;
  isViewMode?: boolean;
}

const ConditionSession = ({ form, isViewMode }: Props) => {
  const { control } = form;
  const { fields, insert, remove, update } = useFieldArray({ control, name: 'ruleGroups' });
  const { data: customObjectData } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), { showLoading: false });
  const { data: priorityData } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });
  const priorityOptions = useMemo<ComboboxData>(
    () => priorityData?.data?.map((priority) => ({ value: String(priority.id), label: priority.name })) || [],
    [priorityData?.data],
  );
  const queryFields = useMemo<QueryBuilderField[]>(() => {
    return [
      {
        name: 'content',
        label: 'Alert Content',
        placeholder: 'Please enter value',
        operators: [QueryBuilderOperatorEnum.CONTAINS, QueryBuilderOperatorEnum.DOES_NOT_CONTAIN],
      },
      {
        name: 'priority',
        label: 'Priority',
        placeholder: 'Please enter value',
        operators: [QueryBuilderOperatorEnum.IS_ONE_OF, QueryBuilderOperatorEnum.IS_NOT_ONE_OF],
        valueEditorType: 'multiselect',
        values: priorityOptions,
      },
      {
        name: 'recipient',
        label: 'Contact',
        placeholder: 'Please enter value',
        operators: [
          QueryBuilderOperatorEnum.IS,
          QueryBuilderOperatorEnum.IS_NOT,
          QueryBuilderOperatorEnum.IS_ONE_OF,
          QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
          QueryBuilderOperatorEnum.CONTAINS,
          QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
        ],
      },
      ...(customObjectData?.data?.content?.map((customObject) => ({
        name: String(customObject.id),
        label: customObject.name,
        placeholder: 'Please enter value',
        operators: Object.values(QueryBuilderOperatorEnum) as QueryBuilderOperatorEnum[],
      })) || []),
    ];
  }, [customObjectData?.data?.content, priorityOptions]);
  const type = useWatch({ control, name: 'type' });

  const customObjectOptions = useMemo<ComboboxData>(
    () => customObjectData?.data?.content?.map((ele) => ({ value: String(ele.id), label: ele.name })) || [],
    [customObjectData],
  );

  return (
    <>
      <Controller
        control={control}
        name='type'
        render={({ field }) => (
          <KanbanSelect
            disabled={isViewMode}
            required
            allowDeselect={false}
            data={Object.values(AlertGroupConfigTypeEnum).map((key) => ({ value: key, label: ALERT_GROUP_CONFIG_TYPE_LABEL[key] }))}
            {...field}
          />
        )}
      />
      {AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE === type ? (
        <Controller
          control={control}
          name='customObjectIds'
          render={({ field }) => (
            <MultipleSelect
              disabled={isViewMode}
              required
              searchable
              data={customObjectOptions}
              placeholder='Search custom object'
              value={field.value}
              onChange={field.onChange}
              showSelectAll={false}
            />
          )}
        />
      ) : (
        <Stack>
          {fields.map((group, index) => (
            <Box key={index} style={{ border: '1px solid var(--mantine-color-default-border)' }} p='md'>
              <Flex justify='space-between' mb='xs'>
                <Title order={6}>{`${DEFAULT_GROUP_CONDTION_NAME} ${index + 1}`}</Title>
                <Flex align='center' gap='md'>
                  <ActionIcon disabled={isViewMode} onClick={() => insert(index + 1, DEFAULT_GROUP_CONDITION)} variant='outline'>
                    <IconPlus />
                  </ActionIcon>
                  {fields.length !== 1 && (
                    <ActionIcon disabled={isViewMode} onClick={() => remove(index)}>
                      <IconTrash />
                    </ActionIcon>
                  )}
                </Flex>
              </Flex>
              <QueryBuilderComponent
                disabled={isViewMode}
                value={group as RuleGroupType}
                onChange={(value) => update(index, value as QueryRuleGroupTypeModel)}
                fields={queryFields}
                baseRule={DEFAULT_RULE as RuleType}
                operators={Object.values(QueryBuilderOperatorEnum)}
              />
            </Box>
          ))}
        </Stack>
      )}
    </>
  );
};

export default ConditionSession;
