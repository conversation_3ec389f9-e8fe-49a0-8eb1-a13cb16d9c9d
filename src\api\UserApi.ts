import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { createPageSchema, createResponseSchema, Page, ResponseData } from '@core/schema';
import { User, UserSchema } from '@core/schema/User';
import { PaginationRequest } from './Type';
import { UserDetail, UserDetailSchema } from '@core/schema/UserDetails';
import { UserModel } from '@models/UserModel';

export class UserApi {
  static me(): RequestConfig<ResponseData<UserDetail>> {
    return {
      url: `${BaseURL.user}/me`,
      method: 'GET',
      schema: createResponseSchema(UserDetailSchema),
    };
  }

  static findWithRolesById(id: number): RequestConfig<ResponseData<UserDetail>> {
    return {
      url: `${BaseURL.user}/${id}/with-roles`,
      method: 'GET',
      schema: createResponseSchema(UserDetailSchema),
    };
  }

  static findAll(pagination?: PaginationRequest): RequestConfig<ResponseData<Page<User>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}`,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(UserSchema)),
      params: pagination,
    };
  }
  static findAllByRoleId(roleId: number, pagination?: PaginationRequest): RequestConfig<ResponseData<Page<User>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}`,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(UserSchema)),
      params: {
        ...pagination,
        roleId,
      } as Record<string, unknown>,
    };
  }

  static save(user: UserModel): RequestConfig<ResponseData<User>> {
    return {
      url: `${BaseURL.user}`,
      method: 'POST',
      schema: createResponseSchema(UserSchema),
      data: user,
    };
  }

  static active(id: number): RequestConfig<ResponseData<User>> {
    return {
      url: `${BaseURL.user}/${id}`,
      method: 'PUT',
      schema: createResponseSchema(UserSchema),
      params: { isActive: true },
    };
  }

  static deActive(id: number): RequestConfig<ResponseData<User>> {
    return {
      url: `${BaseURL.user}/${id}`,
      method: 'PUT',
      schema: createResponseSchema(UserSchema),
      params: { isActive: false },
    };
  }

  static setAdmin(id: number): RequestConfig<ResponseData<User>> {
    return {
      url: `${BaseURL.user}/${id}`,
      method: 'PUT',
      schema: createResponseSchema(UserSchema),
      params: { isAdmin: true },
    };
  }

  static unsetAdmin(id: number): RequestConfig<ResponseData<User>> {
    return {
      url: `${BaseURL.user}/${id}`,
      method: 'PUT',
      schema: createResponseSchema(UserSchema),
      params: { isAdmin: false },
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.user}/${id}`,
      method: 'DELETE',
    };
  }

  static deleteByIdIn(ids: number[]): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.user}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    };
  }
}
