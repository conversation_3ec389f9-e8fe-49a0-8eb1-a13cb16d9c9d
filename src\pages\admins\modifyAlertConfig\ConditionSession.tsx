import React, { useMemo } from 'react';
import { Flex } from '@mantine/core';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST, DEFAULT_RULE } from './Constants';
import QueryBuilderComponent, { QueryBuilderField } from '@components/queryBuilder';
import { Controller, UseFormReturn } from 'react-hook-form';
import useFetch from '@core/hooks/useFetch';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { QueryRuleGroupTypeModel } from '@models/RuleGroupTypeModel';
import { RuleGroupType, RuleType } from 'react-querybuilder';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { ModifyAlertConfigModel } from '@models/ModifyAlertConfigModel';

interface Props {
  form: UseFormReturn<ModifyAlertConfigModel>;
  isViewMode?: boolean;
}

const ConditionSession = ({ form, isViewMode }: Props) => {
  const { control } = form;
  const { data: customObjectData } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), { showLoading: false });

  const queryFields = useMemo<QueryBuilderField[]>(() => {
    return [
      {
        name: 'content',
        label: 'Alert Content',
        placeholder: 'Please enter value',
        operators: [QueryBuilderOperatorEnum.CONTAINS, QueryBuilderOperatorEnum.DOES_NOT_CONTAIN],
      },
      {
        name: 'priority',
        label: 'Priority',
        placeholder: 'Please enter value',
        operators: [
          QueryBuilderOperatorEnum.IS,
          QueryBuilderOperatorEnum.IS_NOT,
          QueryBuilderOperatorEnum.IS_ONE_OF,
          QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
          QueryBuilderOperatorEnum.CONTAINS,
          QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
        ],
      },
      {
        name: 'recipient',
        label: 'Contact',
        placeholder: 'Please enter value',
        operators: [
          QueryBuilderOperatorEnum.IS,
          QueryBuilderOperatorEnum.IS_NOT,
          QueryBuilderOperatorEnum.IS_ONE_OF,
          QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
          QueryBuilderOperatorEnum.CONTAINS,
          QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
        ],
      },
      ...(customObjectData?.data?.content?.map((customObject) => ({
        name: String(customObject.id),
        label: customObject.name,
        placeholder: 'Please enter value',
        operators: Object.values(QueryBuilderOperatorEnum) as QueryBuilderOperatorEnum[],
      })) || []),
    ];
  }, [customObjectData?.data?.content]);

  return (
    <Flex direction='column' gap='sm'>
      <Controller
        name='ruleGroup'
        control={control}
        render={({ field }) => (
          <QueryBuilderComponent
            disabled={isViewMode}
            value={field.value as RuleGroupType}
            onChange={(val) => {
              field.onChange(val as QueryRuleGroupTypeModel);
            }}
            fields={queryFields}
            baseRule={DEFAULT_RULE as RuleType}
            operators={Object.values(QueryBuilderOperatorEnum)}
          />
        )}
      />
    </Flex>
  );
};

export default ConditionSession;
