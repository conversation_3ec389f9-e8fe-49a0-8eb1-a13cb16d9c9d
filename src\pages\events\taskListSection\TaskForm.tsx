import { Box, ComboboxData, Flex } from '@mantine/core';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import React from 'react';
import { DateTimePicker } from '@mantine/dates';
import { Controller, UseFormReturn } from 'react-hook-form';
import { TaskModel } from '@models/TaskModel';
import { TaskTimeTypeEnum, TaskTimeTypeLabel } from '@common/constants/TaskConstants';
import dayjs from 'dayjs';
import { MAX_DESCRIPTION_LENGTH, MAX_NAME_LENGTH } from '@common/constants/ValidationConstant';

interface Props {
  form: UseFormReturn<TaskModel>;
}

const TaskTypeOptions: ComboboxData = Object.keys(TaskTimeTypeEnum).map((key) => ({ value: key, label: TaskTimeTypeLabel[key as TaskTimeTypeEnum] }));
const TaskForm = ({ form }: Props) => {
  const { control, formState, register, trigger, watch } = form;
  const timeType = watch('timeType');
  const startTime = watch('startTime');
  return (
    <Box>
      <KanbanInput label='Name' placeholder='Name' {...register('name')} error={formState.errors.name?.message} maxLength={MAX_NAME_LENGTH} />
      <KanbanInput
        label='Description'
        placeholder='Description'
        {...register('description')}
        error={formState.errors.description?.message}
        maxLength={MAX_DESCRIPTION_LENGTH}
      />
      <Flex justify='center' gap='sm'>
        <Controller
          name='timeType'
          control={control}
          render={({ field }) => (
            <KanbanSelect label='Time type' data={TaskTypeOptions} allowDeselect={false} {...field} error={formState.errors.timeType?.message} />
          )}
        />
        <Controller
          name='startTime'
          control={control}
          render={({ field, fieldState }) => {
            const day = dayjs(field.value);
            return (
              <DateTimePicker
                value={day.isValid() ? day.toDate() : new Date()}
                onChange={(value) => {
                  const dateValue = dayjs(value);
                  field.onChange(dateValue.isValid() ? dateValue.format() : '');
                  if (timeType === TaskTimeTypeEnum.FROM_TIME_TO_TIME) {
                    trigger('endTime');
                  }
                }}
                placeholder='Pick date and time'
                style={{ flex: 1 }}
                label='Start time'
                error={fieldState?.error?.message}
                minDate={new Date()}
                required
              />
            );
          }}
        />
        {TaskTimeTypeEnum.FROM_TIME_TO_TIME === timeType && (
          <Controller
            name='endTime'
            control={control}
            render={({ field, fieldState }) => {
              const day = dayjs(field.value);
              return (
                <DateTimePicker
                  value={day.isValid() ? day.toDate() : new Date()}
                  onChange={(value) => {
                    const dateValue = dayjs(value);
                    field.onChange(dateValue.isValid() ? dateValue.format() : '');
                    if (timeType === TaskTimeTypeEnum.FROM_TIME_TO_TIME) {
                      trigger('startTime');
                    }
                  }}
                  placeholder='Pick date and time'
                  style={{ flex: 1 }}
                  label='End time'
                  error={fieldState?.error?.message}
                  minDate={startTime ? dayjs(startTime).toDate() : new Date()}
                  required
                />
              );
            }}
          />
        )}
      </Flex>
    </Box>
  );
};

export default TaskForm;
