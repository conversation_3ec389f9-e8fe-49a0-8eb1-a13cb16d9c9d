import React from 'react';
import { KanbanButton } from 'kanban-design-system';
import { CustomObjectApi } from '@api/CustomObjectApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { areAllArraysEmpty } from '@common/utils/StringUtils';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

type DeleteModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  customObjectId?: number;
  customObjectName?: string;
};

const DeleteModal: React.FC<DeleteModalProps> = ({ customObjectId, customObjectName, onClose, opened, refetchList }) => {
  const { data: dependencies } = useFetch(CustomObjectApi.findAllDependenciesById(customObjectId || 0), {
    enabled: !!customObjectId && opened,
  });
  const dependencyConfig: DependencyItem[] = [
    {
      dependencyEntity: 'collect email configs',
      dependencies: dependencies?.data?.collectEmailConfigs ?? [],
    },
    {
      dependencyEntity: 'alert group configs',
      dependencies: dependencies?.data?.alertGroupConfigs ?? [],
    },
    {
      dependencyEntity: 'maintenance time configs',
      dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
    },
    {
      dependencyEntity: 'filter alert configs',
      dependencies: dependencies?.data?.filterAlertConfigs ?? [],
    },
    {
      dependencyEntity: 'modify alert configs',
      dependencies: dependencies?.data?.modifyAlertConfigs ?? [],
    },
  ];
  const { mutate: deleteByIdMutate } = useMutate(CustomObjectApi.deleteById, {
    successNotification: { message: 'Deleted successfully!' },
    onSuccess: () => {
      refetchList();
      onClose();
    },
  });
  const isDisabledButtonConfirm = !areAllArraysEmpty(dependencies?.data || {});
  return (
    <Modal
      size='xl'
      opened={opened}
      onClose={() => {
        onClose();
      }}
      title={'Delete custom object'}
      actions={
        <KanbanButton onClick={() => deleteByIdMutate(customObjectId ?? 0)} disabled={isDisabledButtonConfirm}>
          Confirm
        </KanbanButton>
      }>
      <DependenciesWarningAlert mainEntity={`Custom object ${customObjectName}`} dependencyConfigs={dependencyConfig} isDeleted={true} />
      {!isDisabledButtonConfirm && <DeleteConfirmMessage name={customObjectName} />}
    </Modal>
  );
};

export default DeleteModal;
