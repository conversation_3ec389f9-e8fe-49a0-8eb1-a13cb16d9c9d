import { BaseURL } from '@common/constants/BaseUrl';
import { PaginationRequest } from './Type';
import { createPageSchema, createResponseSchema } from '@core/schema';
import { BaseDatabaseThresholdConfigSchema, DatabaseThresholdConfigSchema } from '@core/schema/DatabaseThresholdConfig';
import { DatabaseThresholdConfigModel } from '@models/DatabaseThresholdConfigModel';
import { createRequest } from './Utils';

export class DatabaseThresholdConfigApi {
  static findAll(pagination: PaginationRequest) {
    return createRequest({
      url: BaseURL.databaseThreshold,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(BaseDatabaseThresholdConfigSchema)),
    });
  }

  static findById(id: string) {
    return createRequest({
      url: `${BaseURL.databaseThreshold}/:id`,
      method: 'GET',
      schema: createResponseSchema(DatabaseThresholdConfigSchema),
      pathVariable: {
        id,
      },
    });
  }
  static save(body: DatabaseThresholdConfigModel) {
    return createRequest({
      url: BaseURL.databaseThreshold,
      method: 'POST',
      data: body,
    });
  }

  static deleteById(id: string) {
    return createRequest({
      url: `${BaseURL.databaseThreshold}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    });
  }
  static activeOrInactive(id: string) {
    return createRequest({
      url: `${BaseURL.databaseThreshold}/:id/toggle-status`,
      method: 'PUT',
      pathVariable: {
        id,
      },
    });
  }
}
