import useFetch from '@core/hooks/useFetch';
import { ActionIcon, Card, Group, Radio, Stack } from '@mantine/core';
import { IconClock, IconDeviceFloppy } from '@tabler/icons-react';
import { KanbanButton, KanbanInput, KanbanNumberInput, KanbanText } from 'kanban-design-system';
import React, { useCallback, useEffect, useRef } from 'react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { TeamsAlertConfigApi } from '@api/TeamsAlertConfigApi';
import useMutate from '@core/hooks/useMutate';
import {
  TEAMS_CLIENT_ID_MAX_LENGTH,
  TEAMS_TENANT_ID_MAX_LENGTH,
  TEAMS_CLIENT_SECRET_MAX_LENGTH,
  TEAMS_EMAIL_MAX_LENGTH,
  TEAMS_PASSWORD_MAX_LENGTH,
  TEAMS_INTERVAL_MAX_LENGTH,
} from '@common/constants/ValidationConstant';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TimeInput } from '@mantine/dates';
import { TeamsModel, TeamsModelSchema } from '@models/TeamsModel';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { TeamsIntervalTypeEnum } from '@common/constants/TeamsConstants';
import TeamsEditor from '@components/editor/TeamsEditor';

const defaultValue = {
  messageTemplate: '',
  interval: '',
  intervalType: TeamsIntervalTypeEnum.ONCE_DAILY_AT_TIME,
};

export const TeamsPage = () => {
  const { data: teamsConfig, refetch: teamsConfigRefetchList } = useFetch(TeamsAlertConfigApi.findConfig(), {
    placeholderData: (prev) => prev,
  });

  const {
    control,
    formState: { isValid },
    getValues,
    handleSubmit,
    reset,
    setValue,
  } = useForm<TeamsModel>({
    resolver: zodResolver(TeamsModelSchema),
    mode: 'onChange',
    defaultValues: defaultValue,
  });

  useEffect(() => {
    if (teamsConfig?.data) {
      reset({
        id: teamsConfig?.data?.id,
        messageTemplate: teamsConfig?.data.messageTemplate,
        interval: teamsConfig?.data.interval,
        intervalType: teamsConfig?.data.intervalType,
      });
    }
  }, [teamsConfig, reset]);

  const { mutate: saveMutate } = useMutate(TeamsAlertConfigApi.saveConfig, {
    successNotification: { message: 'Save config successfully!' },
    onSuccess: () => {
      teamsConfigRefetchList();
    },
  });

  const { mutate: triggerJobMutate } = useMutate(TeamsAlertConfigApi.triggerJob, {
    successNotification: { message: 'Trigger job successfully!' },
  });

  const onSubmit = useCallback(
    (data: TeamsModel) => {
      if (isValid) {
        const validatedData = TeamsModelSchema.parse(data);
        saveMutate(validatedData);
      }
    },
    [isValid, saveMutate],
  );

  const timeRef = useRef<HTMLInputElement>(null);
  const pickerControl = (
    <ActionIcon variant='subtle' color='gray' onClick={() => timeRef.current?.showPicker()}>
      <IconClock size={16} stroke={1.5} />
    </ActionIcon>
  );

  return (
    <>
      <HeaderTitleComponent
        title='Config Teams'
        rightSection={
          <KanbanButton size='xs' onClick={handleSubmit(onSubmit)} disabled={!isValid} leftSection={<IconDeviceFloppy />}>
            Save Config
          </KanbanButton>
        }
      />
      <Card shadow='sm' radius='md' m={5} withBorder>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Card shadow='sm' radius='md' m={5} withBorder>
            <Controller
              name='clientId'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <KanbanInput
                  label='Client ID'
                  description={getMaxLengthMessage(TEAMS_CLIENT_ID_MAX_LENGTH)}
                  maxLength={TEAMS_CLIENT_ID_MAX_LENGTH}
                  placeholder={teamsConfig?.data?.clientId}
                  required
                  {...field}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name='tenantId'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <KanbanInput
                  label='Tenant ID'
                  description={getMaxLengthMessage(TEAMS_TENANT_ID_MAX_LENGTH)}
                  maxLength={TEAMS_TENANT_ID_MAX_LENGTH}
                  placeholder={teamsConfig?.data?.tenantId}
                  required
                  {...field}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name='clientSecret'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <KanbanInput
                  label='Client Secret'
                  description={getMaxLengthMessage(TEAMS_CLIENT_SECRET_MAX_LENGTH)}
                  maxLength={TEAMS_CLIENT_SECRET_MAX_LENGTH}
                  required
                  placeholder={teamsConfig?.data?.clientSecretPlaceholder}
                  {...field}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name='username'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <KanbanInput
                  label='UserName'
                  type='email'
                  placeholder={teamsConfig?.data?.username}
                  description={getMaxLengthMessage(TEAMS_EMAIL_MAX_LENGTH)}
                  maxLength={TEAMS_EMAIL_MAX_LENGTH}
                  required
                  {...field}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name='password'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <KanbanInput
                  label='Password'
                  type='password'
                  description={getMaxLengthMessage(TEAMS_PASSWORD_MAX_LENGTH)}
                  maxLength={TEAMS_PASSWORD_MAX_LENGTH}
                  placeholder={teamsConfig?.data ? '********' : ''}
                  required
                  {...field}
                  error={error?.message}
                />
              )}
            />
          </Card>
          <Card shadow='sm' radius='md' m={5} withBorder>
            <Controller
              name='messageTemplate'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <TeamsEditor
                  label='Message Template'
                  value={teamsConfig?.data?.messageTemplate}
                  onChange={(value) => {
                    field.onChange(value);
                  }}
                  error={error?.message}
                  height='350px'
                />
              )}
            />
          </Card>
          <Card shadow='sm' radius='md' m={5} withBorder>
            <Group>
              <KanbanText>Sync Microsoft Teams Data</KanbanText>
              <KanbanButton onClick={triggerJobMutate}>Sync now</KanbanButton>
            </Group>
            <Stack align='stretch' justify='center' pt={5}>
              <Group>
                <Controller
                  name='intervalType'
                  control={control}
                  render={({ field }) => (
                    <Radio.Group
                      value={field.value}
                      onChange={(value: string) => {
                        const typedValue = value as TeamsIntervalTypeEnum;
                        field.onChange(typedValue);
                        // Reset interval value when changing types
                        setValue('interval', '', { shouldValidate: true });
                      }}
                      name='intervalType'
                      withAsterisk>
                      <Stack>
                        <Group>
                          <Radio value={TeamsIntervalTypeEnum.ONCE_DAILY_AT_TIME} label='Sync once time at' />
                          {field.value === TeamsIntervalTypeEnum.ONCE_DAILY_AT_TIME && (
                            <TimeInput
                              ref={timeRef}
                              rightSection={pickerControl}
                              withAsterisk
                              value={getValues('interval')}
                              onChange={(event) => {
                                const data = event.currentTarget.value;
                                setValue('interval', data, { shouldValidate: true });
                              }}
                            />
                          )}
                        </Group>
                        <Group>
                          <Radio value={TeamsIntervalTypeEnum.EVERY_X_MINUTES} label='Sync every' />
                          {field.value === TeamsIntervalTypeEnum.EVERY_X_MINUTES && (
                            <>
                              <Controller
                                name='interval'
                                control={control}
                                render={({ field: intervalField }) => (
                                  <KanbanNumberInput
                                    min={1}
                                    max={TEAMS_INTERVAL_MAX_LENGTH}
                                    size='xs'
                                    pt={6}
                                    w={50}
                                    value={intervalField.value ? Number(intervalField.value) : undefined}
                                    onChange={(val) => {
                                      intervalField.onChange(val?.toString() || '');
                                    }}
                                  />
                                )}
                              />
                              <KanbanText>minute(s)</KanbanText>
                            </>
                          )}
                        </Group>
                      </Stack>
                    </Radio.Group>
                  )}
                />
              </Group>
            </Stack>
          </Card>
        </form>
      </Card>
    </>
  );
};

export default TeamsPage;
