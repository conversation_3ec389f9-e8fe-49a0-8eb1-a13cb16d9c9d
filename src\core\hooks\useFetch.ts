import { callRequest, RequestConfig } from '../api/BaseApi';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { AppConfig } from './AppConfigTypes';
import { getQueryKey } from '@common/utils/QueryUtils';
import { defaultErrorNotification } from './Utils';
import useConfigNotification from './useConfigNotification';

type FetchAppConfig<TQueryFnData, TData = TQueryFnData> = Omit<AppConfig<TQueryFnData>, 'successNotification'> &
  Omit<UseQueryOptions<TQueryFnData, Error, TData>, 'queryKey' | 'queryFn'>;

function useFetch<TQueryFnData, TData = TQueryFnData>(requestConfig: RequestConfig<TQueryFnData>, appConfig?: FetchAppConfig<TQueryFnData, TData>) {
  const { errorNotification = defaultErrorNotification, showLoading, throwParsedError = true, withSignal = true, ...otherConfig } = appConfig || {};
  const queryResult = useQuery<TQueryFnData, Error, TData>({
    queryKey: getQueryKey(requestConfig),
    queryFn: ({ signal }) => callRequest({ ...requestConfig, signal: withSignal ? signal : undefined }, { showLoading, throwParsedError }),
    ...otherConfig,
  });
  useConfigNotification(queryResult, { errorNotification });
  return queryResult;
}

export default useFetch;
