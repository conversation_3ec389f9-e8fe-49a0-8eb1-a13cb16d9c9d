import { isEmpty, isNaN, parseInt } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { AlertGroupConfigModel, AlertGroupConfigModelSchema } from '@models/AlertGroupConfigModel';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';
import { KanbanButton, KanbanInput } from 'kanban-design-system';
import classes from './GroupConfigStyle.module.css';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { AlertGroupConfigApi } from '@api/AlertGroupConfigApi';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import ConditionSession from './CoditionSession';
import { AlertGroupConfigAction, DEFAULT_FORM_VALUE, DEFAULT_GROUP_CONDITION } from './Constants';
import ReferenceSession from './ReferenceSession';
import { MAX_ALERT_GROUP_CONFIG_DESCRIPTION_LENGTH, MAX_ALERT_GROUP_CONFIG_NAME_LENGTH } from '@common/constants/ValidationConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import AlertOutput from './AlertOutput';

const SaveButton = ({ form }: { form: UseFormReturn<AlertGroupConfigModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveGroupConfigMutate } = useMutate(AlertGroupConfigApi.save, {
    onSuccess: () => {
      navigate('../');
    },
  });
  const { formState } = form;
  const onSubmit = useCallback(() => {
    const parsedData = AlertGroupConfigModelSchema.safeParse(form.getValues());
    if (parsedData.success) {
      const data = parsedData.data;
      saveGroupConfigMutate({ ...data, serviceIds: data.services.map((ele) => ele.id), applicationIds: data.applications.map((ele) => ele.id) });
    }
  }, [form, saveGroupConfigMutate]);
  return (
    <GuardComponent requirePermissions={[AclPermission.alertGroupConfigEdit, AclPermission.alertGroupConfigCreate]}>
      <KanbanButton onClick={onSubmit} disabled={!formState.isValid}>
        Save
      </KanbanButton>
    </GuardComponent>
  );
};

const GroupConfigFormPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { groupConfigId: groupCofigStringId } = useParams();
  const groupConfigId = !!groupCofigStringId && !isNaN(groupCofigStringId) ? parseInt(groupCofigStringId) : 0;
  const isCreateMode = !groupConfigId;
  const [currentTab, setCurrentTab] = useState<string>(AlertGroupConfigAction.CREATE);
  const isViewMode = currentTab === AlertGroupConfigAction.VIEW;

  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);

  const form = useForm<AlertGroupConfigModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(AlertGroupConfigModelSchema),
    mode: 'onChange',
  });

  const { data: alertGroupConfigData } = useFetch(AlertGroupConfigApi.findById(groupConfigId), { enabled: !isCreateMode });

  useEffect(() => {
    if (!isCreateMode && alertGroupConfigData?.data) {
      const { data } = alertGroupConfigData;
      form.reset({
        ...data,
        customObjectIds: data?.customObjectIds?.map((ele) => String(ele)) || [],
        ruleGroups: isEmpty(data?.ruleGroups) ? [DEFAULT_GROUP_CONDITION] : data?.ruleGroups,
        services: data?.services || [],
        applications: data?.applications || [],
        customPriorityConfigId: data?.customPriorityConfigId ? `${data?.customPriorityConfigId}` : undefined,
        customApplicationId: data?.customApplication?.id,
        customServiceId: data?.customService?.id,
      });
    }
  }, [alertGroupConfigData, alertGroupConfigData?.data, form, isCreateMode]);

  return (
    <Box>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>{isViewMode ? 'View alert group config' : isCreateMode ? 'Create alert group config' : 'Update alert group config'}</Title>
        <Flex>
          <Group>
            <KanbanButton variant='outline' onClick={() => navigate('../')}>
              Cancel
            </KanbanButton>
            {!isViewMode && <SaveButton form={form} />}
          </Group>
        </Flex>
      </Flex>
      <Stack gap='md'>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>General Information</Title>
          <KanbanInput disabled={isViewMode} label='Config name' required {...form.register('name')} maxLength={MAX_ALERT_GROUP_CONFIG_NAME_LENGTH} />
          <KanbanInput
            disabled={isViewMode}
            label='Description'
            {...form.register('description')}
            maxLength={MAX_ALERT_GROUP_CONFIG_DESCRIPTION_LENGTH}
          />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Reference</Title>
          <ReferenceSession isViewMode={isViewMode} form={form} />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Condition</Title>
          <ConditionSession isViewMode={isViewMode} form={form} />
        </Stack>

        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Alert output</Title>
          <AlertOutput disabled={isViewMode} form={form} groupConfig={alertGroupConfigData?.data} />
        </Stack>
      </Stack>
    </Box>
  );
};

export default GroupConfigFormPage;
