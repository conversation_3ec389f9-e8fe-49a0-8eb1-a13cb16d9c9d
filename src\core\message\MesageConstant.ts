// For export file message
export const FILE_NAME_EXPORT_REGEX_MESSAGE_ERROR = 'Input can only contain A-Z, a-z, 0-9, . -  _';

//For create/update application and service message
export const SERVICE_SELECT_MESSAGE_ERROR = 'Please select one service';
export const REGEX_INPUT_MESSAGE_ERROR = 'Input can only contain A-Z, a-z, 0-9, . -  _';
export const CUSTOM_OBJECT_REGEX_INPUT_MESSAGE_ERROR = 'Invalid regex pattern';
export const CUSTOM_OBJECT_INDEX_TO_INDEX_INPUT_MESSAGE_ERROR = 'From index must be less than To index';
export const CUSTOM_OBJECT_KEYWORD_TO_KEYWORD_INPUT_MESSAGE_ERROR = 'From keyword and To keyword are required';
export const CUSTOM_OBJECT_MATCH_REGEX_INPUT_MESSAGE_ERROR = 'Regex are required';
export const DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR = 'Input can only contain A-Z, a-z, 0-9, _';
export const TELEGRAM_GROUP_ID_REGEX_INPUT_MESSAGE_ERROR = `The first character must be '-' and the rest must be digits (0-9) only.`;
export const REGEX_USER_NAME_ERROR = 'UserName is invalid.!';
