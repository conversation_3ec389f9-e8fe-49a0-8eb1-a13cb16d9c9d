*{
  box-sizing: border-box;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

a{
  text-decoration: none;
}

/* Firefox */
/* * {
  scrollbar-width: thin;
  scrollbar-color: #979FB0 #F7F9FF;
} */

/* Chrome, Edge and Safari */
*::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}
*::-webkit-scrollbar-track {
  border-radius: 5px;
  background-color: #F7F9FF;
}

*::-webkit-scrollbar-track:hover {
  background-color: #F7F9FF;
}

*::-webkit-scrollbar-track:active {
  background-color: #F7F9FF;
}

*::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: #b9c3d87e;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: #7C85B0;
}

*::-webkit-scrollbar-thumb:active {
  background-color: #7C85B0;
}