import { CustomObjectSearchRequest } from '@api/Type';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { QueryBuilderCombinatorEnum } from '@components/queryBuilder/QueryBuilderCombinatorEnum';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { QueryRuleGroupType, QueryRuleType } from '@core/schema/RuleGroupCondition';
import { ApplicationPaginationRequest } from '@models/ApplicationModel';
import { ServicePaginationRequest } from '@models/ServiceModel';
import { SortType } from '@common/constants/SortType';
import { MaintenanceTimeConfigModel } from '@models/MaintenanceTimeConfigModel';
import { MaintenanceTimeTypeEnum, TimeUnitEnum } from '@common/constants/MaintenanceTimeConfigConstants';
import dayjs from 'dayjs';

export enum MaintenanceTimeConfigAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  VIEW = 'VIEW',
}

export const DEFAULT_RULE: QueryRuleType = { field: 'content', operator: QueryBuilderOperatorEnum.CONTAINS, value: '' };

export const DEFAULT_GROUP_CONDITION: QueryRuleGroupType = {
  combinator: QueryBuilderCombinatorEnum.AND,
  rules: [DEFAULT_RULE],
};

export const DEFAULT_FORM_VALUE: MaintenanceTimeConfigModel = {
  name: '',
  type: MaintenanceTimeTypeEnum.NEXT_TIME,
  ruleGroup: DEFAULT_GROUP_CONDITION,
  cronExpression: '* * * * *',
  unit: TimeUnitEnum.DAY,
  startTime: dayjs().format(),
  endTime: dayjs().format(),
  applications: [],
  services: [],
};

export const DEFAULT_APPLICATION_PAGINATION_REQUEST: ApplicationPaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  name: '',
  withDeleted: false,
};

export const DEFAULT_SERVICE_PAGINATION_REQUEST: ServicePaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  withDeleted: false,
};

export const DEFAULT_GROUP_CONDTION_NAME = 'Group Condition';

export const DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST: CustomObjectSearchRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  size: 10000,
  withDeleted: false,
};
