import React, { useContext } from 'react';
import { Button, Flex, Stack } from '@mantine/core';
import { KanbanInput } from 'kanban-design-system';
import AppServiceFilter from './AppServiceFilter';
import PriorityFilter from './PriorityFilter';
import { MonitorAlertPageContext } from '../MonitorAlertPageContext';
import { FilterForm } from '../Types';
import { useForm } from 'react-hook-form';
import { SEARCH_INPUT_MAX_LENGTH } from '../Constants';

const Filter = () => {
  const { filter, onFilterReset, onFilterSubmit } = useContext(MonitorAlertPageContext);
  const form = useForm<FilterForm>({
    defaultValues: filter,
  });

  return (
    <form onSubmit={form.handleSubmit(onFilterSubmit)}>
      <Stack>
        <AppServiceFilter form={form} />
        <KanbanInput placeholder='Search contact' flex={1} label='Contact' {...form.register('recipient')} maxLength={SEARCH_INPUT_MAX_LENGTH} />
        <PriorityFilter form={form} />
        <KanbanInput placeholder='Alert content' flex={1} label='Search content' {...form.register('content')} maxLength={SEARCH_INPUT_MAX_LENGTH} />
        <Flex justify='flex-start' align='center' gap={5} style={{ alignSelf: 'flex-end' }}>
          <Button type='submit'>Apply</Button>
          <Button type='button' onClick={onFilterReset} variant='outline'>
            Reset
          </Button>
        </Flex>
      </Stack>
    </form>
  );
};

export default Filter;
