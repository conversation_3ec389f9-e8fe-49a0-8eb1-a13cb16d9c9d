export const LocalStorageKey = {
  TIME_INTERVAL_REFRESH_KEY: 'TIME_INTERVAL_REFRESH',
  EVENT_FILTER_KEY: 'EVENT_FILTER',
  MONITOR_ALERT_NAVBAR_TREE: 'MONITOR_ALERT_NAVBAR_TREE',
  MONITOR_ALERT_NAVBAR_FILTER: 'MONITOR_ALERT_NAVBAR_FILTER',
  MONITOR_ALERT_NAVBAR_TAB: 'MONITOR_ALERT_NAVBAR_TAB',
  MONITOR_ALERT_NAVBAR_SEARCH_PARAM: 'MONITOR_ALERT_NAVBAR_SEARCH_PARAM',
  MONITOR_ACCESS_TOKEN: 'MONITOR_ACCESS_TOKEN',
  MONITOR_REFRESH_TOKEN: 'MONITOR_REFRESH_TOKEN',
  MONITOR_ACCESS_TOKEN_EXPRIRE_AT: 'MONITOR_ACCESS_TOKEN_EXPRIRE_AT',
} as const;

export const CLEAR_WHEN_LOGOUT_KEYS: (typeof LocalStorageKey)[keyof typeof LocalStorageKey][] = [
  LocalStorageKey.TIME_INTERVAL_REFRESH_KEY,
  LocalStorageKey.EVENT_FILTER_KEY,
  LocalStorageKey.MONITOR_ALERT_NAVBAR_TREE,
  LocalStorageKey.MONITOR_ALERT_NAVBAR_FILTER,
  LocalStorageKey.MONITOR_ALERT_NAVBAR_TAB,
  LocalStorageKey.MONITOR_ALERT_NAVBAR_SEARCH_PARAM,
  LocalStorageKey.MONITOR_ACCESS_TOKEN,
  LocalStorageKey.MONITOR_REFRESH_TOKEN,
  LocalStorageKey.MONITOR_ACCESS_TOKEN_EXPRIRE_AT,
];
