import { z } from 'zod';
import { PaginationModelSchema } from './PaginationModel';
import { VARIABLE_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';

export const ExecuteScriptModelSchema = z.object({
  executionId: z.string(),
  variables: z.array(
    z.object({
      id: z.string().optional(),
      name: z.string(),
      value: z.string().min(1).max(VARIABLE_MAX_LENGTH),
    }),
  ),
  paginationRequest: PaginationModelSchema.optional(),
  name: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(ExecutionTypeEnum),
  databaseConnectionId: z.number().optional(),
  executionGroupId: z.string(),
  script: z.string(),
});

export type ExecuteScriptModel = z.infer<typeof ExecuteScriptModelSchema>;
