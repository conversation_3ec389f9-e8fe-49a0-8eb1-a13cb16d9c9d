import React, { useMemo } from 'react';
import DateRange from '@components/dateRange';
import { UseFormReturn, useWatch } from 'react-hook-form';
import { SysLogFilterModel } from '@models/SysLogFilterModel';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { DateRangeTypeEnum } from '@components/dateRange/Constants';

interface Props {
  form: UseFormReturn<SysLogFilterModel>;
}

const SysLogDateRangeFilter = ({ form }: Props) => {
  const { control, formState, reset, trigger } = form;
  const fromDate = useWatch({ control, name: 'fromDate' });
  const toDate = useWatch({ control, name: 'toDate' });
  const rangeType = useWatch({ control, name: 'rangeType' });
  const fromDateValue = useMemo(() => {
    const temp = dayjs(fromDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS);
    return temp.isValid() ? temp : undefined;
  }, [fromDate]);
  const toDateValue = useMemo(() => {
    const temp = dayjs(toDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS);
    return temp.isValid() ? temp : undefined;
  }, [toDate]);
  const { errors } = formState;
  return (
    <DateRange
      fromDate={fromDateValue}
      toDate={toDateValue}
      onChange={(fromDate, toDate, rangeType) => {
        let from = fromDate || dayjs().startOf('date');
        let to = toDate || dayjs().endOf('date');
        if (DateRangeTypeEnum.BEFORE_SPECIFIC_DATE === rangeType) {
          from = to.clone().add(-1, 'year');
        } else if (DateRangeTypeEnum.SINCE_SPECIFIC_DATE === rangeType) {
          to = from.clone().add(1, 'year');
        }
        reset((formValue) => ({
          ...formValue,
          fromDate: from.format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
          toDate: to.format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
          rangeType,
        }));
        trigger();
      }}
      rangeType={rangeType}
      error={errors.fromDate?.message}
    />
  );
};

export default SysLogDateRangeFilter;
