// TeamsSendMessage.module.scss
.container {
  margin-bottom: 16px;
}

.tagsInputWrapper {
  display: flex;
  flex-direction: column;
}

.tagsInput {
  min-height: 32px;
  height: auto;
  max-height: 96px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
}

.errorMessage {
  color: var(--mantine-color-black);
  font-size: 0.75rem;
  margin-top: 4px;
}

.textareaContainer {
  margin-top: 12px;
}
