import { createPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { PaginationRequest } from './Type';
import { TelegramAlertConfig, TelegramAlertConfigSchema, TelegramConfigSchema } from '@core/schema/Telegram';
import { TelegramModel } from '@models/TelegramModel';

export class TelegramAlertConfigApi {
  static findAllServices(pagination: PaginationRequest) {
    return createRequest({
      url: `${BaseURL.telegramAlert}/services`,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(TelegramAlertConfigSchema)),
    });
  }

  static findAllApplications(serviceId: string, pagination: PaginationRequest) {
    return createRequest({
      url: `${BaseURL.telegramAlert}/services/${serviceId}/applications`,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(TelegramAlertConfigSchema)),
    });
  }

  static findConfig() {
    return createRequest({
      url: `${BaseURL.telegramAlert}`,
      method: 'GET',
      schema: createResponseSchema(TelegramConfigSchema),
    });
  }

  static saveConfig(config: { config: TelegramModel; alertsConfig: TelegramAlertConfig[] }) {
    return createRequest({
      url: `${BaseURL.telegramAlert}`,
      method: 'POST',
      data: config,
    });
  }
}
