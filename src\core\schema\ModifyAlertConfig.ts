import { z } from 'zod';
import { QueryRuleGroupTypeSchema } from './RuleGroupCondition';
import { ServiceSchema } from './Service';
import { ApplicationSchema } from './Application';

export const ModifyAlertConfigModifyResponseSchema = z.object({
  fieldName: z.string(),
  fieldValue: z.string(),
  contentHtml: z.string().optional(),
});

export const BaseModifyAlertConfigSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  active: z.boolean(),
  position: z.number(),
  modifies: z.array(ModifyAlertConfigModifyResponseSchema),
  ruleGroup: QueryRuleGroupTypeSchema,
});

export type BaseModifyAlertConfig = z.infer<typeof BaseModifyAlertConfigSchema>;
export const ModifyAlertConfigSchema = BaseModifyAlertConfigSchema.extend({
  services: z.array(ServiceSchema),
  applications: z.array(ApplicationSchema),
});

export type ModifyAlertConfig = z.infer<typeof ModifyAlertConfigSchema>;
