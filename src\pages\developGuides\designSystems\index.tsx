import React, { useState } from 'react';

import { IconCameraSelfie, IconHeart, IconPhoto } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import {
  KanbanAccordion,
  KanbanAutocomplete,
  KanbanButton,
  KanbanCheckbox,
  KanbanColorInput,
  KanbanContentLoading,
  KanbanDateInput,
  KanbanDatePicker,
  KanbanDateTimePicker,
  KanbanDropZone,
  KanbanFileInput,
  KanbanIconButton,
  KanbanIconSelect,
  KanbanInput,
  KanbanLoading,
  KanbanMonthPicker,
  KanbanMultiSelect,
  KanbanNumberInput,
  KanbanPagination,
  KanbanRadio,
  KanbanSelect,
  KanbanSlider,
  KanbanSwitch,
  KanbanTable,
  KanbanTabs,
  KanbanTagsInput,
  KanbanTextarea,
  KanbanTimePicker,
  KanbanTooltip,
  KanbanYearPicker,
  renderCheckbox,
  renderDate,
} from 'kanban-design-system';
import { Container } from '@mantine/core';
import { random } from 'lodash';
import Modal from '@components/Modal';

export const DesignSystemPage = () => {
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const [tableData] = useState(
    Array.from({ length: 50 }).map((_value, index) => ({
      col1: `Name ${index}`,
      col2: new Date(new Date().getMilliseconds() - Math.random() * 1e12),
      col3: !(random() < 0.5),
    })),
  );

  const [icon, onChangeIcon] = useState('Icon123');

  return (
    <>
      API Defination Rules 1:{' '}
      <a href='https://restfulapi.net/resource-naming/' target='_blank' rel='noreferrer'>
        https://restfulapi.net/resource-naming/
      </a>
      CSS Variables are require:{' '}
      <a href='https://mantine.dev/styles/css-variables' target='_blank' rel='noreferrer'>
        https://mantine.dev/styles/css-variables/
      </a>
      Icons:{' '}
      <a href='https://tabler-icons.io/' target='_blank' rel='noreferrer'>
        https://tabler-icons.io/
      </a>
      <br />
      <KanbanInput label='Input' description='Input description' placeholder='Input placeholder' />
      <br />
      <KanbanSelect label='Select' placeholder='Pick value' data={['React', 'Angular', 'Vue', 'Svelte']} />
      <br />
      <KanbanMultiSelect label='Multi select' placeholder='Pick value' data={['React', 'Angular', 'Vue', 'Svelte']} />
      <br />
      <KanbanIconSelect
        value={icon}
        label='Icon select'
        placeholder='Pick an icon'
        onChange={(e) => {
          onChangeIcon(e || '');
        }}
      />
      <br />
      <KanbanCheckbox defaultChecked label='I agree to sell my privacy' />
      <br />
      <KanbanColorInput label='Color input' description='Input description' placeholder='Input placeholder' />
      <br />
      <KanbanFileInput label='File input' description='Input description' placeholder='Input placeholder' />
      <br />
      <KanbanTextarea label='Textarea' description='Input description' placeholder='Input placeholder' />
      <br />
      <KanbanNumberInput label='Number' description='Input description' placeholder='Input placeholder' />
      <br />
      <KanbanSwitch defaultChecked label='I agree to sell my privacy' />
      <br />
      <KanbanDropZone />
      <br />
      <KanbanDateInput label='Date input' />
      <br />
      <KanbanDatePicker label='Date picker' type={'multiple'} />
      <br />
      <KanbanDateTimePicker label='Date time picker' />
      <br />
      <KanbanMonthPicker label='Month picker' />
      <br />
      <KanbanYearPicker label='Year picker' />
      <br />
      <KanbanTimePicker label='Time picker' />
      <br />
      <KanbanRadio
        group={{
          name: 'favoriteFramework',
          label: 'Radio',
          description: 'This is anonymous',
          withAsterisk: true,
        }}
        radios={[
          {
            value: 'react',
            label: 'react',
          },
          {
            value: 'svelte',
            label: 'svelte',
          },
          {
            value: 'vue',
            label: 'vue',
          },
        ]}
      />
      <br />
      <KanbanSlider
        withAsterisk
        label='Slider'
        marks={[
          { value: 20, label: '20%' },
          { value: 50, label: '50%' },
          { value: 80, label: '80%' },
        ]}
      />
      <br />
      <KanbanAutocomplete label='Auto complete' placeholder='Pick value or enter anything' data={['React', 'Angular', 'Vue', 'Svelte']} />
      <br />
      <KanbanTagsInput label='Press Enter to submit a tag' placeholder='Tags input' description='Description' />
      <br />
      <KanbanPagination total={10} color='primary' />
      <br />
      <KanbanTable
        columns={[
          {
            name: 'col1',
            title: 'Name',
          },
          {
            name: 'col2',
            title: 'Age',
            customRender: renderDate,
          },
          {
            name: 'col3',
            title: 'Is valid',
            customRender: renderCheckbox,
          },
        ]}
        data={tableData}
        selectableRows={{
          enable: true,
        }}
        pagination={{
          enable: true,
        }}
        sortable={{
          enable: true,
        }}
        searchable={{
          enable: true,
        }}
        actions={{
          deletable: {
            onDeleted() {},
          },
          customAction: () => (
            <>
              <KanbanIconButton key={1} size='sm' color='blue' variant='transparent'>
                <IconHeart />
              </KanbanIconButton>
            </>
          ),
        }}
      />
      <br />
      <div>
        <KanbanButton variant='filled' size='xs'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='sm'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='md'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='lg'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='xl'>
          Button
        </KanbanButton>
        <br />
        <KanbanButton variant='filled' size='compact-xs'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='compact-sm'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='compact-md'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='compact-lg'>
          Button
        </KanbanButton>
        <KanbanButton variant='filled' size='compact-xl'>
          Button
        </KanbanButton>
        <br />
        <KanbanButton variant='default'>Button</KanbanButton>
        <KanbanButton variant='light'>Button</KanbanButton>
        <KanbanButton variant='outline'>Button</KanbanButton>
        <KanbanButton variant='subtle'>Button</KanbanButton>
        <KanbanButton variant='transparent'>Button</KanbanButton>
        <KanbanButton variant='white'>Button</KanbanButton>
        <KanbanIconButton size='md'>
          <IconHeart />
        </KanbanIconButton>
      </div>
      <br />
      <div style={{ display: 'flex' }}>
        <KanbanLoading color='primary' />
        <KanbanLoading color='primary' type='bars' />
        <KanbanLoading color='primary' type='dots' />
      </div>
      <br />
      <KanbanContentLoading loading={true}>
        <KanbanInput label='Input' description='Input description' placeholder='Input placeholder' />
        <br />
        <KanbanSelect label='Select' placeholder='Pick value' data={['React', 'Angular', 'Vue', 'Svelte']} />
        <br />
      </KanbanContentLoading>
      <br />
      <Modal opened={openedModal} onClose={closeModal}>
        <KanbanButton variant='default'>Button</KanbanButton>
        <KanbanButton variant='filled'>Button</KanbanButton>
        <KanbanButton variant='light'>Button</KanbanButton>
      </Modal>
      <KanbanButton variant='default' onClick={openModal}>
        Open modal
      </KanbanButton>
      <br />
      <KanbanTooltip label='This is Tooltip'>
        <div style={{ display: 'inline-block', width: 'min-content' }}>
          <KanbanButton variant='default'>Tooltip</KanbanButton>
        </div>
      </KanbanTooltip>
      <br />
      <KanbanAccordion
        data={[
          {
            content: 'Bender Bending Rodríguez',
            title: 'Fascinated with cooking, though has no sense of taste',
            icon: {
              component: IconPhoto,
            },
          },
          {
            content: <p>One of the richest people on Earth</p>,
            title: 'Carol Miller',
            icon: {
              component: IconCameraSelfie,
              color: 'secondary',
            },
          },
          {
            content: <p>Overweight, lazy, and often ignorant</p>,
            title: 'Homer Simpson',
          },
        ]}
      />
      <br />
      <KanbanTabs
        configs={{
          defaultValue: '1',
        }}
        tabs={{
          '1': {
            content: 'Gallery',
            title: 'Gallery',
          },
          '2': {
            content: 'Gallery 1',
            title: 'Gallery 1',
            icon: IconHeart,
          },
        }}
      />
      <br />
      <KanbanTabs
        configs={{
          defaultValue: '1',
          orientation: 'vertical',
          variant: 'default',
        }}
        tabs={{
          '1': {
            content: <Container ml={'xs'}>Gallery</Container>,
            title: 'Gallery',
          },
          '2': {
            content: <Container ml={'xs'}>Gallery1</Container>,
            title: 'Gallery 1',
            icon: IconHeart,
          },
        }}
      />
      <br />
      <br />
      <br />
    </>
  );
};

export default DesignSystemPage;
