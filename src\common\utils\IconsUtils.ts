import * as TablerIcons from '@tabler/icons-react';
import { camelToHyphenCase } from './Helpers';

type TablerKeys = keyof typeof TablerIcons;
export type TablerIconKeys = TablerKeys extends infer U ? (U extends `Icon${string}` ? U : never) : never;
export type TablesIconName = TablerIconKeys extends `Icon${infer U}` ? U : TablerIconKeys;
export const getAllTablerIconNames = (): TablerIconKeys[] => {
  return Object.keys(TablerIcons).filter((x) => x.startsWith('Icon')) as TablerIconKeys[];
};
export const getAllTablerIcons = () => {
  return getAllTablerIconNames().map((x) => {
    const result: React.ComponentType<TablerIcons.IconProps> = TablerIcons[x];
    return result;
  });
};

export const getTablerIconByName = (name?: TablerIconKeys) => {
  try {
    if (!name) {
      return undefined;
    }
    const result: React.ComponentType<TablerIcons.IconProps> | undefined = TablerIcons[name];

    return result;
  } catch (ex) {
    return undefined;
  }
};

export const getIconPublicPathFromIconName = (name?: TablerIconKeys | TablesIconName) => {
  if (!name) {
    return '';
  }
  let currentName: string = name;
  if (currentName.startsWith('Icon')) {
    currentName = currentName.replace('Icon', '');
  }
  const iconPath = camelToHyphenCase(currentName);
  return `/images/icons/${iconPath}.svg`;
};
