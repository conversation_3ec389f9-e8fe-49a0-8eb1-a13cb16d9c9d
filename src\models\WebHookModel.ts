export type WebHookModel = {
  id?: number;
  name?: string;
  dataType?: string;
  serviceNameType?: string;
  serviceId?: string;
  serviceMapValue?: string;
  applicationType?: string;
  applicationId?: string;
  applicationMapValue?: string;
  alertContentType?: string;
  alertContentCustomValue?: string;
  alertContentMapValue?: string;
  priorityType?: string;
  alertPriorityConfigId?: string;
  priorityMapValue?: string;
  contactType?: string;
  contactCustomValue?: string;
  contactMapValue?: string;
};
