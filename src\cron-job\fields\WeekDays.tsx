/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import React, { useMemo } from 'react';

import CustomSelect from '../components/CustomSelect';
import { UNITS } from '../ConstantsSelect';
import { DEFAULT_LOCALE_EN } from '../LocaleSelect';
import { WeekDaysProps } from '../TypesSelect';

export default function WeekDays(props: WeekDaysProps) {
  const { className, disabled, filterOption, humanizeLabels, locale, mode, monthDays, period, periodicityOnDoubleClick, readOnly, setValue, value } =
    props;
  const optionsList = locale.weekDays || DEFAULT_LOCALE_EN.weekDays;
  const noMonthDays = period === 'week' || !monthDays || monthDays.length === 0;

  const placeholder = useMemo(() => {
    if (noMonthDays) {
      return locale.emptyWeekDays || DEFAULT_LOCALE_EN.emptyWeekDays;
    }

    return locale.emptyWeekDaysShort || DEFAULT_LOCALE_EN.emptyWeekDaysShort;
  }, [noMonthDays, locale.emptyWeekDaysShort, locale.emptyWeekDays]);

  const displayWeekDays =
    period === 'week' || !readOnly || (value && value.length > 0) || ((!value || value.length === 0) && (!monthDays || monthDays.length === 0));

  const monthDaysIsDisplayed =
    !readOnly || (monthDays && monthDays.length > 0) || ((!monthDays || monthDays.length === 0) && (!value || value.length === 0));

  if (!displayWeekDays) {
    return null;
  }

  return (
    <div>
      {locale.prefixWeekDays !== '' && (period === 'week' || !monthDaysIsDisplayed) && (
        <span>{locale.prefixWeekDays || DEFAULT_LOCALE_EN.prefixWeekDays}</span>
      )}

      {locale.prefixWeekDaysForMonthAndYearPeriod !== '' && period !== 'week' && monthDaysIsDisplayed && (
        <span>{locale.prefixWeekDaysForMonthAndYearPeriod || DEFAULT_LOCALE_EN.prefixWeekDaysForMonthAndYearPeriod}</span>
      )}

      <CustomSelect
        placeholder={placeholder}
        optionsList={optionsList}
        grid={false}
        value={value}
        unit={{
          ...UNITS[4],
          // Allow translation of alternative labels when using "humanizeLabels"
          // Issue #3
          alt: locale.altWeekDays || DEFAULT_LOCALE_EN.altWeekDays,
        }}
        setValue={setValue}
        locale={locale}
        className={className}
        humanizeLabels={humanizeLabels}
        disabled={disabled}
        readOnly={readOnly}
        period={period}
        periodicityOnDoubleClick={periodicityOnDoubleClick}
        mode={mode}
        filterOption={filterOption}
      />
    </div>
  );
}
