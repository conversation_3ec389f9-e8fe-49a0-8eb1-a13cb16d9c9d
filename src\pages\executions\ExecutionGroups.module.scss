.container {
  background-color: white;
  border: 1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-4));
  padding: calc(var(--mantine-spacing-xs) / 2) ;
  height: var(--kanban-appshell-maxheight-content);
  overflow-y: auto;
}
.group{
  cursor: pointer;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  gap: var(--mantine-spacing-xs);
}

.group:hover{
  background-color: var(--mantine-color-gray-1);
}

.executionDetail {
  background-color: white;
  border: 1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-4));
  height: 100%;
  height: var(--kanban-appshell-maxheight-content);
  padding: calc(var(--mantine-spacing-xs) / 2);
}

.executionItem {
  cursor: pointer;
  padding: var(--mantine-spacing-xs);
  padding-left: var(--mantine-spacing-xl);
}

.executionItem:hover{
  background-color: var(--mantine-color-gray-3);
}


.executionList {
  background-color: var(--mantine-color-gray-1);
}

