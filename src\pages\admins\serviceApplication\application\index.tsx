import React, { useState, useMemo, useRef, useCallback } from 'react';
import equal from 'fast-deep-equal';
import {
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { Application } from '@core/schema';
import { IconFileExport, IconPlus, IconTrash } from '@tabler/icons-react';
import { ApplicationApi } from '@api/ApplicationApi';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { IconEdit } from '@tabler/icons-react';
import CreateOrUpdateModal from './components/CreateOrUpdateModal';
import DeleteModal from './components/DeleteModal';
import { SortType } from '@common/constants/SortType';
import { ApplicationPaginationRequest } from '@models/ApplicationModel';
import ExportFileModal from './components/ExportFileModal';
import { columns } from './ApplicationCommon';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';

export const ManagementApplicationPage = () => {
  const [tableAffected, setTableAffected] = useState<ApplicationPaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const [openedModalCreateApplication, { close: closeModalCreateApplication, open: openModalCreateApplication }] = useDisclosure(false);
  const [openedExportFile, { close: closeExportFile, open: openExportFile }] = useDisclosure(false);
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);
  const [rowSelected, setRowSelected] = useState<Application | undefined>(undefined);

  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const { data: listApplication, refetch: refetchList } = useFetch(ApplicationApi.findAllByServiceIdIn(tableAffected), {
    showLoading: false,
    placeholderData: (prev) => prev,
  });

  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<Application>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const tableViewListRolesProps: KanbanTableProps<Application> = useMemo(() => {
    return {
      columns: columns,
      data: listApplication?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listApplication?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.applicationManageEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setRowSelected(data);
                      openModalCreateApplication();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>

              <GuardComponent requirePermissions={[AclPermission.applicationManageDelete]}>
                <KanbanTooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      openModalDel();
                      setRowSelected(data);
                    }}>
                    <IconTrash color='red' />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    handleUpdateTablePagination,
    listApplication?.data?.content,
    listApplication?.data?.totalElements,
    openModalCreateApplication,
    openModalDel,
    tableAffected,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of application'
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <GuardComponent requirePermissions={[AclPermission.applicationManageCreate]}>
              <KanbanButton
                size='xs'
                onClick={() => {
                  setRowSelected(undefined);
                  openModalCreateApplication();
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </GuardComponent>

            <GuardComponent requirePermissions={[AclPermission.applicationManageExport]}>
              <KanbanButton
                disabled={listApplication?.data?.empty}
                size='xs'
                onClick={() => {
                  openExportFile();
                }}
                leftSection={<IconFileExport />}>
                Export
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
      <CreateOrUpdateModal
        opened={openedModalCreateApplication}
        onClose={closeModalCreateApplication}
        application={rowSelected}
        refetchList={refetchList}
      />
      <ExportFileModal opened={openedExportFile} onClose={closeExportFile} tableAffected={tableAffected} />
      <DeleteModal
        applicationName={rowSelected?.name}
        opened={openedModalDel}
        onClose={closePopupDel}
        applicationId={rowSelected?.id}
        refetchList={refetchList}
      />
    </Box>
  );
};
export default ManagementApplicationPage;
