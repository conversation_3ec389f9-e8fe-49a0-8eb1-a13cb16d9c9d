import { z } from 'zod';

export const ApplicationDependenciesSchema = z.object({
  webHooks: z.array(z.string()),
  collectEmailConfigs: z.array(z.string()),
  alertGroupConfigs: z.array(z.string()),
  databaseCollects: z.array(z.string()),
  maintenanceTimeConfigs: z.array(z.string()),
  modifyAlertConfigs: z.array(z.string()),
  databaseThresholdConfigs: z.array(z.string()),
});
export type ApplicationDependency = z.infer<typeof ApplicationDependenciesSchema>;
