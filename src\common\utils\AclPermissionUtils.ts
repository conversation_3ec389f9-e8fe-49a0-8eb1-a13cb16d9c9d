import { PermissionTypeEnum } from '@common/constants/PermissionType';
import { AclPermission } from '@models/AclPermission';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { getDirectState } from '@store';

export const isAnyPermissions = (permissions?: AclPermission[]): boolean => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser || !currentUser.userInfo?.isActive) {
    return false;
  }

  if (!permissions || permissions.length === 0 || currentUser.userInfo?.isAdmin) {
    return true;
  }

  const userAclPermissions = getAllPermissionOfCurrentUser();
  // custom is any a permission submodule

  const existsInPermissionAll = permissions.some((p) =>
    userAclPermissions.some((pa) => {
      // check submodule with not id
      if (PermissionTypeEnum.SUB_MODULE === pa.type && !p.moduleId && !p.moduleParentId) {
        return isPermissionAnySubModuleEqual(pa, p);
      }

      // check submodule
      if (PermissionTypeEnum.SUB_MODULE === pa.type) {
        return isPermissionSubModuleEqual(pa, p);
      }

      return isPermissionEqual(p, pa);
    }),
  );
  return !!existsInPermissionAll;
};

export const getAllPermissionOfCurrentUser = (): AclPermission[] => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return [];
  }
  const roles = currentUser.userInfo?.roles || [];

  // Use flatMap to flatten the list of permissions
  const aclPermissions = roles.flatMap((role) => {
    return role.permissions?.map((obj) => AclPermission.createAcl(obj.module, obj.action, obj.type, obj.moduleId, obj.moduleParentId)) || [];
  });

  return aclPermissions;
};

export const aclPermissionGetIdentical = (aclPermission: AclPermission) => {
  return `${aclPermission.module}-${aclPermission.action}`;
};

export const aclPermissionSubModuleGetIdentical = (aclPermission: AclPermission) => {
  return `${aclPermission.module}-${aclPermission.action}-${aclPermission.moduleId}-${aclPermission.moduleParentId}`;
};

export const isPermissionEqual = (alcPermissionA: AclPermission, aclPermissionB: AclPermission) => {
  return aclPermissionGetIdentical(alcPermissionA) === aclPermissionGetIdentical(aclPermissionB);
};

export const isPermissionSubModuleEqual = (userPermission: AclPermission, inputPermission: AclPermission) => {
  if (!userPermission.moduleParentId) {
    const userPermissionIdentical = `${userPermission.module}-${userPermission.action}-${userPermission.moduleId}`;
    const inputPermissionIdentical = `${inputPermission.module}-${inputPermission.action}-${inputPermission.moduleParentId}`;
    return userPermissionIdentical === inputPermissionIdentical;
  }
  return aclPermissionSubModuleGetIdentical(userPermission) === aclPermissionSubModuleGetIdentical(inputPermission);
};

export const isPermissionAnySubModuleEqual = (userPermission: AclPermission, inputPermission: AclPermission) => {
  return aclPermissionGetIdentical(userPermission) === aclPermissionGetIdentical(inputPermission);
};
