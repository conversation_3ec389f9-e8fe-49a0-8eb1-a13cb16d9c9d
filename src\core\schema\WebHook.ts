import { WEBHOOK_FIELD_TYPE } from '@common/constants/WebHookConstant';
import { z } from 'zod';

export const WebHookSchema = z.object({
  id: z.number(),
  name: z.string(),
  dataType: z.string().optional(),
  token: z.string().optional(),
  serviceNameType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  serviceId: z.string().optional(),
  serviceMapValue: z.string().optional(),
  applicationType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  applicationId: z.string().optional(),
  applicationMapValue: z.string().optional(),
  alertContentType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  alertContentCustomValue: z.string().optional(),
  alertContentMapValue: z.string().optional(),
  priorityType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  alertPriorityConfigId: z.number().optional(),
  priorityMapValue: z.string().optional(),
  contactType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  contactCustomValue: z.string().optional(),
  contactMapValue: z.string().optional(),
  createdBy: z.string().optional(),
  createdDate: z.string().optional(),
});

export type WebHook = z.infer<typeof WebHookSchema>;
