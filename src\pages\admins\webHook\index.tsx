import {
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanText,
  renderDateTime,
  TableAffactedSafeType,
} from 'kanban-design-system';
import React, { useMemo, useRef, useState } from 'react';
import { WebHookApi } from 'api/WebHookApi';
import useFetch from '@core/hooks/useFetch';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { IconCheck, IconCopy, IconEdit, IconEye, IconPlus, IconTrash } from '@tabler/icons-react';
import { WebHook } from '@core/schema/WebHook';
import equal from 'fast-deep-equal';
import { ActionIcon, CopyButton, Tooltip } from '@mantine/core';
import useMutate from '@core/hooks/useMutate';
import { getConfigs } from '@core/configs/Configs';
import { tableAffectedToPaginationRequestModel } from '@common/utils/TableAffectedUtils';
import Table from '@components/table';
import { useDisclosure } from '@mantine/hooks';
import { CurlGenerator } from 'curl-generator';
import { CurlBody } from 'curl-generator/dist/bodies/body';
import { WebHookModel } from '@models/WebHookModel';
import WebHookConfig from './WebHookConfig';
import { WEBHOOK_FIELD_TYPE } from '@common/constants/WebHookConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import Modal from '@components/Modal';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';

const DEFAULT_WEBHOOK_VALUE: WebHookModel = {
  serviceNameType: WEBHOOK_FIELD_TYPE.FROM_SOURCE,
  applicationType: WEBHOOK_FIELD_TYPE.FROM_SOURCE,
  alertContentType: WEBHOOK_FIELD_TYPE.FROM_SOURCE,
  priorityType: WEBHOOK_FIELD_TYPE.FROM_SOURCE,
  contactType: WEBHOOK_FIELD_TYPE.FROM_SOURCE,
};

const getTextCopyConfig = (data: WebHook) => {
  const configs = getConfigs();
  const api = configs.apiWebHookCollect;

  const method = 'POST' as const;

  const alertContentAttr =
    (data.alertContentType === WEBHOOK_FIELD_TYPE.FROM_SOURCE ? data.alertContentMapValue?.replace('$.', '') : 'alert_content') || 'alert_content';
  const serviceNamteAttr =
    (data.serviceNameType === WEBHOOK_FIELD_TYPE.FROM_SOURCE ? data.serviceMapValue?.replace('$.', '') : 'service_name') || 'service_name';
  const appNameAttr =
    (data.applicationType === WEBHOOK_FIELD_TYPE.FROM_SOURCE ? data.applicationMapValue?.replace('$.', '') : 'app_name') || 'app_name';
  const contactAttr = (data.contactType === WEBHOOK_FIELD_TYPE.FROM_SOURCE ? data.contactMapValue?.replace('$.', '') : 'contact') || 'contact';
  const priorityAttr = (data.priorityType === WEBHOOK_FIELD_TYPE.FROM_SOURCE ? data.priorityMapValue?.replace('$.', '') : 'priority') || 'priority';

  const body: CurlBody = {
    [alertContentAttr]: 'Kanban team alert',
    [serviceNamteAttr]: 'AWS Backup',
    [appNameAttr]: 'Kanban team',
    [contactAttr]: 'IT.OM',
    [priorityAttr]: '1',
  };

  const params: Parameters<typeof CurlGenerator>[0] = {
    url: api,
    method: method,
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
      Authorization: data.token || '',
    },
    body: body,
  };

  return CurlGenerator(params);
};

const isValidWebHookModel = (data: WebHookModel): boolean => {
  // Check if required fields are provided
  if (
    !data.name ||
    !data.dataType ||
    !data.alertContentType ||
    !data.applicationType ||
    !data.contactType ||
    !data.serviceNameType ||
    !data.priorityType
  ) {
    return false;
  }
  const isServiceValid = !!data.serviceId || !!data.serviceMapValue;
  const isAppValid = !!data.applicationId || !!data.applicationMapValue;
  const isAlertValid = !!data.alertContentCustomValue || !!data.alertContentMapValue;
  const isContactValid = !!data.contactCustomValue || !!data.contactMapValue;
  const isPriorityValid = !!data.alertPriorityConfigId || !!data.priorityMapValue;
  return isServiceValid && isAppValid && isAlertValid && isContactValid && isPriorityValid;
};

export const WebHookPage = () => {
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType<WebHook>>({ page: 1, search: '', isReverse: true });
  const [openedModalConfig, { close: closeModalConfig, open: openModalConfig }] = useDisclosure(false);
  const [rowSelected, setRowSelected] = useState<WebHook | undefined>(undefined);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const [webHook, setWebHook] = useState<WebHookModel>(DEFAULT_WEBHOOK_VALUE);
  const [isViewMode, setIsViewMode] = useState<boolean>(false);

  // CALL API
  const { data: listConfigData, refetch: refetchList } = useFetch(WebHookApi.findAll(tableAffectedToPaginationRequestModel(tableAffected)), {
    enabled: !!tableAffected,
    placeholderData: (prev) => prev,
  });

  //refresh token
  const { mutate: refreshToken } = useMutate(WebHookApi.refreshToken, {
    successNotification: { enable: true, message: 'Refresh Token Successfully' },
    onSuccess: () => {
      if (tableRef?.current) {
        tableRef.current?.deselectAll();
      }
      refetchList();
      closeModalConfig();
      setWebHook(DEFAULT_WEBHOOK_VALUE);
    },
    confirm: {
      title: 'Confirm Refesh Token',
      children: 'The old token will be invalid. Are you sure you want to refresh the token?',
    },
  });

  //save data
  const { mutate: saveMutate } = useMutate(WebHookApi.save, {
    successNotification: { enable: true, message: 'Configuration Saved Successfully' },
    onSuccess: () => {
      if (tableRef?.current) {
        tableRef.current?.deselectAll();
        tableRef.current?.setPage(1);
      }
      refetchList();
      closeModalConfig();
      setWebHook(DEFAULT_WEBHOOK_VALUE);
    },
  });

  //delete by id
  const { mutate: deleteByIdMutate } = useMutate(WebHookApi.deleteById, {
    successNotification: { enable: true, message: 'Deleted successfully.!' },
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const columns = useMemo(() => {
    return [
      {
        title: 'Name',
        name: 'name',
        customRender: (data: string) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
      },
      {
        title: 'Data Type',
        name: 'dataType',
      },
      {
        title: 'Created By',
        name: 'createdBy',
      },
      {
        title: 'Created Date',
        name: 'createdDate',
        customRender: renderDateTime,
      },
    ];
  }, []);

  const tableProps: KanbanTableProps<WebHook> = useMemo(() => {
    return {
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },

      serverside: {
        totalRows: listConfigData?.data?.totalElements || 0,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      columns: columns,
      data: listConfigData?.data?.content || [],
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.webHookView]}>
                <CopyButton value={getTextCopyConfig(data)}>
                  {({ copied, copy }) => (
                    <Tooltip label={copied ? 'Copied Config' : 'Copy Config'}>
                      <KanbanIconButton variant='transparent' size={'sm'} onClick={copy}>
                        {copied ? <IconCheck /> : <IconCopy />}
                      </KanbanIconButton>
                    </Tooltip>
                  )}
                </CopyButton>
              </GuardComponent>

              <GuardComponent requirePermissions={[AclPermission.webHookView]}>
                <Tooltip label='View Config'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setIsViewMode(true);
                      setRowSelected(data);
                      openModalConfig();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>

              <GuardComponent requirePermissions={[AclPermission.webHookEdit]}>
                <Tooltip label='Edit Config'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setIsViewMode(false);
                      setRowSelected(data);
                      openModalConfig();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.webHookDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [listConfigData?.data?.totalElements, listConfigData?.data?.content, columns, tableAffected, deleteByIdMutate, openModalConfig]);

  const enableSave: boolean = useMemo(() => (webHook ? isValidWebHookModel(webHook) : false), [webHook]);

  return (
    <>
      <HeaderTitleComponent
        title='WebHook Config'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.webHookCreate]}>
            <KanbanButton
              onClick={() => {
                setIsViewMode(false);
                setRowSelected(undefined);
                openModalConfig();
              }}
              leftSection={<IconPlus />}>
              New Config
            </KanbanButton>
          </GuardComponent>
        }
      />

      <Table ref={tableRef} {...tableProps} />

      <Modal
        size={'xl'}
        opened={openedModalConfig}
        onClose={() => {
          closeModalConfig();
          setWebHook(DEFAULT_WEBHOOK_VALUE);
        }}
        title={rowSelected?.id ? (isViewMode ? `View webhook ${rowSelected?.name}` : `Update webhook ${rowSelected?.name}`) : 'Create new webhook'}
        actions={
          <>
            {rowSelected?.id && !isViewMode && (
              <KanbanButton
                onClick={() => {
                  if (rowSelected?.id) {
                    refreshToken(rowSelected.id);
                  }
                }}>
                Refresh Token
              </KanbanButton>
            )}
            {!isViewMode && (
              <KanbanButton
                onClick={() => {
                  if (webHook) {
                    saveMutate(webHook);
                  }
                }}
                disabled={!enableSave}>
                Save
              </KanbanButton>
            )}
          </>
        }>
        <WebHookConfig isViewMode={isViewMode} id={rowSelected?.id} setWebHook={setWebHook} webHook={webHook} />
      </Modal>
    </>
  );
};

export default WebHookPage;
