import { KanbanTabs } from 'kanban-design-system';
import React, { useState } from 'react';
import UserManagement from './UserManagement';
import RoleManagement from './RoleManagement';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';

export const UserManagementPage = () => {
  const [activeTab, setActiveTab] = useState<string>(isAnyPermissions([AclPermission.userManageView]) ? 'users' : 'roles');

  return (
    <KanbanTabs
      configs={{
        defaultValue: activeTab,
        onChange: (val) => {
          setActiveTab(val || 'users');
        },
      }}
      tabs={{
        ...(isAnyPermissions([AclPermission.userManageView]) && {
          users: {
            content: <UserManagement />,
            title: 'Users Management',
          },
        }),
        ...(isAnyPermissions([AclPermission.roleManageView]) && {
          roles: {
            content: <RoleManagement />,
            title: 'Role Management',
          },
        }),
      }}></KanbanTabs>
  );
};
export default UserManagementPage;
