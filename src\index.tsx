import './wdyr';
import React from 'react';
import ReactDOM from 'react-dom/client';
import './resources/styles/Global.css';
import App from './App';
import { Provider } from 'react-redux';
import reportWebVitals from './reportWebVitals';
import { runMiddleWare, store } from './store';
import rootSaga from '@sagas/index';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

runMiddleWare(rootSaga);

root.render(
  <Provider store={store}>
    <App />
  </Provider>,
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
