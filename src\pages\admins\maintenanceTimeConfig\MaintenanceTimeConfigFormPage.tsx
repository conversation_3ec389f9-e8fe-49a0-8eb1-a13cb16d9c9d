import { isNaN, parseInt } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';
import { KanbanButton, KanbanInput } from 'kanban-design-system';
import classes from './GroupConfigStyle.module.css';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import ConditionSession from './CoditionSession';
import { MaintenanceTimeConfigAction, DEFAULT_FORM_VALUE } from './Constants';
import ReferenceSession from './ReferenceSession';
import { DESCRIPTION_MAX_LENGTH, MAX_NAME_LENGTH } from '@common/constants/ValidationConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import TimeType from './TimeType';
import { MaintenanceTimeConfigModel, MaintenanceTimeConfigModelSchema } from '@models/MaintenanceTimeConfigModel';
import { MaintenanceTimeConfigApi } from '@api/MaintenanceTimeConfigApi';

const SaveButton = ({ form }: { form: UseFormReturn<MaintenanceTimeConfigModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveMaintenanceTimeConfigMutate } = useMutate(MaintenanceTimeConfigApi.save, {
    onSuccess: () => {
      navigate('../');
    },
  });
  const { formState } = form;
  const onSubmit = useCallback(() => {
    const parsedData = MaintenanceTimeConfigModelSchema.safeParse(form.getValues());

    if (parsedData.success) {
      const data = parsedData.data;
      saveMaintenanceTimeConfigMutate({
        ...data,
        serviceIds: data.services.map((ele) => ele.id),
        applicationIds: data.applications?.map((ele) => ele.id) || [],
      });
    } else {
      form.setError('endTime', { message: 'End time must be later than the current time' });
    }
  }, [form, saveMaintenanceTimeConfigMutate]);
  return (
    <GuardComponent requirePermissions={[AclPermission.maintenanceTimeConfigEdit, AclPermission.maintenanceTimeConfigCreate]}>
      <KanbanButton onClick={onSubmit} disabled={!formState.isValid}>
        Save
      </KanbanButton>
    </GuardComponent>
  );
};

const MaintenanceTimeConfigFormPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { maintenanceTimeConfigId: maintenanceTimeConfigStringId } = useParams();
  const maintenanceTimeConfigId =
    !!maintenanceTimeConfigStringId && !isNaN(maintenanceTimeConfigStringId) ? parseInt(maintenanceTimeConfigStringId) : 0;
  const isCreateMode = !maintenanceTimeConfigId;
  const [currentTab, setCurrentTab] = useState<string>(MaintenanceTimeConfigAction.CREATE);
  const isViewMode = currentTab === MaintenanceTimeConfigAction.VIEW;

  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);

  const form = useForm<MaintenanceTimeConfigModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(MaintenanceTimeConfigModelSchema),
    mode: 'onChange',
  });

  const { data: maintenanceTimeConfigData } = useFetch(MaintenanceTimeConfigApi.findById(maintenanceTimeConfigId), { enabled: !isCreateMode });

  useEffect(() => {
    if (!isCreateMode && maintenanceTimeConfigData?.data) {
      const { data } = maintenanceTimeConfigData;
      form.reset({
        ...data,
        services: data?.services || [],
        applications: data?.applications || [],
      });
    }
  }, [maintenanceTimeConfigData, maintenanceTimeConfigData?.data, form, isCreateMode]);
  return (
    <Box>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>
          {isViewMode ? 'View maintenance time config' : isCreateMode ? 'Create maintenance time config' : 'Update maintenance time config'}
        </Title>
        <Flex>
          <Group>
            <KanbanButton variant='outline' onClick={() => navigate('../')}>
              Cancel
            </KanbanButton>
            {!isViewMode && <SaveButton form={form} />}
          </Group>
        </Flex>
      </Flex>
      <Stack gap='md'>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>General Information</Title>
          <KanbanInput disabled={isViewMode} label='Config name' required {...form.register('name')} maxLength={MAX_NAME_LENGTH} />
          <KanbanInput disabled={isViewMode} label='Description' {...form.register('description')} maxLength={DESCRIPTION_MAX_LENGTH} />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Reference</Title>
          <ReferenceSession isViewMode={isViewMode} form={form} />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Condition</Title>
          <ConditionSession isViewMode={isViewMode} form={form} />
        </Stack>

        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Time type</Title>
          <TimeType disabled={isViewMode} form={form} maintenanceTimeConfig={maintenanceTimeConfigData?.data} />
        </Stack>
      </Stack>
    </Box>
  );
};

export default MaintenanceTimeConfigFormPage;
