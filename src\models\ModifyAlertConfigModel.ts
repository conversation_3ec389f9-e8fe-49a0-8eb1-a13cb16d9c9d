import { DESCRIPTION_MAX_LENGTH, MAX_NAME_LENGTH, MIN_STRING_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { QueryRuleGroupTypeModelSchema } from './RuleGroupTypeModel';
import { ApplicationSchema, ServiceSchema } from '@core/schema';

export const ModifyAlertConfigModifyRequestSchema = z.object({
  fieldName: z.string().min(1, 'Name is required'),
  fieldValue: z.string().min(1, 'Value is required'),
  contentHtml: z.string().optional(),
});

export const ModifyAlertConfigModelSchema = z.object({
  id: z.number().optional(),
  name: z.string().trim().min(MIN_STRING_LENGTH).max(MAX_NAME_LENGTH),
  description: z.string().max(DESCRIPTION_MAX_LENGTH).optional(),
  modifies: z.array(ModifyAlertConfigModifyRequestSchema),
  ruleGroup: QueryRuleGroupTypeModelSchema,
  services: z.array(ServiceSchema),
  applications: z.array(ApplicationSchema).optional(),
});

export type ModifyAlertConfigModel = z.infer<typeof ModifyAlertConfigModelSchema>;
