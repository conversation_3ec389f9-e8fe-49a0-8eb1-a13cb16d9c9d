import { BaseURL } from '@common/constants/BaseUrl';
import { createResponseSchema } from '@core/schema';
import { z } from 'zod';
import { ModifyAlertConfigRequest, ModifyAlertConfigUpdatePositionRequest, PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { BaseModifyAlertConfigSchema, ModifyAlertConfigSchema } from '@core/schema/ModifyAlertConfig';

export type ModifyAlertPaginationRequest = PaginationRequest & {
  search?: string;
};
export class ModifyAlertConfigApi {
  static findAll(searchParam?: { search?: string }) {
    return createRequest({
      url: BaseURL.modifyAlert,
      method: 'GET',
      schema: createResponseSchema(z.array(BaseModifyAlertConfigSchema)),
      params: searchParam,
    });
  }
  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.modifyAlert}/:id`,
      method: 'GET',
      schema: createResponseSchema(ModifyAlertConfigSchema),
      pathVariable: {
        id,
      },
    });
  }
  static save(data: ModifyAlertConfigRequest) {
    return createRequest({
      url: BaseURL.modifyAlert,
      method: 'POST',
      schema: createResponseSchema(ModifyAlertConfigSchema),
      data,
    });
  }
  static delete(id: number) {
    return createRequest({
      url: `${BaseURL.modifyAlert}/:id`,
      method: 'DELETE',
      schema: createResponseSchema(z.string()),
      pathVariable: {
        id,
      },
    });
  }

  static updateActive({ active, id }: { id: number; active: boolean }) {
    return createRequest({
      url: `${BaseURL.modifyAlert}/:id`,
      method: 'PUT',
      schema: createResponseSchema(ModifyAlertConfigSchema),
      pathVariable: {
        id,
      },
      params: {
        active,
      },
    });
  }

  static updatePosition(requestBody: ModifyAlertConfigUpdatePositionRequest) {
    return createRequest({
      url: `${BaseURL.modifyAlert}/position`,
      method: 'PUT',
      schema: createResponseSchema(z.array(ModifyAlertConfigSchema)),
      data: requestBody,
    });
  }
}
