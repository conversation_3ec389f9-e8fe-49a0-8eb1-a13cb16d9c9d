import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import React from 'react';

type ProtectedRouteProps = {
  children: React.ReactNode;
  requirePermissions: AclPermission[];
  errorElement: React.ReactNode;
};

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, errorElement, requirePermissions }) => {
  return <>{isAnyPermissions(requirePermissions) ? children : errorElement}</>;
};
