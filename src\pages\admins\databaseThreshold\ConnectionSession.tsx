import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Divider, Flex, TextInput } from '@mantine/core';
import { DatabaseThresholdConfigModel } from '@models/DatabaseThresholdConfigModel';
import { MAX_CRON_EXPRESSION_LENGTH } from '@common/constants/ValidationConstant';
import Cron from 'cron-job';
import DatabaseConnectionComponent from './DatabaseConnectionComponent';

interface Props {
  form: UseFormReturn<DatabaseThresholdConfigModel>;
  isViewMode?: boolean;
}

const ConnectionSession = ({ form, isViewMode }: Props) => {
  const { control, setValue } = form;
  const inputRef = React.useRef<HTMLInputElement>(null);
  return (
    <Flex direction='column' gap='sm'>
      <form>
        <Controller
          control={control}
          name='databaseConnectionId'
          render={({ field: { onChange, value } }) => (
            <DatabaseConnectionComponent onChange={onChange} opened={true} value={value.toString()} isViewMode={isViewMode} label={''} />
          )}
        />
        <Controller
          name='cronTime'
          control={control}
          render={({ field, fieldState }) => (
            <TextInput
              label='Interval'
              maxLength={MAX_CRON_EXPRESSION_LENGTH}
              ref={inputRef}
              value={field.value}
              onChange={(e) => {
                field.onChange(e.target.value);
              }}
              error={fieldState.error?.message}
            />
          )}
        />
        <Divider my='sm' label='OR' labelPosition='center' />
        <Controller
          name='cronTime'
          control={control}
          render={({ field }) => (
            <Cron
              value={field.value || ''}
              setValue={(val: string) => {
                setValue('cronTime', val);
                field.onChange(val);
              }}
            />
          )}
        />
      </form>
    </Flex>
  );
};

export default ConnectionSession;
