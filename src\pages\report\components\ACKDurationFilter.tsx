import React from 'react';
import { ComboboxData, Flex } from '@mantine/core';
import { KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import { AlertDurationCompareUnitEnum, DurationUnitLabel, AlertDurationCompareOperatorEnum, OperatorLabel } from '../Constants';

interface Props {
  operator?: AlertDurationCompareOperatorEnum;
  duration?: number;
  unit?: AlertDurationCompareUnitEnum;
  onChangeDuration: (operator?: AlertDurationCompareOperatorEnum, duration?: number, unit?: AlertDurationCompareUnitEnum) => void;
}

const OPERATOR_OPTIONS: ComboboxData = Object.keys(AlertDurationCompareOperatorEnum).map((key) => ({
  value: key,
  label: OperatorLabel[key as AlertDurationCompareOperatorEnum],
}));
const DURATION_UNIT_OPTIONS: ComboboxData = Object.keys(AlertDurationCompareUnitEnum).map((key) => ({
  value: key,
  label: DurationUnitLabel[key as AlertDurationCompareUnitEnum],
}));

const ACKDuration = ({ duration, onChangeDuration, operator, unit }: Props) => {
  return (
    <Flex>
      <KanbanSelect
        data={OPERATOR_OPTIONS}
        allowDeselect={false}
        label='ACK Duration'
        styles={{
          input: { border: '1px solid var(--input-bd)', borderRight: 'none', borderTopRightRadius: 'unset', borderBottomRightRadius: 'unset' },
        }}
        value={operator}
        onChange={(value) => onChangeDuration(value as AlertDurationCompareOperatorEnum, duration, unit)}
      />
      <KanbanNumberInput
        placeholder='Enter time'
        label=' '
        styles={{ input: { borderRadius: 'unset' } }}
        min={1}
        max={100000}
        clampBehavior='strict'
        value={duration}
        onChange={(value) => {
          const number = parseInt(`${value}`);
          onChangeDuration(operator, isNaN(number) ? undefined : number, unit);
        }}
      />
      <KanbanSelect
        label=' '
        styles={{
          input: { border: '1px solid var(--input-bd)', borderLeft: 'none', borderTopLeftRadius: 'unset', borderBottomLeftRadius: 'unset' },
        }}
        data={DURATION_UNIT_OPTIONS}
        allowDeselect={false}
        value={unit}
        onChange={(value) => onChangeDuration(operator, duration, value as AlertDurationCompareUnitEnum)}
      />
    </Flex>
  );
};

export default ACKDuration;
