import React, { useEffect, useMemo } from 'react';
import { Kanban<PERSON>itle, KanbanAccordionData, KanbanAccordion, KanbanButton } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Flex, Title } from '@mantine/core';
import GuardComponent from '@components/GuardComponent';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { AclPermission } from '@models/AclPermission';
import { IconArrowBack, IconUpload } from '@tabler/icons-react';
import { DatabaseThresholdConfigModel, DatabaseThresholdConfigModelSchema } from '@models/DatabaseThresholdConfigModel';
import BaseInformationSession from './BaseInformationSession';
import ConnectionSession from './ConnectionSession';
import ConditionSession from './ConditionSession';
import OutputSession from './OutputSession';
import { DatabaseThresholdConfigApi } from '@api/DatabaseThresholdConfigApi';
import { ConditionOperatorEnum } from '@common/constants/ConditionOperatorEnum';
import { DatabaseThresholdConfigAction } from '@common/constants/DatabaseThresholdConfigConstant';
import classes from './CreateOrUpdatePage.module.css';
import { DEFAULT_ALERT_PRIORITY_CONFIG_ID } from '@common/constants/AlertPriorityConfigConstants';

const defaultValues: DatabaseThresholdConfigModel = {
  id: undefined,
  name: '',
  description: '',
  applicationId: '',
  serviceId: '',
  databaseConnectionId: 0,
  priorityId: DEFAULT_ALERT_PRIORITY_CONFIG_ID,
  content: '',
  contentJson: '',
  cronTime: '* * * * *',
  recipient: '',
  sqlCommand: '',
  conditionOperator: ConditionOperatorEnum.GREATER_THAN,
  conditionValue: 0,
  active: true,
};

const TITLE_MAPS: Record<DatabaseThresholdConfigAction, string> = {
  VIEW: 'Detail Database Threshold Config',
  UPDATE: 'Update Database Threshold Config',
  CREATE: 'Create Database Threshold Config',
  COPY: 'Create Database Threshold Config',
};

const CreateOrUpdateDatabaseThresholdConfigPage: React.FC = () => {
  const navigate = useNavigate();
  const { id = '' } = useParams<{ id?: string }>();
  const [searchParams] = useSearchParams();
  const action = searchParams.get('action') || DatabaseThresholdConfigAction.CREATE;
  const isEdit = Boolean(id);
  const isView = action === DatabaseThresholdConfigAction.VIEW;

  const form = useForm<DatabaseThresholdConfigModel>({
    defaultValues,
    resolver: zodResolver(DatabaseThresholdConfigModelSchema),
    mode: 'onChange',
  });

  const {
    formState: { isSubmitting, isValid },
    handleSubmit,
    reset,
  } = form;

  const { data: detail, isFetching } = useFetch(DatabaseThresholdConfigApi.findById(id), { enabled: isEdit });

  const { mutate: saveConfig } = useMutate(DatabaseThresholdConfigApi.save, {
    successNotification:
      isEdit && action === DatabaseThresholdConfigAction.UPDATE
        ? 'Update Database Threshold Config Successfully'
        : 'Create Database Threshold Config Successfully',
    onSuccess: () => navigate(ROUTE_PATH.DATABASE_THRESHOLD),
  });

  // Populate form when editing
  useEffect(() => {
    if (detail?.data && !isFetching) {
      reset({
        ...detail.data,
        id: action === DatabaseThresholdConfigAction.UPDATE ? detail.data.id : undefined,
      });
    }
  }, [action, detail, isFetching, reset]);

  const onSave = handleSubmit((data) => {
    saveConfig(data);
  });

  const accordionItems: KanbanAccordionData[] = useMemo(
    () => [
      {
        key: 'general',
        title: <KanbanTitle order={4}>General Information</KanbanTitle>,
        content: <BaseInformationSession form={form} isViewMode={isView} />,
      },
      {
        key: 'connection',
        title: <KanbanTitle order={4}>Connection</KanbanTitle>,
        content: <ConnectionSession form={form} isViewMode={isView} />,
      },
      {
        key: 'condition',
        title: <KanbanTitle order={4}>Condition</KanbanTitle>,
        content: <ConditionSession form={form} isViewMode={isView} />,
      },
      {
        key: 'output',
        title: <KanbanTitle order={4}>Output</KanbanTitle>,
        content: <OutputSession form={form} isViewMode={isView} oldData={detail?.data} />,
      },
    ],
    [form, isView, detail],
  );

  return (
    <>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>{TITLE_MAPS[action as DatabaseThresholdConfigAction]}</Title>
        <Flex gap='sm'>
          <KanbanButton leftSection={<IconArrowBack />} variant='outline' onClick={() => navigate(ROUTE_PATH.DATABASE_THRESHOLD)}>
            Cancel
          </KanbanButton>

          {!isView && (
            <GuardComponent requirePermissions={[AclPermission.databaseThresholdConfigCreate, AclPermission.databaseThresholdConfigEdit]}>
              <KanbanButton leftSection={<IconUpload />} onClick={onSave} disabled={!isValid || isSubmitting}>
                Save
              </KanbanButton>
            </GuardComponent>
          )}
        </Flex>
      </Flex>

      <KanbanAccordion data={accordionItems} defaultValue={accordionItems.map((item) => String(item.key))} />
    </>
  );
};

export default CreateOrUpdateDatabaseThresholdConfigPage;
