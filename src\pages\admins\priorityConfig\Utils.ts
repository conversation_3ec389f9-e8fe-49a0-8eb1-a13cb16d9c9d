import { RGB_REGEX } from '@common/constants/RegexConstant';
import { RBG_MAX_VALUE, RGB_MIN_VALUE } from './Constants';

const DEFAULT_RGB = [0, 0, 0];

const formatItemRGB = (number: number) => {
  if (number < RGB_MIN_VALUE || number > RBG_MAX_VALUE) {
    return RGB_MIN_VALUE;
  }
  return number;
};
export const formatRGB = (r: number, g: number, b: number) => {
  return `rgb(${formatItemRGB(r)}, ${formatItemRGB(g)}, ${formatItemRGB(b)})`;
};

export const parseRGB = (rgbColor: string) => {
  if (!RGB_REGEX.test(rgbColor)) {
    return DEFAULT_RGB;
  }
  const matchArray = rgbColor.match(/\d{1,3}/g);
  if (!matchArray) {
    return DEFAULT_RGB;
  }
  return matchArray.map((ele) => {
    const num = Number(ele);
    return formatItemRGB(num);
  });
};
