import { TaskShiftEnum, TaskTimeTypeEnum, TaskTypeEnum } from '@common/constants/TaskConstants';
import { MAX_DESCRIPTION_LENGTH, MAX_NAME_LENGTH } from '@common/constants/ValidationConstant';
import { TaskSchema } from '@core/schema/Task';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { z } from 'zod';

export const TaskModelSchema = z
  .object({
    id: z.number().optional(),
    name: z.string().trim().max(MAX_NAME_LENGTH).optional(),
    description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
    type: z.nativeEnum(TaskTypeEnum),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
    timeType: z.nativeEnum(TaskTimeTypeEnum).optional(),
    shift: z.nativeEnum(TaskShiftEnum).optional(),
    handoverUsers: z.array(z.string()).optional(),
    tasks: z.array(TaskSchema).optional(),
  })
  .superRefine((value, ctx) => {
    if (value.type === TaskTypeEnum.TASK) {
      if (!value.name) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['name'],
          message: 'Task name can not be blank',
        });
      }
      if (!value.startTime) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['startTime'],
          message: 'Start time can not be empty',
        });
      } else if (!dayjs(value.startTime).isValid()) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['startTime'],
          message: 'Start time invalid',
        });
      }
      if (value.timeType === TaskTimeTypeEnum.FROM_TIME_TO_TIME) {
        if (!value.endTime) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['endTime'],
            message: 'End time can not be empty',
          });
        } else if (!dayjs(value.endTime).isValid()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['endTime'],
            message: 'End time invalid',
          });
        } else if (dayjs(value.startTime).isAfter(value.endTime)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['startTime'],
            message: 'Start time can not after end time',
          });
        }
      }
    } else if (value.type === TaskTypeEnum.SHIFT_HANDOVER_TASK) {
      if (!value.shift) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['shift'],
          message: 'Shift handover can not be empty',
        });
      } else if (isEmpty(value.tasks)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['tasks'],
          message: 'Shift handover need have at least one task',
        });
      } else if (isEmpty(value.handoverUsers)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['handoverUsers'],
          message: 'Handover user can not be empty',
        });
      }
    }
  });

export type TaskModel = z.infer<typeof TaskModelSchema>;
