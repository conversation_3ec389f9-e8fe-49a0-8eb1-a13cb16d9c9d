import { EmailProtocolSecurityTypeEnum } from '@common/constants/EmailProtocolSecurityTypeConstant';
import { EmailProtocolTypeEnum } from '@common/constants/EmailProtocolTypeConstant';
import { z } from 'zod';

export const EmailConfigSchema = z.object({
  id: z.number(),
  host: z.string(),
  description: z.string().optional(),
  protocolType: z.nativeEnum(EmailProtocolTypeEnum),
  port: z.number(),
  password: z.string().optional(),
  securityType: z.nativeEnum(EmailProtocolSecurityTypeEnum),
  username: z.string(),
  email: z.string(),
  active: z.boolean(),
});

export type EmailConfig = z.infer<typeof EmailConfigSchema>;
