import { refetchRequest } from '@common/utils/QueryUtils';
import { CalendarMode } from '../Types';
import { FilterTaskModel } from '@models/FilterTaskModel';
import { TaskApi } from '@api/TaskApi';
import { range } from 'lodash';
import { DEFAULT_TASK_PAGE_SIZE, ViewModeEnum } from '../Contants';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { groupConsecutiveDates } from '../calendarSection/Utils';

export const refetchEventPageData = (calendarMode: CalendarMode, filterValue: FilterTaskModel) => {
  const { day, selectedDates, viewMode } = calendarMode;
  refetchRequest(
    TaskApi.findAll(
      {
        ...filterValue,
        dateRanges: selectedDates.length
          ? groupConsecutiveDates(selectedDates).map((range) => {
              return {
                fromDate: range.at(0)?.startOf('date').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
                toDate: range
                  .at(range.length - 1)
                  ?.endOf('date')
                  .format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
              };
            })
          : filterValue.dateRanges,
      },
      { pageSize: DEFAULT_TASK_PAGE_SIZE },
    ),
  );
  if (viewMode === ViewModeEnum.MONTH) {
    refetchRequest(
      TaskApi.countTask({
        ...filterValue,
        dateRanges: [
          {
            fromDate: day.startOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
            toDate: day.endOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
          },
        ],
      }),
    );
  } else if (viewMode === ViewModeEnum.YEAR) {
    range(12).forEach((index) => {
      const monthDay = day.set('month', 0).add(index, 'month');
      refetchRequest(
        TaskApi.countTask({
          ...filterValue,
          dateRanges: [
            {
              fromDate: monthDay.startOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
              toDate: monthDay.endOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
            },
          ],
        }),
      );
    });
  }
};
