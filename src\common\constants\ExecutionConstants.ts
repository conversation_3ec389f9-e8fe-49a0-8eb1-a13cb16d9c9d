import { EnumKey } from '@common/utils/Type';

export enum ExecutionTypeEnum {
  SQL = 'SQL',
  PYTHON = 'PYTHON',
}

export enum ExecutionStatusEnum {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
}

export const HIDDEN_VARIABLE_PLACEHOLDER = '*****';

export const ExecutionTypeLabel: EnumKey<ExecutionTypeEnum> = {
  [ExecutionTypeEnum.PYTHON]: 'Python',
  [ExecutionTypeEnum.SQL]: 'SQL',
};

export type ExecutionStatusInfo = {
  label: string;
  color: string;
  bgColor: string;
};

export const ExecutionStatusInfo: EnumKey<ExecutionStatusEnum, ExecutionStatusInfo> = {
  [ExecutionStatusEnum.IN_PROGRESS]: {
    label: 'Inprogress',
    color: '--mantine-primary-color-8',
    bgColor: '--mantine-primary-color-2',
  },
  [ExecutionStatusEnum.COMPLETED]: {
    label: 'Completed',
    color: '--mantine-color-green-8',
    bgColor: '--mantine-color-green-2',
  },
  [ExecutionStatusEnum.FAILED]: {
    label: 'Failed',
    color: '--mantine-color-red-8',
    bgColor: '--mantine-color-red-2',
  },
  [ExecutionStatusEnum.CANCELED]: {
    label: 'Canceled',
    color: '--mantine-color-gray-8',
    bgColor: '--mantine-color-gray-2',
  },
};
