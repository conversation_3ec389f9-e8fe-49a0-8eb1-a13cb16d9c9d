import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createEmailComposedFormData } from '@pages/sendEmail/Utils';
import { EmailComposedModel } from '@models/EmailComposedModel';
import { z } from 'zod';
import { EmailConfigModel } from '@models/EmailConfigModel';

export class EmailApi {
  static testConnection(body: EmailConfigModel) {
    return createRequest({
      url: `${BaseURL.emails}/test-connection`,
      method: 'POST',
      data: body,
    });
  }
  static sendEmail(body: EmailComposedModel) {
    return createRequest({
      url: `${BaseURL.email}`,
      method: 'POST',
      data: createEmailComposedFormData(body),
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      schema: z.string(),
    });
  }
}
