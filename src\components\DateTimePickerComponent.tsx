import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { KanbanDateTimePicker } from 'kanban-design-system';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { getEndOfDate, getNowDate } from '@common/utils/DateUtils';
import { DateTimePickerProps } from '@mantine/dates';

type DateTimePickerComponentProps = {
  label?: string;
  value: Dayjs;
  onChange: (value: Dayjs) => void;
} & Omit<DateTimePickerProps, 'label' | 'withSeconds' | 'valueFormat' | 'value' | 'onChange' | 'highlightToday' | 'maxDate'>;

const DateTimePickerComponent: React.FC<DateTimePickerComponentProps> = ({ label, onChange, value, ...otherProps }) => {
  const maxDate = getEndOfDate(getNowDate()).toDate();
  const handleDateChange = (val: Date | null) => {
    if (val) {
      onChange(dayjs(val));
    }
  };
  return (
    <KanbanDateTimePicker
      label={label}
      withSeconds
      valueFormat={DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS}
      value={value.toDate()}
      onChange={handleDateChange}
      highlightToday
      maxDate={maxDate}
      {...otherProps}
    />
  );
};

export default DateTimePickerComponent;
