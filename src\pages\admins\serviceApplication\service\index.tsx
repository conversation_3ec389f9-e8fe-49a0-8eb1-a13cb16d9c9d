import React, { useState, useMemo, useRef, useCallback } from 'react';
import equal from 'fast-deep-equal';
import {
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { BaseService, Service } from '@core/schema';
import { IconFileExport, IconPlus, IconTrash } from '@tabler/icons-react';
import { ServiceApi } from '@api/ServiceApi';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { IconEdit } from '@tabler/icons-react';
import CreateOrUpdateModal from './components/CreateOrUpdateModal';
import DeleteModal from './components/DeleteModal';
import { ServicePaginationRequest } from '@models/ServiceModel';
import { SortType } from '@common/constants/SortType';
import { columns } from './ServiceCommon';
import ExportFileModal from './components/ExportFileModal';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';

export const ManagementServicePage = () => {
  const [tableAffected, setTableAffected] = useState<ServicePaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const [openedModalCreateService, { close: closeModalCreateService, open: openModalCreateService }] = useDisclosure(false);
  const [openedExportFile, { close: closeExportFile, open: openExportFile }] = useDisclosure(false);
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);
  const [rowSelected, setRowSelected] = useState<Service | undefined>(undefined);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const { data: listService, refetch: refetchList } = useFetch(ServiceApi.findAll(tableAffected), {
    showLoading: false,
    placeholderData: (prev) => prev,
  });
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<Service>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const tableViewListRolesProps: KanbanTableProps<BaseService> = useMemo(() => {
    return {
      columns: columns,
      data: listService?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listService?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.serviceManageEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setRowSelected(data);
                      openModalCreateService();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>

              <GuardComponent requirePermissions={[AclPermission.serviceManageDelete]}>
                <KanbanTooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      openModalDel();
                      setRowSelected(data);
                    }}>
                    <IconTrash color='red' />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    handleUpdateTablePagination,
    listService?.data?.content,
    listService?.data?.totalElements,
    openModalCreateService,
    openModalDel,
    tableAffected,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of service'
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <GuardComponent requirePermissions={[AclPermission.serviceManageCreate]}>
              <KanbanButton
                size='xs'
                onClick={() => {
                  setRowSelected(undefined);
                  openModalCreateService();
                }}
                leftSection={<IconPlus />}>
                Create Service
              </KanbanButton>
            </GuardComponent>
            <GuardComponent requirePermissions={[AclPermission.serviceManageExport]}>
              <KanbanButton
                onClick={() => {
                  openExportFile();
                }}
                disabled={listService?.data?.empty}
                size='xs'
                leftSection={<IconFileExport />}>
                Export
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
      <CreateOrUpdateModal opened={openedModalCreateService} onClose={closeModalCreateService} service={rowSelected} refetchList={refetchList} />
      <ExportFileModal opened={openedExportFile} onClose={closeExportFile} tableAffected={tableAffected} />
      <DeleteModal
        serviceName={rowSelected?.name}
        opened={openedModalDel}
        onClose={closePopupDel}
        serviceId={rowSelected?.id}
        refetchList={refetchList}
      />
    </Box>
  );
};
export default ManagementServicePage;
