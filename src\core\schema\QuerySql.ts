import { z } from 'zod';

export const QuerySqlSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  databaseConnectionId: z.number(),
  databaseConnectionName: z.string().optional(),
  groupQuerySqlId: z.number(),
  groupQuerySqlName: z.string().optional(),
  command: z.string(),
  params: z.array(z.string()).optional(),
});

export type QuerySql = z.infer<typeof QuerySqlSchema>;
