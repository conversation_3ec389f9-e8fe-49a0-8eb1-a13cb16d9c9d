import { z } from 'zod';
import { PaginationModelSchema } from './PaginationModel';

export const AlertGroupSearchRequestModelSchema = PaginationModelSchema.extend({
  serviceIds: z.array(z.string()),
  applicationIds: z.array(z.string()),
  content: z.string(),
  recipients: z.array(z.string()),
  alertPriorityConfigIds: z.array(z.string()),
});

export type AlertGroupSearchRequestModel = z.infer<typeof AlertGroupSearchRequestModelSchema>;
