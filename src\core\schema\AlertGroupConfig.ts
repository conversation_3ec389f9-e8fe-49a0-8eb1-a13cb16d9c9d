import { AlertGroupConfigTypeEnum, AlertGroupOutputEnum } from '@common/constants/AlertGroupConfigConstants';
import { z } from 'zod';
import { QueryRuleGroupTypeSchema } from './RuleGroupCondition';
import { ServiceSchema } from './Service';
import { ApplicationSchema } from './Application';

export const AlertGroupConfigSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  alertOutput: z.nativeEnum(AlertGroupOutputEnum),
  deleted: z.boolean(),
  type: z.nativeEnum(AlertGroupConfigTypeEnum),
  ruleGroups: z.array(QueryRuleGroupTypeSchema).optional(),
  customObjectIds: z.array(z.number()).optional(),
  position: z.number(),
  services: z.array(ServiceSchema).optional(),
  applications: z.array(ApplicationSchema).optional(),
  active: z.boolean(),
  customService: ServiceSchema.optional(),
  customApplication: ApplicationSchema.optional(),
  customPriorityConfigId: z.number().optional(),
  customContent: z.string().optional(),
  customRecipient: z.string().optional(),
});

export type AlertGroupConfig = z.infer<typeof AlertGroupConfigSchema>;
