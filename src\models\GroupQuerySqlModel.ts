import { DESCRIPTION_MAX_LENGTH, QUERY_SQL_NAME_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const GroupQuerySqlModelSchema = z.object({
  id: z.number().optional(),
  name: z.string().trim().min(1).max(QUERY_SQL_NAME_MAX_LENGTH),
  description: z.string().max(DESCRIPTION_MAX_LENGTH).optional(),
});
export type GroupQuerySqlModel = z.infer<typeof GroupQuerySqlModelSchema>;
