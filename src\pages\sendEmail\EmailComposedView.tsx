import styles from './EmailComposeView.module.scss';

import React, { useState } from 'react';
import { KanbanButton, KanbanSelect } from 'kanban-design-system';
import { Box, Button, Chip, Flex, Group, Modal, Radio, Text } from '@mantine/core';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import MultiFileInput from '@pages/admins/emailRichTextEditor/MultiFileInput';
import TextEditorTemplate from '@pages/admins/emailRichTextEditor/TextEditorTemplate';
import { IconArrowBack, IconSend } from '@tabler/icons-react';
import { EmailComposedModel } from '@models/EmailComposedModel';
import { EmailApi } from '@api/EmailApi';
import useMutate from '@core/hooks/useMutate';
import { isEmpty } from 'lodash';

type EmailComposedViewModalProps = {
  emailComposed: EmailComposedModel;
  handleBack: (isReset: boolean) => void;
};

const EmailComposedViewPage: React.FC<EmailComposedViewModalProps> = ({ emailComposed, handleBack }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { mutate: sendEmail } = useMutate(EmailApi.sendEmail, {
    onSuccess: () => {
      setIsModalOpen(true);
    },
  });
  const handleRedirect = () => {
    setIsModalOpen(false);
    handleBack(true);
  };
  return (
    <>
      <Box flex={1} p='sm' bg='white'>
        <HeaderTitleComponent
          title={'Email Detail'}
          rightSection={
            <Flex direction='row' gap='xs' align='center'>
              <KanbanSelect disabled data={[emailComposed.emailSender]} value={emailComposed.emailSender} size='xs' mt={'xs'} />
              <KanbanButton
                size='xs'
                variant={'light'}
                onClick={() => {
                  handleBack(false);
                }}
                leftSection={<IconArrowBack />}>
                Back
              </KanbanButton>
              <KanbanButton
                onClick={() => {
                  sendEmail({ ...emailComposed, content: emailComposed.content.replace(/<p>\s*<\/p>/g, '<p>&nbsp;</p>') });
                }}
                size='xs'
                leftSection={<IconSend />}>
                Send
              </KanbanButton>
            </Flex>
          }
        />
        <Box>
          <Box mb='xs' style={{ gap: 'md' }}>
            <Text fw={700}>Template Email</Text>
            <Flex align='center' mt='md' mb='xs'>
              <Text size='sm' mr='md'>
                Select sending type:
              </Text>
              <Radio.Group value={emailComposed.isOneEmail ? 'true' : 'false'}>
                <Group>
                  <Radio value='true' label='Send an email' />
                  <Radio value='false' label='Send multiple emails' />
                </Group>
              </Radio.Group>
            </Flex>
            <Flex align='center' justify='space-between' gap='xs'>
              <Flex>
                <Text> To</Text>
              </Flex>
              <Flex direction='column' gap='xs' className={styles.containerBox}>
                {emailComposed.partners?.map((partner) => (
                  <Flex key={partner.name} align='center' gap='xs'>
                    <Text c={'var(--mantine-color-primary-9)'} fw={700} size='xs'>
                      {partner.name}
                    </Text>
                    <Group gap={'xs'}>
                      {partner.addresses.map((address, index) => (
                        <Chip size='xs' key={index} checked={false}>
                          {address}
                        </Chip>
                      ))}
                    </Group>
                  </Flex>
                ))}
                <Flex align='center' gap='xs'>
                  <Group>
                    {emailComposed.to?.map((address, index) => (
                      <Chip size='xs' key={index} checked={false}>
                        {address}
                      </Chip>
                    ))}
                  </Group>
                </Flex>
              </Flex>
            </Flex>
          </Box>
          {!isEmpty(emailComposed.cc) && (
            <Box mb='xs'>
              <Flex align='center' justify='space-between' gap='xs'>
                <Flex>
                  <Text>CC</Text>
                </Flex>
                <Flex direction='column' gap='xs' className={styles.containerBox}>
                  <Group>
                    {emailComposed.cc?.map((address, index) => (
                      <Chip size='xs' key={index} checked={false}>
                        {address}
                      </Chip>
                    ))}
                  </Group>
                </Flex>
              </Flex>
            </Box>
          )}
          <Box mb='xs'>
            <Flex align='center' justify='space-between' gap='xs'>
              <Flex>
                <Text>Subject</Text>
              </Flex>
              <Flex direction='column' gap='xs' className={styles.containerBox}>
                <Group>{emailComposed.subject}</Group>
              </Flex>
            </Flex>
          </Box>
          {(!isEmpty(emailComposed.files) || !isEmpty(emailComposed.fileStorages)) && (
            <MultiFileInput files={emailComposed.files || []} isViewMode={true} fileStorages={emailComposed.fileStorages || []} />
          )}
          <TextEditorTemplate isViewMode={true} value={emailComposed.content} />
        </Box>
      </Box>
      <Modal opened={isModalOpen} onClose={() => setIsModalOpen(false)} title='Email Sent' centered>
        <Text>Email sent successfully!</Text>
        <Button onClick={handleRedirect} mt='md'>
          OK
        </Button>
      </Modal>
    </>
  );
};
export default EmailComposedViewPage;
