import React, { useCallback, useMemo, useRef } from 'react';
import { ColumnType, KanbanTableProps, KanbanTableSelectHandleMethods, KanbanText } from 'kanban-design-system';
import { ActionIcon, Box, Switch, Tooltip } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import Table from '@components/table';
import useMutate from '@core/hooks/useMutate';

import { DatabaseConnection } from '@core/schema/DatabaseConnection';
import { DatabaseConnectionApi } from '@api/DatabaseConnectionApi';
import DatabaseConnectionModal from './DatabaseConnectionModal';
import { IconTrash } from '@tabler/icons-react';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';

const columns: ColumnType<DatabaseConnection>[] = [
  {
    title: 'Name',
    name: 'name',
  },
  {
    name: 'description',
    title: 'Description',
  },
  {
    name: 'type',
    title: 'Type',
  },
  {
    name: 'host',
    title: 'Host',
  },
  {
    name: 'port',
    title: 'Port',
  },
  {
    name: 'userName',
    title: 'UserName',
  },
];
export const DatabaseConnectionPage = () => {
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const { data: lstData, refetch: refetchList } = useFetch(DatabaseConnectionApi.findAll());
  const { mutate: deleteByIdMutate } = useMutate(DatabaseConnectionApi.deleteById, {
    successNotification: 'Deleted successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const { mutate: inactiveMutate } = useMutate(DatabaseConnectionApi.inactiveById, {
    successNotification: {
      title: 'Inactive database connection',
      message: `Inactive database connection success`,
    },
    confirm: { title: 'Confirm inactive', children: <KanbanText>Are you sure inactive database connection?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      refetchList();
    },
  });

  const { mutate: activeMutate } = useMutate(DatabaseConnectionApi.activeById, {
    successNotification: {
      title: 'Active database connection',
      message: `Active database connection success`,
    },
    confirm: { title: 'Confirm active', children: <KanbanText>Are you sure active database connection?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      refetchList();
    },
  });

  /**
   * custom search.
   */
  const onSearched = useCallback((datas: DatabaseConnection[], search: string): DatabaseConnection[] => {
    const lowerCaseSearch = search.toLowerCase();
    return datas.filter((item) => {
      const name = item.name || '';
      const description = item.description || '';
      const type = `${item.type}` || '';
      const host = item.host || '';
      const port = `${item.port}` || '';
      const userName = item.userName || '';
      if (
        name.toLowerCase().includes(lowerCaseSearch) ||
        description.toLowerCase().includes(lowerCaseSearch) ||
        type.toLowerCase().includes(lowerCaseSearch) ||
        host.toLowerCase().includes(lowerCaseSearch) ||
        port.toLowerCase().includes(lowerCaseSearch) ||
        userName.toLowerCase().includes(lowerCaseSearch)
      ) {
        return true;
      }
      return false;
    });
  }, []);

  const tableViewListRolesProps: KanbanTableProps<DatabaseConnection> = useMemo(() => {
    return {
      columns: columns,
      data: lstData?.data || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        onSearched: onSearched,
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.databaseConnectionEdit]}>
                <Tooltip label={data.isActive ? 'Active' : 'InActive'}>
                  <Switch
                    checked={data.isActive}
                    onClick={() => {
                      data.isActive ? inactiveMutate(data.id) : activeMutate(data.id);
                    }}
                  />
                </Tooltip>
                <DatabaseConnectionModal data={data} refetchList={refetchList} />
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.databaseConnectionDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [activeMutate, deleteByIdMutate, inactiveMutate, lstData?.data, onSearched, refetchList]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <GuardComponent requirePermissions={[AclPermission.databaseConnectionCreate]}>
        <DatabaseConnectionModal refetchList={refetchList} />
      </GuardComponent>

      <Table ref={tableRef} {...tableViewListRolesProps} />
    </Box>
  );
};
export default DatabaseConnectionPage;
