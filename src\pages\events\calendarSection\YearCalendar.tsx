import React, { useContext } from 'react';
import { SimpleGrid, Stack, Title } from '@mantine/core';
import { range } from 'lodash';
import MonthCalendar from './MonthCalendar';
import { EventPageContext } from '../EventPageContext';

interface Props {
  scrollRef: React.RefObject<HTMLDivElement>;
}

const YearCalendar = ({ scrollRef }: Props) => {
  const { calendarMode } = useContext(EventPageContext);
  return (
    <SimpleGrid cols={{ lg: 1, xl: 2 }}>
      {range(12).map((index) => {
        const monthDay = calendarMode.day.set('month', 0).add(index, 'month');
        return (
          <Stack key={index} gap='xs'>
            <Title order={5}>{monthDay.format('MMMM')}</Title>
            <MonthCalendar scrollRef={scrollRef} month={monthDay.month()} />
          </Stack>
        );
      })}
    </SimpleGrid>
  );
};

export default YearCalendar;
