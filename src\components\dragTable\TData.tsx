import React, { memo } from 'react';
import { Table as MantineTable } from '@mantine/core';
import { TDataProps } from './Types';
import { getCustomTdProps, renderCell } from './Utils';

function TData<T extends object>({ column, customProps, index, record }: TDataProps<T>) {
  return (
    <MantineTable.Td p='xs' {...getCustomTdProps(record, index, customProps?.td)}>
      {renderCell(record, column.render)}
    </MantineTable.Td>
  );
}

export default memo(TData) as <T extends object>(props: TDataProps<T>) => JSX.Element;
