import { ConditionOperatorEnum } from '@common/constants/ConditionOperatorEnum';
import { z } from 'zod';

export const BaseDatabaseThresholdConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  databaseConnectionName: z.string(),
  contentJson: z.string(),
  cronTime: z.string(),
  serviceName: z.string(),
  applicationName: z.string(),
  priorityConfigName: z.string().optional(),
  recipient: z.string(),
  active: z.boolean().optional(),
});
export type BaseDatabaseThresholdConfig = z.infer<typeof BaseDatabaseThresholdConfigSchema>;
export const DatabaseThresholdConfigSchema = BaseDatabaseThresholdConfigSchema.extend({
  databaseConnectionId: z.number(),
  serviceId: z.string(),
  applicationId: z.string(),
  priorityId: z.number(),
  content: z.string(),
  conditionValue: z.number(),
  conditionOperator: z.nativeEnum(ConditionOperatorEnum),
  sqlCommand: z.string(),
});

export type DatabaseThresholdConfig = z.infer<typeof DatabaseThresholdConfigSchema>;
