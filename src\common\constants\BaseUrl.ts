import { getConfigs } from '@core/configs/Configs';

const config = getConfigs();

export const DEFAULT_VERSION = 'v1';
export const SERVER_URL = `${config.buildApiBaseUrl('server')}`;
export const NOTIFICATION_URL = `${config.buildApiBaseUrl('external-notification')}`;
export const EXTERNAL_MAIL_URL = `${config.buildApiBaseUrl('external-mail')}/${DEFAULT_VERSION}`;
export const EXTERNAL_DATABASE_URL = `${config.buildApiBaseUrl('external-database')}/${DEFAULT_VERSION}`;
export const EXTERNAL_JOB_URL = `${config.buildApiBaseUrl('external-job')}/${DEFAULT_VERSION}`;
export const BaseURL = {
  //Server API
  application: `${SERVER_URL}/${DEFAULT_VERSION}/applications`,
  note: `${SERVER_URL}/${DEFAULT_VERSION}/notes`,
  service: `${SERVER_URL}/${DEFAULT_VERSION}/services`,
  user: `${SERVER_URL}/${DEFAULT_VERSION}/systems/users`,
  webHook: `${SERVER_URL}/${DEFAULT_VERSION}/admin/webhooks`,
  role: `${SERVER_URL}/${DEFAULT_VERSION}/admin/roles`,
  alert: `${SERVER_URL}/${DEFAULT_VERSION}/alerts`,
  customObject: `${SERVER_URL}/${DEFAULT_VERSION}/admin/custom-objects`,
  emailConfig: `${SERVER_URL}/${DEFAULT_VERSION}/admin/email-configs`,
  databaseThreshold: `${SERVER_URL}/${DEFAULT_VERSION}/admin/database-thresholds`,
  collectEmail: `${SERVER_URL}/${DEFAULT_VERSION}/admin/collect-emails`,
  priority: `${SERVER_URL}/${DEFAULT_VERSION}/admin/priority-configs`,
  partnerManagement: `${SERVER_URL}/${DEFAULT_VERSION}/admin/email-config/partners`,
  emailTemplate: `${SERVER_URL}/${DEFAULT_VERSION}/admin/email-config/templates`,
  groupConfig: `${SERVER_URL}/${DEFAULT_VERSION}/admin/group-configs`,
  alertGroup: `${SERVER_URL}/${DEFAULT_VERSION}/alert-groups`,
  email: `${SERVER_URL}/${DEFAULT_VERSION}/email`,
  superiors: `${SERVER_URL}/${DEFAULT_VERSION}/admin/superiors`,
  maintenanceTime: `${SERVER_URL}/${DEFAULT_VERSION}/admin/maintenance-times`,
  filterAlert: `${SERVER_URL}/${DEFAULT_VERSION}/admin/filter-alerts`,
  modifyAlert: `${SERVER_URL}/${DEFAULT_VERSION}/admin/modify-alerts`,
  auth: `${SERVER_URL}/${DEFAULT_VERSION}/authenticate`,

  //External Notification
  notification: `${NOTIFICATION_URL}/${DEFAULT_VERSION}/notifications`,

  //External Database API
  databaseConnection: `${SERVER_URL}/${DEFAULT_VERSION}/admin/database-connections`,
  databaseCollect: `${SERVER_URL}/${DEFAULT_VERSION}/admin/database-collects`,
  //External Mail API
  emails: `${EXTERNAL_MAIL_URL}/emails`,
  task: `${SERVER_URL}/${DEFAULT_VERSION}/tasks`,
  telegramAlert: `${SERVER_URL}/${DEFAULT_VERSION}/admin/telegram-alert-configs`,
  teamsAlert: `${SERVER_URL}/${DEFAULT_VERSION}/admin/teams-alert-configs`,
  sysLog: `${SERVER_URL}/${DEFAULT_VERSION}/admin/sys-logs`,
  exportData: `${SERVER_URL}/${DEFAULT_VERSION}/export-datas`,
  // Execution URL
  execution: `${SERVER_URL}/${DEFAULT_VERSION}/admin/executions`,
  executionGroup: `${SERVER_URL}/${DEFAULT_VERSION}/admin/execution-groups`,
  variable: `${SERVER_URL}/${DEFAULT_VERSION}/admin/variables`,
  executionHistory: `${SERVER_URL}/${DEFAULT_VERSION}/admin/execution-histories`,
};
