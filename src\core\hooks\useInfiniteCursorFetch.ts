import { callRequest, RequestConfig } from '../api/BaseApi';
import { InfiniteData, QueryKey, useInfiniteQuery, UseInfiniteQueryOptions } from '@tanstack/react-query';
import { getQueryKey } from '@common/utils/QueryUtils';
import { AppConfig } from './AppConfigTypes';
import useConfigNotification from './useConfigNotification';
import { CursorPage, ResponseData } from '@core/schema';
import { useMemo } from 'react';
import { CursorPagingRequestModel } from '@models/CursorPagingRequestModel';

export type InfiniteFetchAppConfig<TQueryFnData extends ResponseData<CursorPage<any, any>>> = Omit<AppConfig<TQueryFnData>, 'successNotification'> &
  Omit<
    UseInfiniteQueryOptions<TQueryFnData, Error, InfiniteData<TQueryFnData, any>, TQueryFnData, QueryKey, any>,
    'queryKey' | 'queryFn' | 'getNextPageParam' | 'getPreviousPageParam' | 'initialPageParam'
  >;

function useInfiniteCursorFetch<
  Response extends ResponseData<CursorPage<any, any>>,
  SearchParam extends CursorPagingRequestModel<any>,
  Data = unknown,
>(requestConfig: RequestConfig<Response, SearchParam, Data>, appConfig?: InfiniteFetchAppConfig<Response>) {
  const { errorNotification, showLoading, throwParsedError = true, withSignal = true, ...otherConfig } = appConfig || {};
  const queryResult = useInfiniteQuery<Response, Error, InfiniteData<Response, any>, QueryKey, any>({
    queryKey: getQueryKey(requestConfig),
    queryFn: ({ pageParam, signal }) => {
      return callRequest(
        { ...requestConfig, params: { ...requestConfig.params, ...pageParam }, signal: withSignal ? signal : undefined },
        { showLoading, throwParsedError },
      );
    },
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => {
      return lastPage.data?.nextCursor;
    },
    ...otherConfig,
  });
  useConfigNotification(queryResult, { errorNotification });
  const { data } = queryResult;
  const flatData: NonNullable<Response['data']>['data'] = useMemo(() => data?.pages?.flatMap((page) => page.data?.data ?? []) ?? [], [data?.pages]);
  return { ...queryResult, flatData };
}

export default useInfiniteCursorFetch;
