import React from 'react';
import { KanbanButton } from 'kanban-design-system';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { areAllArraysEmpty } from '@common/utils/StringUtils';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

type DeleteModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  alertPriorityConfigId?: number;
  alertPriorityConfigName?: string;
};

const DeleteModal: React.FC<DeleteModalProps> = ({ alertPriorityConfigId, alertPriorityConfigName, onClose, opened, refetchList }) => {
  const { data: dependencies, refetch: refetch } = useFetch(AlertPriorityConfigApi.findAllDependenciesById(alertPriorityConfigId || 0), {
    enabled: !!alertPriorityConfigId && opened,
  });
  const dependencyConfig: DependencyItem[] = [
    {
      dependencyEntity: 'webhooks',
      dependencies: dependencies?.data?.webHooks ?? [],
    },
    {
      dependencyEntity: 'collect email configs',
      dependencies: dependencies?.data?.collectEmailConfigs ?? [],
    },
    {
      dependencyEntity: 'collect database configs',
      dependencies: dependencies?.data?.databaseCollects ?? [],
    },
    {
      dependencyEntity: 'alert group configs',
      dependencies: dependencies?.data?.alertGroupConfigs ?? [],
    },
    {
      dependencyEntity: 'maintenance time configs',
      dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
    },
    {
      dependencyEntity: 'modify alert configs',
      dependencies: dependencies?.data?.modifyAlertConfigs ?? [],
    },
    {
      dependencyEntity: 'database threshold configs',
      dependencies: dependencies?.data?.databaseThresholdConfigs ?? [],
    },
  ];
  const { mutate: deleteByIdMutate } = useMutate(AlertPriorityConfigApi.delete, {
    successNotification: 'Delete Priority Config success',
    errorNotification: 'Delete Priority Config failed!',
    onSuccess: () => {
      refetchList();
      refetch();
      onClose();
    },
  });
  const isDisabledButtonConfirm = !areAllArraysEmpty(dependencies?.data || {});
  return (
    <Modal
      size='xl'
      opened={opened}
      onClose={() => {
        onClose();
      }}
      title={'Delete Alert priority config'}
      actions={
        <KanbanButton onClick={() => deleteByIdMutate(alertPriorityConfigId ?? 0)} disabled={isDisabledButtonConfirm}>
          Confirm
        </KanbanButton>
      }>
      <DependenciesWarningAlert
        mainEntity={`Alert priority config ${alertPriorityConfigName}`}
        dependencyConfigs={dependencyConfig}
        isDeleted={true}
      />
      {!isDisabledButtonConfirm && <DeleteConfirmMessage name={alertPriorityConfigName} />}
    </Modal>
  );
};

export default DeleteModal;
