import type { AclPermission } from 'models/AclPermission';
import { ProtectedComponent, ProtectedRouteProps } from '@core/auth/hocs/ProtectedComponent';
import React from 'react';
import FobiddenComponent from './ForbiddenComponent';

export type GuardComponentType = Omit<ProtectedRouteProps, 'errorElement' | 'requirePermissions' | 'userPermissions'> & {
  requirePermissions: AclPermission[];
};

export const GuardComponent = (props: GuardComponentType) => {
  return (
    <>
      <ProtectedComponent
        errorElement={<FobiddenComponent />}
        requirePermissions={props.requirePermissions}
        hiddenOnUnauthorized={props.hiddenOnUnauthorized}>
        {props.children}
      </ProtectedComponent>
    </>
  );
};

export default GuardComponent;
