import React from 'react';

const EmptyBox = () => {
  return (
    <svg width='200' height='160' viewBox='0 0 200 160' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <rect width='200' height='160' fill='white' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M50.4 107.2H123.2C123.612 107.2 124.013 107.156 124.4 107.071C124.787 107.156 125.188 107.2 125.6 107.2H167.2C170.293 107.2 172.8 104.693 172.8 101.6C172.8 98.5074 170.293 96.0002 167.2 96.0002H162.4C159.307 96.0002 156.8 93.493 156.8 90.4002C156.8 87.3074 159.307 84.8002 162.4 84.8002H177.6C180.693 84.8002 183.2 82.293 183.2 79.2002C183.2 76.1074 180.693 73.6002 177.6 73.6002H160C163.093 73.6002 165.6 71.093 165.6 68.0002C165.6 64.9074 163.093 62.4002 160 62.4002H108.8C111.893 62.4002 114.4 59.893 114.4 56.8002C114.4 53.7074 111.893 51.2002 108.8 51.2002H63.2C60.1072 51.2002 57.6 53.7074 57.6 56.8002C57.6 59.893 60.1072 62.4002 63.2 62.4002H31.2C28.1072 62.4002 25.6 64.9074 25.6 68.0002C25.6 71.093 28.1072 73.6002 31.2 73.6002H51.2C54.2928 73.6002 56.8 76.1074 56.8 79.2002C56.8 82.293 54.2928 84.8002 51.2 84.8002H19.2C16.1072 84.8002 13.6 87.3074 13.6 90.4002C13.6 93.493 16.1072 96.0002 19.2 96.0002H50.4C47.3072 96.0002 44.8 98.5074 44.8 101.6C44.8 104.693 47.3072 107.2 50.4 107.2ZM180.8 107.2C183.893 107.2 186.4 104.693 186.4 101.6C186.4 98.5074 183.893 96.0002 180.8 96.0002C177.707 96.0002 175.2 98.5074 175.2 101.6C175.2 104.693 177.707 107.2 180.8 107.2Z'
        fill='#F3F7FF'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M90.4949 89.8457C90.4323 90.2883 90.4 90.7406 90.4 91.2004C90.4 96.5023 94.6981 100.8 100 100.8C105.302 100.8 109.6 96.5023 109.6 91.2004C109.6 90.7406 109.568 90.2883 109.505 89.8457H132.8V111.2C132.8 112.526 131.725 113.6 130.4 113.6H69.6C68.2745 113.6 67.2 112.526 67.2 111.2V89.8457H90.4949Z'
        fill='white'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M110.4 89.5998C110.4 95.3436 105.744 99.9998 100 99.9998C94.2562 99.9998 89.6 95.3436 89.6 89.5998C89.6 89.4137 89.6049 89.2288 89.6145 89.0451H67.2L74.8484 66.4309C75.1781 65.456 76.0927 64.7998 77.1219 64.7998H122.878C123.907 64.7998 124.822 65.456 125.152 66.4309L132.8 89.0451H110.385C110.395 89.2288 110.4 89.4137 110.4 89.5998Z'
        fill='white'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M108.878 90.3636C108.878 94.802 104.903 99.2 100 99.2C95.0968 99.2 91.1219 94.802 91.1219 90.3636C91.1219 90.2198 91.1261 89.2769 91.1344 89.135H74.4L80.9291 73.2604C81.2105 72.507 81.9913 72 82.8699 72H117.13C118.009 72 118.789 72.507 119.071 73.2604L125.6 89.135H108.866C108.874 89.2769 108.878 90.2198 108.878 90.3636Z'
        fill='#E8F0FE'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M68.2 89.2096V110.4C68.2 111.173 68.8268 111.8 69.6 111.8H130.4C131.173 111.8 131.8 111.173 131.8 110.4V89.2096L124.204 66.7513C124.012 66.1826 123.479 65.7998 122.878 65.7998H77.1219C76.5215 65.7998 75.988 66.1826 75.7957 66.7513L68.2 89.2096Z'
        stroke='#1F64E7'
        strokeWidth='2.5'
      />
      <path
        d='M78.4 88.7998C81.5493 88.7998 84.9479 88.7998 88.5959 88.7998C90.097 88.7998 90.097 89.8547 90.097 90.3998C90.097 95.7017 94.4939 99.9998 99.9178 99.9998C105.342 99.9998 109.739 95.7017 109.739 90.3998C109.739 89.8547 109.739 88.7998 111.24 88.7998H131.2M72.459 88.7998H74.4H72.459Z'
        stroke='#1F64E7'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M120.08 46.6424L111.2 56.6049M99.2797 43.2002V56.6049V43.2002ZM78.4 46.6424L87.2797 56.6049L78.4 46.6424Z'
        stroke='#75A4FE'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};

export default EmptyBox;
