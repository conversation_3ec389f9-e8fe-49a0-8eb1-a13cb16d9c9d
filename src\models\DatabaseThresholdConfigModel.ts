import { z } from 'zod';
import { MAX_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_RECIPIENT_LENGTH } from '@common/constants/ValidationConstant';
import { ConditionOperatorEnum } from '@common/constants/ConditionOperatorEnum';
import { parseCronString } from 'cron-job/ConverterSelect';
export const DatabaseThresholdConfigModelSchema = z
  .object({
    id: z.string().optional(),
    name: z.string().trim().min(1).max(MAX_NAME_LENGTH),
    description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
    databaseConnectionId: z.number().min(1),
    cronTime: z.string().min(1),
    serviceId: z.string().min(1),
    applicationId: z.string().min(1),
    priorityId: z.number(),
    conditionValue: z.number(),
    conditionOperator: z.nativeEnum(ConditionOperatorEnum),
    content: z.string().trim().min(1),
    contentJson: z.string().trim().min(1),
    recipient: z.string().trim().min(1).max(MAX_RECIPIENT_LENGTH),
    sqlCommand: z.string().trim().min(1),
    active: z.boolean(),
  })
  .superRefine((value, ctx) => {
    try {
      parseCronString(value.cronTime);
    } catch (error) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['cronTime'],
        message: (error as Error).message,
      });
    }
    const sqlCommandCleaned = value.sqlCommand.replace(/\s+/g, ' ').toUpperCase();
    if (!sqlCommandCleaned.startsWith('SELECT COUNT')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['sqlCommand'],
        message: 'SQL command must start with "SELECT COUNT".',
      });
    }
  });

export type DatabaseThresholdConfigModel = z.infer<typeof DatabaseThresholdConfigModelSchema>;
