import React, { useState, useMemo, useRef, useCallback } from 'react';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { ActionIcon, Box, Flex } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { IconEye, IconPlus, IconTrash } from '@tabler/icons-react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { IconEdit } from '@tabler/icons-react';
import { SortType } from '@common/constants/SortType';
import { PaginationRequest } from '@api/Type';
import useMutate from '@core/hooks/useMutate';
import { EmailTemplateApi } from '@api/EmailTemplateApi';
import { EmailTemplate } from '@core/schema/EmailTemplate';
import { createSearchParams, useNavigate } from 'react-router-dom';
import { RawValuesChipDisplay } from '@components/RawValuesChipDisplay';
import { DEFAULT_DEBOUNCE_TIME } from '@common/constants/ValidationConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';

const columns: ColumnType<EmailTemplate>[] = [
  {
    title: 'Name',
    name: 'name',
  },
  {
    title: 'Subject',
    name: 'subject',
  },
  {
    title: 'Description',
    name: 'description',
  },
  {
    name: 'to',
    title: 'To',
    customRender: (_, record: EmailTemplate) => <RawValuesChipDisplay values={record.to ?? []} />,
  },
  {
    name: 'cc',
    title: 'CC',
    customRender: (_, record: EmailTemplate) => <RawValuesChipDisplay values={record.cc ?? []} />,
  },
];

export enum EmailTemplateConfigAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  VIEW = 'VIEW',
}

export const TemplateManagement = () => {
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const { data: listEmailTemplate, refetch: refetchList } = useFetch(EmailTemplateApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const navigate = useNavigate();
  const { mutate: deleteByIdMutate } = useMutate(EmailTemplateApi.deleteById, {
    successNotification: 'Deleted successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<EmailTemplate>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );

  const handleChangeURL = useCallback(
    (param: number, value: string | null) => {
      navigate({
        pathname: `${ROUTE_PATH.EMAIL_TEMPLATE}/${param}`,
        search: createSearchParams({
          action: value || EmailTemplateConfigAction.CREATE,
        }).toString(),
      });
    },
    [navigate],
  );

  const tableViewListRolesProps: KanbanTableProps<EmailTemplate> = useMemo(() => {
    return {
      columns: columns,
      data: listEmailTemplate?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listEmailTemplate?.data?.totalElements ?? 0,
        onTableAffected: handleUpdateTablePagination,
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <KanbanTooltip label='View'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    handleChangeURL(data.id, EmailTemplateConfigAction.VIEW);
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </KanbanTooltip>
              <GuardComponent requirePermissions={[AclPermission.emailTemplateEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      handleChangeURL(data.id, EmailTemplateConfigAction.UPDATE);
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailTemplateDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [deleteByIdMutate, handleChangeURL, handleUpdateTablePagination, listEmailTemplate?.data?.content, listEmailTemplate?.data?.totalElements]);

  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='Template management'
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <GuardComponent requirePermissions={[AclPermission.emailTemplateCreate]}>
              <KanbanButton
                size='xs'
                onClick={() => {
                  handleChangeURL(0, EmailTemplateConfigAction.CREATE);
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
    </Box>
  );
};
export default TemplateManagement;
