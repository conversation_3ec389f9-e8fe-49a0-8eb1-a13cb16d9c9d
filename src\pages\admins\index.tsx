import React from 'react';
import { Outlet, useMatch, useNavigate, useResolvedPath } from 'react-router-dom';
import { Box, Container, Flex, NavLink } from '@mantine/core';
import { AdminSelectionConfigs, AdminSelectionConfigType } from './AdminSelectionConfigs';
import classes from './AdminViewPage.module.scss';
import clsx from 'clsx';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';

interface RenderNavLinkProps {
  config: AdminSelectionConfigType;
  level?: number;
}

const RenderNavLink: React.FC<RenderNavLinkProps> = ({ config, level = 0 }) => {
  const navigate = useNavigate();
  const { pathname } = useResolvedPath(config.path);

  const handleClick = (path: string) => {
    if (path && !config.children) {
      navigate(path);
    }
  };

  const isActive = !!useMatch({ path: pathname, end: false });

  return (
    <Box ml={level * 10}>
      <NavLink
        className={clsx({
          [classes.navLink]: true,
          [classes.active]: isActive,
          [classes.firstActiveLevel]: isActive && level === 0,
        })}
        label={config.title}
        leftSection={<config.icon size='1rem' />}
        onClick={() => handleClick(config.path)}
        childrenOffset={0}>
        {config.children &&
          config.children.map(
            (child: AdminSelectionConfigType, childIndex) =>
              isAnyPermissions(child.requirePermissions) && <RenderNavLink config={child} key={childIndex} level={level + 1} />,
          )}
      </NavLink>
    </Box>
  );
};

const AdminViewPage: React.FC = () => {
  return (
    <Flex h={'var(--kanban-appshell-maxheight-content)'}>
      <Box className={classes.nav}>
        {AdminSelectionConfigs.map((config, index) => isAnyPermissions(config.requirePermissions) && <RenderNavLink config={config} key={index} />)}
      </Box>
      <Container className={classes.content} bg={'white'} flex={'1'} fluid pt={'sm'} ml='sm' pb={'sm'}>
        <Outlet />
      </Container>
    </Flex>
  );
};

export default AdminViewPage;
