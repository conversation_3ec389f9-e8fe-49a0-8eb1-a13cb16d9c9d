import React from 'react';
import { Tooltip, ColorPicker, Popover } from '@mantine/core';
import classes from './Template.module.scss';
import { IconPalette } from '@tabler/icons-react';
import { colors } from './DataSelect';

type CustomColorPickerProps = {
  onChange: (color: string | undefined) => void;
};

export const TextColorPicker: React.FC<CustomColorPickerProps> = ({ onChange }) => {
  return (
    <Popover shadow='xs'>
      <Tooltip label='Color' withArrow position='bottom'>
        <Popover.Target>
          <IconPalette size={30} className={classes.mantineButton} />
        </Popover.Target>
      </Tooltip>
      <Popover.Dropdown p='5px'>
        <ColorPicker
          size='xs'
          format='hex'
          onChange={(e) => {
            onChange(e);
          }}
          swatches={colors}
        />
      </Popover.Dropdown>
    </Popover>
  );
};
