import { TeamsIntervalTypeEnum } from '@common/constants/TeamsConstants';
import {
  TEAMS_CLIENT_ID_MAX_LENGTH,
  TEAMS_CLIENT_SECRET_MAX_LENGTH,
  TEAMS_TENANT_ID_MAX_LENGTH,
  TEAMS_EMAIL_MAX_LENGTH,
  TEAMS_PASSWORD_MAX_LENGTH,
  TEAMS_MESSAGE_TEMPLATE_MAX_LENGTH,
  TEAMS_INTERVAL_MAX_LENGTH,
  TEAMS_MESSAGE_CONTENT_MAX_LENGTH,
  TEAMS_MAX_CONTACTS,
} from '@common/constants/ValidationConstant';
import { z, ZodIssueCode } from 'zod';

// Helper function to check if a string value is present and not just whitespace
const isPresent = (value: string | undefined | null): boolean => {
  return typeof value === 'string' && value.trim() !== '';
};

export const TeamsModelSchema = z
  .object({
    id: z.string().optional(),
    // Make all credential fields optional at the base schema level
    clientId: z.string().trim().max(TEAMS_CLIENT_ID_MAX_LENGTH).optional(),
    clientSecret: z.string().trim().max(TEAMS_CLIENT_SECRET_MAX_LENGTH).optional(),
    tenantId: z.string().trim().max(TEAMS_TENANT_ID_MAX_LENGTH).optional(),
    username: z.string().trim().max(TEAMS_EMAIL_MAX_LENGTH).email({ message: 'Invalid email format' }).optional(),
    password: z.string().trim().max(TEAMS_PASSWORD_MAX_LENGTH).optional(),

    // --- Other fields remain the same ---
    messageTemplate: z
      .string()
      .trim()
      .max(TEAMS_MESSAGE_TEMPLATE_MAX_LENGTH)
      .refine(
        (val) => {
          if (!val) {
            return true;
          } // Optional is valid
          try {
            JSON.parse(val);
            return true;
          } catch (e) {
            return false;
          }
        },
        { message: 'Invalid JSON format' },
      ),
    interval: z.string().trim().max(TEAMS_INTERVAL_MAX_LENGTH),
    intervalType: z.nativeEnum(TeamsIntervalTypeEnum),
  })
  .superRefine((data, ctx) => {
    const credentialFields = ['clientId', 'clientSecret', 'tenantId', 'username', 'password'] as const;
    const presentStates = credentialFields.map((field) => isPresent(data[field]));
    const presentCount = presentStates.filter(Boolean).length;

    // --- Rule 1: All credentials must be present or all must be absent ---
    if (presentCount > 0 && presentCount < credentialFields.length) {
      // Some are present, some are missing - this is invalid
      credentialFields.forEach((field, index) => {
        if (!presentStates[index]) {
          // If this specific field is missing
          ctx.addIssue({
            code: ZodIssueCode.custom,
            message: `This field is required when other credential fields (Client ID, Tenant ID, etc.) are provided.`,
            path: [field],
          });
        }
      });
    }

    // --- Rule 2: If creating (no id), credentials MUST be present ---
    // This combines with Rule 1: if no id, presentCount must be 5. If it's 0, this rule adds errors.
    if (!data.id && presentCount === 0) {
      credentialFields.forEach((field) => {
        // Determine the appropriate message based on the field
        let message = 'This field is required for new configurations.';
        if (field === 'clientId') {
          message = 'Client ID is required for new configurations.';
        }
        if (field === 'clientSecret') {
          message = 'Client Secret is required for new configurations.';
        }
        if (field === 'tenantId') {
          message = 'Tenant ID is required for new configurations.';
        }
        if (field === 'username') {
          message = 'Email is required for new configurations.';
        }
        if (field === 'password') {
          message = 'Password is required for new configurations.';
        }

        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: message,
          path: [field],
        });
      });
    }

    if (data.intervalType === TeamsIntervalTypeEnum.EVERY_X_MINUTES) {
      if (!isPresent(data.interval)) {
        ctx.addIssue({
          message: 'Hours interval is required when sync every X hours is selected',
          path: ['interval'],
          code: ZodIssueCode.custom,
        });
      } else {
        const hours = Number(data.interval);
        if (isNaN(hours) || !Number.isInteger(hours) || hours < 1 || hours > 24) {
          ctx.addIssue({
            message: 'Hours must be an integer between 1 and 24',
            path: ['interval'],
            code: ZodIssueCode.custom,
          });
        }
      }
    }

    // --- Existing messageTemplate Validation ---
    if (isPresent(data.messageTemplate)) {
      try {
        JSON.parse(data.messageTemplate as string);
      } catch (e) {
        ctx.addIssue({
          message: 'Invalid JSON format in message template',
          path: ['messageTemplate'],
          code: ZodIssueCode.custom,
        });
      }
    }
  });

// --- TeamsSendMessageSchema remains unchanged ---
export const TeamsSendMessageSchema = z
  .object({
    message: z
      .string()
      .trim()
      .min(1, { message: 'Message content is required' })
      .max(TEAMS_MESSAGE_CONTENT_MAX_LENGTH, {
        message: `Message content cannot exceed ${TEAMS_MESSAGE_CONTENT_MAX_LENGTH} characters`,
      }),
    contacts: z
      .array(z.string())
      .min(1, { message: 'At least one recipient is required' })
      .max(TEAMS_MAX_CONTACTS, {
        message: `You cannot send to more than ${TEAMS_MAX_CONTACTS} recipients`,
      }),

    isForceSend: z.boolean().optional(),
    invalidEmails: z.array(z.string()).optional(),
    isSend: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.contacts.length > 1) {
      const validPattern = /@mbbank\.com\.vn$/i;
      const invalidContacts = data.contacts.filter((contact) => !validPattern.test(contact));

      if (invalidContacts.length > 0) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: 'You can only send messages to recipients with @mbbank.com.vn format or a single Teams group chat',
          path: ['contacts'],
        });
      }
    }
  });

export type TeamsSendMessageModel = z.infer<typeof TeamsSendMessageSchema>;
export type TeamsModel = z.infer<typeof TeamsModelSchema>;
