import { TaskApi } from '@api/TaskApi';
import useMutate from '@core/hooks/useMutate';
import { Task } from '@core/schema/Task';
import { ActionIcon, Flex } from '@mantine/core';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import React, { useContext, useMemo } from 'react';
import { EventPageContext } from '../EventPageContext';
import { refetchEventPageData } from './Utilts';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { TaskStatusEnum, TaskTypeEnum } from '@common/constants/TaskConstants';
import ViewTaskDetailButton from './ViewTaskDetail';
import { IconEye } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { AclPermission } from '@models/AclPermission';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { useAppSelector } from '@store';
import { getCurrentUser } from '@slices/CurrentUserSlice';

interface Props {
  task: Task;
}

const TaskTableAction = ({ task }: Props) => {
  const { calendarMode, filterValue } = useContext(EventPageContext);
  const navigate = useNavigate();
  const hasEditPermission = useMemo(() => isAnyPermissions([AclPermission.taskEdit]), []);
  const { mutate: deleteTask } = useMutate(TaskApi.deleteTask, {
    onSuccess: () => {
      refetchEventPageData(calendarMode, filterValue);
    },
    confirm: {
      title: 'Delete task',
      children: <DeleteConfirmMessage name={task.name} />,
    },
  });
  const currentUser = useAppSelector(getCurrentUser).userInfo;
  const hasDeletePermission = useMemo(() => isAnyPermissions([AclPermission.taskDelete]), []);
  const isDone = TaskStatusEnum.DONE === task.status;
  const deleteable = !isDone && hasDeletePermission;
  const editable = !isDone && (hasEditPermission || task.createdBy === currentUser?.userName);
  return (
    <Flex>
      {TaskTypeEnum.TASK === task.type ? (
        <ViewTaskDetailButton taskId={task.id} editable={editable} />
      ) : (
        <ActionIcon
          variant='transparent'
          onClick={() =>
            navigate({
              pathname: `${ROUTE_PATH.EVENT}/${task.id}`,
            })
          }>
          {editable ? <IconEdit size={18} /> : <IconEye size={18} />}
        </ActionIcon>
      )}

      {deleteable && (
        <ActionIcon variant='transparent'>
          <IconTrash size={18} onClick={() => deleteTask(task.id)} />
        </ActionIcon>
      )}
    </Flex>
  );
};

export default TaskTableAction;
