import { z } from 'zod';

export const SqlMappingColumnDataSchema = z.object({
  column: z.string(),
  value: z.string().optional(),
});

export const SqlDataMappingSchema = z.object({
  listSqlMappingColumnDatas: z.array(SqlMappingColumnDataSchema),
});

export const SqlExecutionSchema = z.object({
  listColumns: z.array(z.string()),
  listDataMappings: z.array(SqlDataMappingSchema),
  isNonQuery: z.boolean().optional(),
  total: z.number().optional(),
});

export type SqlExecution = z.infer<typeof SqlExecutionSchema>;
export type SqlDataMapping = z.infer<typeof SqlDataMappingSchema>;
export type SqlMappingColumnData = z.infer<typeof SqlMappingColumnDataSchema>;
