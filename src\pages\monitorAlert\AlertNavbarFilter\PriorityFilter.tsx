import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import MultipleSelect from '@components/MultipleSelect';
import useFetch from '@core/hooks/useFetch';
import { ComboboxData } from '@mantine/core';
import { sortBy } from 'lodash';
import React, { useMemo } from 'react';
import classes from './AlertNavbarFilter.module.css';
import { formatSelectCountMessage } from '@common/utils/MessageUtils';
import { Controller, UseFormReturn } from 'react-hook-form';
import { FilterForm } from '../Types';

interface Props {
  form: UseFormReturn<FilterForm>;
}

const PriorityFilter = ({ form }: Props) => {
  const { control } = form;
  const { data: alertPriorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: true }), {
    showLoading: false,
  });
  const priorityConfigOptions: ComboboxData = useMemo(
    () =>
      sortBy(
        alertPriorityConfigs?.data?.map((config) => ({ value: `${config.id || ''}`, label: config.name + (config.deleted ? ' (DELETED)' : '') })) ||
          [],
        (option) => option.label.toLowerCase(),
      ),
    [alertPriorityConfigs?.data],
  );
  return (
    <Controller
      name='alertPriorityConfigIds'
      control={control}
      render={({ field: { onChange, value } }) => (
        <MultipleSelect
          data={priorityConfigOptions}
          classNames={{ pillsList: classes.multipleInput }}
          searchable
          label='Priority'
          placeholder='Select priority'
          checkIconPosition='right'
          onChange={onChange}
          value={value}
          description={formatSelectCountMessage(value?.length || 0)}
        />
      )}
    />
  );
};

export default PriorityFilter;
