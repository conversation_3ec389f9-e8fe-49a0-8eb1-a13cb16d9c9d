import { Stack } from '@mantine/core';
import React, { useContext, useRef } from 'react';
import MonthCalendar from './MonthCalendar';
import CalendarHeader from './CalendarHeader';
import { ViewModeEnum } from '../Contants';
import YearCalendar from './YearCalendar';
import { EventPageContext } from '../EventPageContext';
import classes from './CalendarSection.module.css';

const CalendarSection = () => {
  const { calendarMode } = useContext(EventPageContext);
  const containerRef = useRef<HTMLDivElement>(null);
  return (
    <Stack
      className={classes.calendarWrapper}
      ref={containerRef}
      gap='xs'
      h='var(--kanban-appshell-maxheight-content)'
      style={{ overflow: 'scroll' }}
      bg='white'
      p='sm'>
      <CalendarHeader />
      {calendarMode.viewMode === ViewModeEnum.MONTH ? (
        <MonthCalendar scrollRef={containerRef} month={calendarMode.day.month()} />
      ) : (
        <YearCalendar scrollRef={containerRef} />
      )}
    </Stack>
  );
};

export default CalendarSection;
