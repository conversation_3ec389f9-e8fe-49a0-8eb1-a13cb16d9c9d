import React, { useEffect, useMemo } from 'react';
import { createContext, useState } from 'react';
import { CalendarMode } from './Types';
import { FilterTaskModel } from '@models/FilterTaskModel';
import { DEFAULT_CALENDAR_MODE, DEFAULT_FILTER_VALUE, ViewModeEnum } from './Contants';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { useLocalStorage } from '@mantine/hooks';

export interface EventPageState {
  calendarMode: CalendarMode;
  setCalendarMode: React.Dispatch<React.SetStateAction<CalendarMode>>;
  filterValue: FilterTaskModel;
  setFilterValue: React.Dispatch<React.SetStateAction<FilterTaskModel>>;
}

export const EventPageContext = createContext<EventPageState>({
  calendarMode: DEFAULT_CALENDAR_MODE,
  setCalendarMode: () => {},
  filterValue: DEFAULT_FILTER_VALUE,
  setFilterValue: () => {},
});

interface Props {
  children: React.ReactNode;
}

export const EventPageProvider = ({ children }: Props) => {
  const [calendarMode, setCalendarMode] = useState<CalendarMode>(DEFAULT_CALENDAR_MODE);
  const [filterValue, setFilterValue] = useLocalStorage<FilterTaskModel>({
    key: LocalStorageKey.EVENT_FILTER_KEY,
    defaultValue: DEFAULT_FILTER_VALUE,
    getInitialValueInEffect: false,
  });
  const state = useMemo(() => ({ calendarMode, setCalendarMode, filterValue, setFilterValue }), [calendarMode, filterValue, setFilterValue]);
  useEffect(() => {
    const { day, viewMode } = calendarMode;
    setFilterValue((prev) => ({
      ...prev,
      dateRanges: [
        {
          fromDate: day.startOf(viewMode === ViewModeEnum.MONTH ? 'month' : 'year').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
          toDate: day.endOf(viewMode === ViewModeEnum.MONTH ? 'month' : 'year').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
        },
      ],
    }));
  }, [calendarMode, calendarMode.day, calendarMode.viewMode, setFilterValue]);
  return <EventPageContext.Provider value={state}>{children}</EventPageContext.Provider>;
};
