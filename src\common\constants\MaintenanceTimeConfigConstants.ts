export enum MaintenanceTimeTypeEnum {
  NEXT_TIME = 'NEXT_TIME',
  FROM_TIME_TO_TIME = 'FROM_TIME_TO_TIME',
  CRON_JOB = 'CRON_JOB',
}

export const TIME_TYPE_LABEL: { [key in MaintenanceTimeTypeEnum]: string } = {
  [MaintenanceTimeTypeEnum.NEXT_TIME]: 'Next time',
  [MaintenanceTimeTypeEnum.FROM_TIME_TO_TIME]: 'From time to time',
  [MaintenanceTimeTypeEnum.CRON_JOB]: 'Cron job',
};

export enum TimeUnitEnum {
  SECOND = 'SECOND',
  MINUTE = 'MINUTE',
  HOUR = 'HOUR',
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
}

export const TIME_UNIT_LABEL: {
  [key in TimeUnitEnum]: string;
} = {
  [TimeUnitEnum.SECOND]: 'SECOND',
  [TimeUnitEnum.MINUTE]: 'MINUTE',
  [TimeUnitEnum.HOUR]: 'HOUR',
  [TimeUnitEnum.DAY]: 'DAY',
  [TimeUnitEnum.WEEK]: 'WEEK',
  [TimeUnitEnum.MONTH]: 'MONTH',
};
