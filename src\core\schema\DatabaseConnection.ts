import { DATABASE_ORACLE_CONNECT_TYPE, DATABASE_TYPE } from '@common/constants/DatabaseConnectionConstants';
import { z } from 'zod';

export const DatabaseConnectionSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(DATABASE_TYPE),
  host: z.string(),
  port: z.number(),
  userName: z.string(),
  password: z.string().optional(),
  oracleConnectType: z.nativeEnum(DATABASE_ORACLE_CONNECT_TYPE).optional(),
  sid: z.string().optional(),
  serviceName: z.string().optional(),
  databaseName: z.string().optional(),
  isActive: z.boolean(),
});

export type DatabaseConnection = z.infer<typeof DatabaseConnectionSchema>;
