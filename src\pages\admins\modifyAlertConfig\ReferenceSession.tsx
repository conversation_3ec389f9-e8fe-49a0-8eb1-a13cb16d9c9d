import React, { useCallback, useMemo, useState } from 'react';
import ComboboxLoadMore, { ComboboxLoadMoreProps } from '@components/ComboboxLoadMore';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { ApplicationApi } from '@api/ApplicationApi';
import { Application, Service } from '@core/schema';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { ServiceApi } from '@api/ServiceApi';
import { Box } from '@mantine/core';
import { DEFAULT_APPLICATION_PAGINATION_REQUEST, DEFAULT_SERVICE_PAGINATION_REQUEST } from './Constants';
import { ModifyAlertConfigModel } from '@models/ModifyAlertConfigModel';

interface Props {
  form: UseFormReturn<ModifyAlertConfigModel>;
  isViewMode?: boolean;
}

const ReferenceSession = ({ form, isViewMode }: Props) => {
  const { control, setValue } = form;
  const serviceValue = useWatch({ control, name: 'services' });
  const applicationValue = useWatch({ control, name: 'applications' });
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_SERVICE_PAGINATION_REQUEST);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_APPLICATION_PAGINATION_REQUEST);
  const { fetchNextPage: fetchNextPageService, flatData: serviceData } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
  });
  const { fetchNextPage: fetchNextPageApplication, flatData: applicationData } = useInfiniteFetch(
    ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds: serviceValue?.map((service) => service.id) || [] }),
    {
      showLoading: false,
    },
  );

  const onServiceChange = useCallback(
    (values: Service[]) => {
      setValue('services', values || [], { shouldValidate: true });
      setValue(
        'applications',
        applicationValue?.filter((application) => values.some((service) => service.id === application.serviceId)),
        { shouldValidate: true },
      );
    },
    [applicationValue, setValue],
  );

  const renderApplicationPill = useCallback<ComboboxLoadMoreProps<Application>['renderPillLabel']>((application: Application) => {
    return (
      <span>
        {application.serviceName}: {application.name}
      </span>
    );
  }, []);
  const serviceIdsWithAllApp = useMemo(
    () => serviceValue?.filter((service) => !applicationValue?.some((app) => app.serviceId === service.id))?.map((ele) => ele.id) || [],
    [applicationValue, serviceValue],
  );

  return (
    <Box>
      <Controller
        control={control}
        name='services'
        render={({ field }) => {
          return (
            <ComboboxLoadMore
              disabled={isViewMode}
              options={serviceData}
              onChange={onServiceChange}
              label={field.value.length === 0 ? 'Service Name (All Service)' : 'Service Name'}
              placeholder='Search service name'
              onSearch={(val) => setServiceSearchParams((prev) => ({ ...prev, name: val }))}
              onScroll={fetchNextPageService}
              onClickedOption={() => setServiceSearchParams(DEFAULT_SERVICE_PAGINATION_REQUEST)}
              renderPillLabel={(data) => {
                const isServiceWithAllApp = serviceIdsWithAllApp?.some((serviceId) => serviceId === data.id);
                return (
                  <span>
                    {data.name}
                    {isServiceWithAllApp ? <b> (All application)</b> : ''}
                  </span>
                );
              }}
              renderOptionLabel={(data) => data.name}
              values={field.value ?? []}
              scrollableForValue={true}
              onClearValue={() => setValue('applications', [])}
              clearable={!isViewMode}
            />
          );
        }}
      />
      <Controller
        control={control}
        name='applications'
        render={({ field: { onChange, value } }) => {
          return (
            <ComboboxLoadMore
              disabled={isViewMode || serviceValue.length === 0}
              options={applicationData}
              onChange={onChange}
              label='Application Name'
              placeholder='Search application name'
              onSearch={(val) => setApplicationSearchParams((prev) => ({ ...prev, name: val }))}
              onClickedOption={() => setApplicationSearchParams(DEFAULT_APPLICATION_PAGINATION_REQUEST)}
              onScroll={fetchNextPageApplication}
              renderPillLabel={renderApplicationPill}
              renderOptionLabel={(data) => data.name}
              values={value || []}
              groupByKeys={['serviceName']}
              scrollableForValue={true}
              clearable={!isViewMode}
            />
          );
        }}
      />
    </Box>
  );
};

export default ReferenceSession;
