import { TeamsIntervalTypeEnum } from '@common/constants/TeamsConstants';
import { z } from 'zod';

export const TeamsConfigSchema = z.object({
  id: z.string().optional(),
  clientId: z.string().optional(),
  clientSecret: z.string().optional(),
  clientSecretPlaceholder: z.string().optional(),
  tenantId: z.string().optional(),
  username: z.string().optional(),
  password: z.string().optional(),
  messageTemplate: z.string().optional(),
  interval: z.string().optional(),
  intervalType: z.nativeEnum(TeamsIntervalTypeEnum).optional(),
});

export type TeamsConfig = z.infer<typeof TeamsConfigSchema>;
