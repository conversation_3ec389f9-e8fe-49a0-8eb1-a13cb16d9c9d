import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ActionIcon, Box, Flex } from '@mantine/core';
import React, { useCallback, useMemo, useState } from 'react';
import { AlertPriorityConfig } from '@core/schema';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import useFetch from '@core/hooks/useFetch';
import UpdateOrCreatePriorityButton from './UpdateOrCreatePriorityButton';
import useMutate from '@core/hooks/useMutate';
import { sortBy } from 'lodash';
import PriorityColor from '@components/PriorityColor';
import DragTable from '@components/dragTable/DragTable';
import { IconTrash, IconSearch } from '@tabler/icons-react';
import { refetchRequest } from '@common/utils/QueryUtils';
import { Column, OnDragHandler } from '@components/dragTable/Types';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { KanbanInput } from 'kanban-design-system';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { DEFAULT_PRIORITY } from './Constants';
import { DEFAULT_DEBOUNCE_TIME, TABLE_INPUT_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { useDebouncedState, useDisclosure } from '@mantine/hooks';
import DeleteModal from './DeleteModal';

interface ActionColumnProps {
  priorityConfig: AlertPriorityConfig;
}

const ActionColumn = ({ priorityConfig }: ActionColumnProps) => {
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);
  return (
    <Flex gap='xs' align='center'>
      <GuardComponent requirePermissions={[AclPermission.priorityConfigEdit]}>
        <UpdateOrCreatePriorityButton priorityConfigId={priorityConfig.id} />
      </GuardComponent>
      <DeleteModal
        alertPriorityConfigName={priorityConfig.name}
        opened={openedModalDel}
        onClose={closePopupDel}
        alertPriorityConfigId={priorityConfig.id}
        refetchList={() => refetchRequest(AlertPriorityConfigApi.findAll({ withDeleted: false }))}
      />
      <GuardComponent requirePermissions={[AclPermission.priorityConfigDelete]}>
        <ActionIcon disabled={priorityConfig.id === DEFAULT_PRIORITY} variant='transparent' color='red' onClick={() => openModalDel()}>
          <IconTrash width={20} height={24} />
        </ActionIcon>
      </GuardComponent>
    </Flex>
  );
};

const COLUMNS: Column<AlertPriorityConfig>[] = [
  {
    id: 'name',
    title: 'Name',
    render: 'name',
  },
  {
    id: 'displayColor',
    title: 'Display Color',
    render: (record) => <PriorityColor backgroundColor={record.color} />,
  },
  { id: 'action', title: 'Action', render: (record) => <ActionColumn priorityConfig={record} /> },
];

const PriorityConfigPage = () => {
  const [searchInput, setSearchInput] = useState('');
  const [search, setSearch] = useDebouncedState(searchInput, DEFAULT_DEBOUNCE_TIME);
  const { data, isFetching, refetch } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false, search }));
  const { mutate: updatePositionMutate } = useMutate(AlertPriorityConfigApi.updatePosition, {
    successNotification: 'Update position success.',
    errorNotification: 'Update position failed!',
    onSuccess: () => refetch(),
    onError: () => refetch(),
  });

  const priorityConfigs = useMemo(() => sortBy(data?.data || [], 'position'), [data?.data]);
  const updatePositionHandler = useCallback<OnDragHandler<AlertPriorityConfig>>(
    (activeConfig, overConfig) => {
      if (activeConfig.position !== overConfig.position) {
        updatePositionMutate({
          alertPriorityConfigFromId: activeConfig.id,
          alertPriorityConfigToId: overConfig.id,
          fromPosition: activeConfig.position,
          toPosition: overConfig.position,
        });
      }
    },
    [updatePositionMutate],
  );
  return (
    <Box>
      <HeaderTitleComponent
        title='Priority Config'
        rightSection={
          <Flex gap='sm'>
            <KanbanInput
              placeholder='Search'
              maxLength={TABLE_INPUT_MAX_LENGTH}
              leftSection={<IconSearch />}
              size='sm'
              value={searchInput}
              onChange={(event) => {
                setSearchInput(event.target.value);
                setSearch(event.target.value);
              }}
            />
            <GuardComponent requirePermissions={[AclPermission.priorityConfigEdit, AclPermission.priorityConfigCreate]}>
              <UpdateOrCreatePriorityButton />
            </GuardComponent>
          </Flex>
        }
      />
      <DragTable
        disableDraggable={!isAnyPermissions([AclPermission.priorityConfigEdit]) || !!search || isFetching}
        columns={COLUMNS}
        data={priorityConfigs}
        onDragHandler={updatePositionHandler}
        showIndexColumn={false}
        dataKey={(record) => record.id}
        staticIds={[DEFAULT_PRIORITY]}
        position='bottom'
      />
    </Box>
  );
};

export default PriorityConfigPage;
