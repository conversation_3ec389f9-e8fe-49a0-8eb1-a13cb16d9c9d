import { createPageSchema, createResponseSchema, Page, ResponseData } from '@core/schema';
import { RequestConfig } from '../core/api/BaseApi';
import { BaseURL } from '@common/constants/BaseUrl';
import { PaginationRequest } from './Type';
import { createEmailTemplateFormData, EmailTemplateModel } from '@models/EmailTemplateModel';
import { EmailTemplate, EmailTemplateSchema } from '@core/schema/EmailTemplate';

export type EmailTemplatePaginationRequest = PaginationRequest & {
  search?: string;
};

export class EmailTemplateApi {
  static findAll(tableAffected: EmailTemplatePaginationRequest): RequestConfig<ResponseData<Page<EmailTemplate>>, PaginationRequest> {
    return {
      url: BaseURL.emailTemplate,
      method: 'GET',
      params: tableAffected,
      schema: createResponseSchema(createPageSchema(EmailTemplateSchema)),
    };
  }

  static findById(id: number): RequestConfig<ResponseData<EmailTemplate>> {
    return {
      url: `${BaseURL.emailTemplate}/:id`,
      method: 'GET',
      schema: createResponseSchema(EmailTemplateSchema),
      pathVariable: {
        id,
      },
    };
  }

  static save(body: EmailTemplateModel): RequestConfig<ResponseData<EmailTemplate>> {
    return {
      url: BaseURL.emailTemplate,
      method: 'POST',
      data: createEmailTemplateFormData(body),
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
  }

  static deleteById(id: number): RequestConfig<string> {
    return {
      url: `${BaseURL.emailTemplate}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    };
  }
}
