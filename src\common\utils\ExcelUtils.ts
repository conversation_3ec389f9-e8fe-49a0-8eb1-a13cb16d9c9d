import { ExportFileTypeEnum } from '@common/constants/ExportFileTypeConstants';
import dayjs from 'dayjs';

export function generateFileExportName(componentName: string, fileName: string | null, fileExtension: ExportFileTypeEnum): string {
  fileName = fileName ? fileName.trim() : '';
  if (fileName) {
    return `${fileName}.${fileExtension.toLowerCase()}`;
  } else {
    const currentDate = dayjs();
    return `${componentName}-${currentDate.format('YYYY_MM_DD-HH_mm_ss')}.${fileExtension.toLowerCase()}`;
  }
}

export const addPrefixToFileName = (originalFileName: string, prefix: string): string => {
  const fileNameParts = originalFileName.split('.');
  const fileExtension = fileNameParts.pop();
  const modifiedFileName = `${fileNameParts.join('_') + prefix}.${fileExtension}`;
  return modifiedFileName;
};

export const validateUploadedFile = (file: File): boolean => {
  if (!file) {
    return false;
  }
  const allowedFormats = ['.xlsx'];
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  if (!allowedFormats.includes(`.${fileExtension}`)) {
    return false;
  }
  return true;
};
