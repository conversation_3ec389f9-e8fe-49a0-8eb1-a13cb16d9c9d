import { MAX_CHARACTER_NAME_LENGTH, MAX_EMAIL_ADDRESSES } from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { EmailModel } from './Common';

export const EmailPartnerModelSchema = z.object({
  id: z.number().optional(),
  name: z.string().trim().min(1).max(MAX_CHARACTER_NAME_LENGTH),
  addresses: z
    .array(EmailModel)
    .min(1)
    .max(MAX_EMAIL_ADDRESSES, { message: `The number of email addresses cannot exceed ${MAX_EMAIL_ADDRESSES}.` })
    .refine(
      (emails) => {
        const uniqueEmails = new Set(emails);
        return uniqueEmails.size === emails.length;
      },
      {
        message: 'Contact partner cannot be duplicate.',
      },
    ),
});

export type EmailPartnerModel = z.infer<typeof EmailPartnerModelSchema>;
