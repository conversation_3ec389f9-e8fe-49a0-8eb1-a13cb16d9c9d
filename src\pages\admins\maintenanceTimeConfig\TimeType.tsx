import React, { useEffect, useState } from 'react';
import { Divider, Flex, Grid, SimpleGrid, TextInput } from '@mantine/core';
import { Controller, UseFormReturn } from 'react-hook-form';
import { KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import { TIME_TYPE_LABEL, MaintenanceTimeTypeEnum, TimeUnitEnum, TIME_UNIT_LABEL } from '@common/constants/MaintenanceTimeConfigConstants';
import { MaintenanceTimeConfigModel } from '@models/MaintenanceTimeConfigModel';
import { MaintenanceTimeConfig } from '@core/schema/MaintenanceTimeConfig';
import Cron from 'cron-job/index';
import { DateTimePicker } from '@mantine/dates';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { MAX_CRON_EXPRESSION_LENGTH } from '@common/constants/ValidationConstant';

interface Props {
  disabled: boolean;
  form: UseFormReturn<MaintenanceTimeConfigModel>;
  maintenanceTimeConfig?: MaintenanceTimeConfig;
}

const TimeType = ({ disabled, form }: Props) => {
  const { control, trigger, watch } = form;
  const type = watch('type');
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [tempCronValue, setTempCronValue] = useState<string>('');

  const startTime = watch('startTime');
  const endTime = watch('endTime');

  useEffect(() => {
    trigger(['startTime', 'endTime']);
  }, [startTime, endTime, trigger]);

  return (
    <>
      <Grid>
        <Grid.Col span={4}>
          <Controller
            name='type'
            control={control}
            render={({ field }) => (
              <KanbanSelect
                disabled={disabled}
                allowDeselect={false}
                required
                data={Object.values(MaintenanceTimeTypeEnum).map((key) => ({ value: key, label: TIME_TYPE_LABEL[key] }))}
                {...field}
              />
            )}
          />
        </Grid.Col>

        <Grid.Col span={8}>
          {MaintenanceTimeTypeEnum.NEXT_TIME === type && (
            <Flex direction='column' gap='sm'>
              <SimpleGrid cols={2}>
                <Controller
                  name='nextTime'
                  control={control}
                  render={({ field, fieldState }) => (
                    <KanbanNumberInput
                      placeholder='Time'
                      maxLength={3}
                      value={field.value}
                      disabled={disabled}
                      required={true}
                      allowDecimal={false}
                      allowNegative={false}
                      error={fieldState.error?.message}
                      onChange={(value) => {
                        if (!value) {
                          field.onChange(undefined);
                        } else {
                          field.onChange(value);
                        }
                      }}
                    />
                  )}
                />
                <Controller
                  name='unit'
                  control={control}
                  render={({ field }) => (
                    <KanbanSelect
                      defaultValue={TimeUnitEnum.DAY}
                      placeholder='Unit'
                      disabled={disabled}
                      required
                      allowDeselect={false}
                      data={Object.values(TimeUnitEnum).map((key) => ({ value: key, label: TIME_UNIT_LABEL[key] }))}
                      {...field}
                    />
                  )}
                />
              </SimpleGrid>
            </Flex>
          )}
          {MaintenanceTimeTypeEnum.FROM_TIME_TO_TIME === type && (
            <Flex direction='column' gap='sm'>
              <SimpleGrid cols={2}>
                <Controller
                  name='startTime'
                  control={control}
                  render={({ field, fieldState }) => (
                    <DateTimePicker
                      {...field}
                      disabled={disabled}
                      valueFormat={DATE_FORMAT.FORMAT_DD_MM_YYYY_HH_MM_A}
                      error={fieldState.error?.message}
                      onChange={(val) => {
                        const stringValue = dayjs(val).format();
                        field.onChange(stringValue);
                      }}
                      value={field?.value ? new Date(field.value) : new Date()}
                    />
                  )}
                />
                <Controller
                  name='endTime'
                  control={control}
                  render={({ field, fieldState }) => {
                    return (
                      <DateTimePicker
                        {...field}
                        disabled={disabled}
                        error={fieldState.error?.message}
                        valueFormat={DATE_FORMAT.FORMAT_DD_MM_YYYY_HH_MM_A}
                        onChange={(val) => {
                          const stringValue = dayjs(val).format();
                          field.onChange(stringValue);
                        }}
                        value={field?.value ? new Date(field.value) : new Date()}
                        minDate={new Date()}
                      />
                    );
                  }}
                />
              </SimpleGrid>
            </Flex>
          )}
          {MaintenanceTimeTypeEnum.CRON_JOB === type && (
            <Controller
              name='cronExpression'
              control={control}
              render={({ field, fieldState }) => (
                <TextInput
                  maxLength={MAX_CRON_EXPRESSION_LENGTH}
                  disabled={disabled}
                  ref={inputRef}
                  value={tempCronValue || field.value}
                  onBlur={() => {
                    field.onChange(tempCronValue);
                  }}
                  onChange={(e) => {
                    setTempCronValue(e.target.value);
                  }}
                  error={fieldState.error?.message}
                />
              )}
            />
          )}
        </Grid.Col>
      </Grid>

      {MaintenanceTimeTypeEnum.CRON_JOB === type && (
        <>
          <Divider my='sm' label='OR' labelPosition='center' />
          <Controller
            name='cronExpression'
            control={control}
            render={({ field }) => (
              <Cron
                value={field.value || ''}
                disabled={disabled}
                setValue={(val: string) => {
                  setTempCronValue(val);
                  field.onChange(val);
                }}
              />
            )}
          />
        </>
      )}
    </>
  );
};

export default TimeType;
