import React, { useState, useMemo, useRef, useCallback } from 'react';
import { ColumnType, KanbanTableProps, KanbanTableSelectHandleMethods, KanbanText, TableAffactedSafeType } from 'kanban-design-system';
import { ActionIcon, Box, Switch, Tooltip } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { SortType } from '@common/constants/SortType';
import { PaginationRequest } from '@api/Type';
import useMutate from '@core/hooks/useMutate';

import { IconTrash } from '@tabler/icons-react';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import CollectDatabaseModal from './DatabaseCollectModal';
import { DatabaseCollectApi } from '@api/DatabaseCollectApi';
import { DatabaseCollect } from '@core/schema/DatabaseCollect';
import DatabaseCollectModal from './DatabaseCollectModal';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';

const columns: ColumnType<DatabaseCollect>[] = [
  {
    title: 'Name',
    name: 'name',
  },
  {
    title: 'Description',
    name: 'description',
  },
  {
    title: 'Interval',
    name: 'interval',
  },
];
export const DatabaseCollectPage = () => {
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const { data: lstData, refetch: refetchList } = useFetch(DatabaseCollectApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const { mutate: deleteByIdMutate } = useMutate(DatabaseCollectApi.deleteById, {
    successNotification: 'Deleted successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const { mutate: inactiveMutate } = useMutate(DatabaseCollectApi.inactiveById, {
    successNotification: {
      title: 'Inactive config database collect',
      message: `Inactive config database collect success`,
    },
    confirm: { title: 'Confirm inactive', children: <KanbanText>Are you sure inactive config database collect?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      refetchList();
    },
  });

  const { mutate: activeMutate } = useMutate(DatabaseCollectApi.activeById, {
    successNotification: {
      title: 'Active config database collect',
      message: `Active config database collect success`,
    },
    confirm: { title: 'Confirm active', children: <KanbanText>Are you sure active config database collect?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      refetchList();
    },
  });

  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<DatabaseCollect>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const tableViewListRolesProps: KanbanTableProps<DatabaseCollect> = useMemo(() => {
    return {
      columns: columns,
      data: lstData?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: lstData?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          handleUpdateTablePagination(dataSet);
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.databaseCollectEdit]}>
                <Tooltip label={data.isActive ? 'Active' : 'InActive'}>
                  <Switch
                    checked={data.isActive}
                    onClick={() => {
                      data.isActive ? inactiveMutate(data.id) : activeMutate(data.id);
                    }}
                  />
                </Tooltip>
              </GuardComponent>
              <DatabaseCollectModal id={data.id} refetchList={refetchList} />
              <GuardComponent requirePermissions={[AclPermission.databaseCollectDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    activeMutate,
    deleteByIdMutate,
    handleUpdateTablePagination,
    inactiveMutate,
    lstData?.data?.content,
    lstData?.data?.totalElements,
    refetchList,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <GuardComponent requirePermissions={[AclPermission.databaseCollectCreate]}>
        <CollectDatabaseModal refetchList={refetchList} />
      </GuardComponent>

      <Table ref={tableRef} {...tableViewListRolesProps} />
    </Box>
  );
};
export default DatabaseCollectPage;
