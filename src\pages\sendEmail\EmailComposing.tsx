import React from 'react';
import { KanbanInput, KanbanTagsInput } from 'kanban-design-system';
import { Box, Flex, Group, InputLabel, Radio, Text } from '@mantine/core';
import { MAX_EMAIL_ADDRESSES, MAX_EMAIL_CHARACTER_LENGTH, MAX_EMAIL_SUBJECT_CHARACTER_LENGTH } from '@common/constants/ValidationConstant';
import { EmailComposedModel } from '@models/EmailComposedModel';
import { Controller, FieldError, UseFormReturn } from 'react-hook-form';
import TextEditorTemplate from '@pages/admins/emailRichTextEditor/TextEditorTemplate';
import { FileStorage } from '@core/schema/FileStorage';
import MultiFileInput from '@pages/admins/emailRichTextEditor/MultiFileInput';

type EmailComposingModalProps = {
  form: UseFormReturn<EmailComposedModel>;
};

const EmailComposingPage: React.FC<EmailComposingModalProps> = ({ form }) => {
  const { control, setValue, watch } = form;

  const handleFilesChange = (newFiles: File[], remainingFiles: FileStorage[]) => {
    setValue('files', newFiles);
    setValue('fileStorages', remainingFiles);
  };

  const getFirstErrorMessage = (errors: FieldError | undefined): string => {
    if (!Array.isArray(errors)) {
      return '';
    }
    const firstErrorWithMessage = errors.find((error) => error?.message);
    return firstErrorWithMessage?.message || '';
  };

  return (
    <Box flex={1} p='sm' bg='white'>
      <Box>
        <Flex align='center' mb='xs'>
          <Text size='sm' mr='md'>
            Select sending type:
          </Text>
          <Controller
            name='isOneEmail'
            control={control}
            render={({ field }) => (
              <Radio.Group value={field.value ? 'true' : 'false'} onChange={(value) => field.onChange(value === 'true')}>
                <Group>
                  <Radio value='true' label='Send an email' />
                  <Radio value='false' label='Send multiple emails' />
                </Group>
              </Radio.Group>
            )}
          />
        </Flex>
        <Controller
          name='to'
          control={control}
          render={({ field, fieldState: { error } }) => {
            return (
              <KanbanTagsInput
                leftSection={<InputLabel>To</InputLabel>}
                acceptValueOnBlur={true}
                maxLength={MAX_EMAIL_CHARACTER_LENGTH}
                maxTags={MAX_EMAIL_ADDRESSES}
                allowDuplicates={true}
                {...field}
                error={getFirstErrorMessage(error)}
              />
            );
          }}
        />
        <Controller
          name='cc'
          control={control}
          render={({ field, fieldState: { error } }) => (
            <KanbanTagsInput
              leftSection={<InputLabel>CC</InputLabel>}
              acceptValueOnBlur={true}
              maxLength={MAX_EMAIL_CHARACTER_LENGTH}
              allowDuplicates={true}
              maxTags={MAX_EMAIL_ADDRESSES}
              {...field}
              error={getFirstErrorMessage(error)}
            />
          )}
        />
        <Controller
          name='subject'
          control={control}
          render={({ field, fieldState: { error } }) => (
            <KanbanInput
              leftSectionWidth={70}
              required
              leftSection={<InputLabel required>Subject</InputLabel>}
              maxLength={MAX_EMAIL_SUBJECT_CHARACTER_LENGTH}
              {...field}
              error={error?.message}
            />
          )}
        />
        <MultiFileInput onFilesChange={handleFilesChange} files={watch('files') || []} fileStorages={watch('fileStorages') || []} />
        <Controller name='content' control={control} render={({ field }) => <TextEditorTemplate value={field.value} onChange={field.onChange} />} />
      </Box>
    </Box>
  );
};
export default EmailComposingPage;
