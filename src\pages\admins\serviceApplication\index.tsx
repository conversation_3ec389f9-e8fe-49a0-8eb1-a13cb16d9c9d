import { KanbanTabs } from 'kanban-design-system';
import React, { useEffect, useMemo, useState } from 'react';
import { createSearchParams, useNavigate, useSearchParams } from 'react-router-dom';
import ManagementServicePage from './service';
import ManagementApplicationPage from './application';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { ROUTE_PATH } from '@common/utils/RouterUtils';

export enum ManagementServiceAndApplicationTab {
  SERVICE = 'SERVICE',
  APPLICATION = 'APPLICATION',
}

export const ManagementServiceAndApplicationPage = () => {
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState<string>(
    isAnyPermissions([AclPermission.serviceManageView]) ? ManagementServiceAndApplicationTab.SERVICE : ManagementServiceAndApplicationTab.APPLICATION,
  );
  const [searchParams] = useSearchParams();
  const tabs = useMemo(() => {
    return {
      ...(isAnyPermissions([AclPermission.serviceManageView]) && {
        SERVICE: {
          title: 'Service',
          content: <ManagementServicePage />,
        },
      }),
      ...(isAnyPermissions([AclPermission.applicationManageView]) && {
        APPLICATION: {
          title: 'Application',
          content: <ManagementApplicationPage />,
        },
      }),
    };
  }, []);

  const handleChangeTab = (value: string | null) => {
    navigate({
      pathname: ROUTE_PATH.SERVICE_APPLICATION,
      search: createSearchParams({
        tab: value || ManagementServiceAndApplicationTab.SERVICE,
      }).toString(),
    });
    setCurrentTab(value as ManagementServiceAndApplicationTab);
  };
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);
  return (
    <KanbanTabs
      configs={{
        defaultValue: isAnyPermissions([AclPermission.serviceManageView])
          ? ManagementServiceAndApplicationTab.SERVICE
          : ManagementServiceAndApplicationTab.APPLICATION,
        value: currentTab,
        onChange: handleChangeTab,
      }}
      tabs={tabs}
    />
  );
};

export default ManagementServiceAndApplicationPage;
