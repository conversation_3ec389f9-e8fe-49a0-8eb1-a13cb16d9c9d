import { VARIABLE_MAX_LENGTH, MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const VariableModelSchema = z
  .object({
    id: z.string().optional(),
    name: z.string().trim().min(1).max(MAX_CHARACTER_NAME_LENGTH),
    description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
    value: z.string().trim().max(VARIABLE_MAX_LENGTH).optional(),
    hidden: z.boolean(),
  })
  .superRefine((value, ctx) => {
    const isCreateMode = !value.id;
    if (isCreateMode && !value.value) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['value'],
        message: 'Value can not be empty',
      });
    }
    if (!isCreateMode && !value.hidden && !value.value) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['value'],
        message: 'Value can not be empty',
      });
    }
  });

export type VariableModel = z.infer<typeof VariableModelSchema>;
