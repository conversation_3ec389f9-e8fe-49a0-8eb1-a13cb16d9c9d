import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ActionIcon, Box, ComboboxData, Flex, Tooltip } from '@mantine/core';
import React, { useCallback, useMemo, useRef } from 'react';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { ColumnType, KanbanButton, KanbanIconButton, KanbanSwitch, KanbanTableProps, KanbanTableSelectHandleMethods } from 'kanban-design-system';
import { IconEdit, IconEye } from '@tabler/icons-react';
import { IconPlus } from '@tabler/icons-react';
import { createSearchParams, useNavigate } from 'react-router-dom';
import classes from './GroupConfigStyle.module.css';
import { IconTrash } from '@tabler/icons-react';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { AclPermission } from '@models/AclPermission';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST, MaintenanceTimeConfigAction } from './Constants';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { BaseMaintenanceTimeConfig } from '@core/schema/MaintenanceTimeConfig';
import { MaintenanceTimeConfigApi } from '@api/MaintenanceTimeConfigApi';
import Table from '@components/table';
import { TIME_TYPE_LABEL } from '@common/constants/MaintenanceTimeConfigConstants';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { QueryBuilderField } from '@components/queryBuilder';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { PreviewCondition } from '@components/queryBuilder/PreviewCondition';
import { RuleGroupType } from 'react-querybuilder';

const ActionColumn = ({
  handleChangeURL,
  maintenanceTimeConfig,
  refetch,
}: {
  maintenanceTimeConfig: BaseMaintenanceTimeConfig;
  refetch: () => void;
  handleChangeURL: (param: number, value: string | null) => void;
}) => {
  const { mutate: deleteMutate } = useMutate(MaintenanceTimeConfigApi.delete, {
    successNotification: 'Delete Maintenance time config success',
    errorNotification: 'Delete Maintenance time config failed!',
    confirm: getDefaultDeleteConfirmMessage(),
    onSuccess: () => refetch(),
  });
  const { mutate: updateActiveMutate } = useMutate(MaintenanceTimeConfigApi.updateActive, {
    successNotification: `${maintenanceTimeConfig.active ? 'Inactive' : 'Active'} Maintenance time config success`,
    errorNotification: `${maintenanceTimeConfig.active ? 'Inactive' : 'Active'} Maintenance time config failed!`,
    onSuccess: () => refetch(),
  });
  return (
    <Flex gap='xs' align='center'>
      <GuardComponent requirePermissions={[AclPermission.maintenanceTimeConfigEdit, AclPermission.maintenanceTimeConfigView]}>
        <Tooltip label={maintenanceTimeConfig.active ? 'Active' : 'Inactive'}>
          <KanbanSwitch
            checked={maintenanceTimeConfig.active || false}
            onChange={
              isAnyPermissions([AclPermission.maintenanceTimeConfigEdit])
                ? (event) => updateActiveMutate({ id: maintenanceTimeConfig.id, active: event.target.checked })
                : undefined
            }
          />
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.maintenanceTimeConfigView]}>
        <Tooltip label='View Config'>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              handleChangeURL(maintenanceTimeConfig.id, MaintenanceTimeConfigAction.VIEW);
            }}>
            <IconEye />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.maintenanceTimeConfigEdit]}>
        <Tooltip label='Edit Config'>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              handleChangeURL(maintenanceTimeConfig.id, MaintenanceTimeConfigAction.UPDATE);
            }}>
            <IconEdit />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.maintenanceTimeConfigDelete]}>
        <ActionIcon
          variant='transparent'
          color='red'
          disabled={maintenanceTimeConfig.active}
          onClick={() =>
            deleteMutate(maintenanceTimeConfig.id, {
              confirm: getDefaultDeleteConfirmMessage(maintenanceTimeConfig.name),
            })
          }>
          <IconTrash width={20} height={24} />
        </ActionIcon>
      </GuardComponent>
    </Flex>
  );
};

const MaintenanceTimeConfigPage = () => {
  const { data, refetch } = useFetch(MaintenanceTimeConfigApi.findAll());
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const navigate = useNavigate();

  const handleChangeURL = useCallback(
    (param: number, value: string | null) => {
      navigate({
        pathname: `${ROUTE_PATH.MAINTENANCE_TIME_CONFIG}/${param}`,
        search: createSearchParams({
          action: value || MaintenanceTimeConfigAction.CREATE,
        }).toString(),
      });
    },
    [navigate],
  );

  /**
   * custom search.
   */
  const onSearched = useCallback((datas: BaseMaintenanceTimeConfig[], search: string): BaseMaintenanceTimeConfig[] => {
    const lowerCaseSearch = search.toLowerCase();
    return datas.filter((item) => {
      const name = item.name || '';
      const description = item.description || '';
      const value = item.value || '';
      const status = item.status || '';
      if (
        name.toLowerCase().includes(lowerCaseSearch) ||
        description.toLowerCase().includes(lowerCaseSearch) ||
        value.toLowerCase().includes(lowerCaseSearch) ||
        status.toLowerCase().includes(lowerCaseSearch) ||
        JSON.stringify(item.ruleGroup).toLowerCase().includes(lowerCaseSearch) ||
        TIME_TYPE_LABEL[item.type].toLowerCase().includes(lowerCaseSearch)
      ) {
        return true;
      }
      return false;
    });
  }, []);

  // logic get condition preview
  const { data: customObjectData } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), { showLoading: false });
  const { data: priorityData } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });
  const priorityOptions = useMemo<ComboboxData>(
    () => priorityData?.data?.map((priority) => ({ value: String(priority.id), label: priority.name })) || [],
    [priorityData?.data],
  );

  const fields = useMemo<QueryBuilderField[]>(() => {
    return [
      {
        name: 'content',
        label: 'Alert Content',
        placeholder: 'Please enter value',
        inputType: 'datetime-local',
        operators: [QueryBuilderOperatorEnum.CONTAINS, QueryBuilderOperatorEnum.DOES_NOT_CONTAIN],
      },
      {
        name: 'priority',
        label: 'Priority',
        placeholder: 'Please enter value',
        operators: [
          QueryBuilderOperatorEnum.IS,
          QueryBuilderOperatorEnum.IS_NOT,
          QueryBuilderOperatorEnum.IS_ONE_OF,
          QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
        ],
        valueEditorType: 'multiselect',
        values: priorityOptions,
      },
      {
        name: 'recipient',
        label: 'Contact',
        placeholder: 'Please enter value',
        operators: [
          QueryBuilderOperatorEnum.IS,
          QueryBuilderOperatorEnum.IS_NOT,
          QueryBuilderOperatorEnum.IS_ONE_OF,
          QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
          QueryBuilderOperatorEnum.CONTAINS,
          QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
        ],
      },
      ...(customObjectData?.data?.content?.map((customObject) => ({
        name: String(customObject.id),
        label: customObject.name,
        placeholder: 'Please enter value',
        operators: Object.values(QueryBuilderOperatorEnum) as QueryBuilderOperatorEnum[],
      })) || []),
    ];
  }, [customObjectData?.data?.content, priorityOptions]);
  const columns = useMemo<ColumnType<BaseMaintenanceTimeConfig>[]>(
    () => [
      {
        title: 'Name',
        name: 'name',
      },
      {
        title: 'Description',
        name: 'description',
      },

      {
        title: ' Type',
        name: 'type',
        customRender: (_data, rowData) => TIME_TYPE_LABEL[rowData.type],
      },
      {
        title: 'Value',
        sortable: false,
        name: 'value',
      },
      {
        title: ' Status',
        name: 'status',
      },
      {
        title: 'Condition',
        name: 'ruleGroup',
        width: '40%',
        sortable: false,
        customRender: (value) => (
          <PreviewCondition fields={fields} operators={Object.values(QueryBuilderOperatorEnum)} value={value as RuleGroupType} />
        ),
      },
    ],
    [fields],
  );
  const tableViewListRolesProps: KanbanTableProps<BaseMaintenanceTimeConfig> = useMemo(() => {
    return {
      columns: columns,
      data: data?.data || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        onSearched: onSearched,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return <ActionColumn handleChangeURL={handleChangeURL} maintenanceTimeConfig={data} refetch={refetch} />;
        },
      },
    };
  }, [columns, data?.data, handleChangeURL, onSearched, refetch]);

  return (
    <Box className={classes.groupConfigWrapper}>
      <HeaderTitleComponent
        title='Maintenance Time Config'
        rightSection={
          <Flex gap='md'>
            <GuardComponent requirePermissions={[AclPermission.maintenanceTimeConfigCreate]}>
              <KanbanButton
                onClick={() => {
                  handleChangeURL(0, MaintenanceTimeConfigAction.CREATE);
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
    </Box>
  );
};

export default MaintenanceTimeConfigPage;
