import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import React from 'react';

export type ProtectedRouteProps = {
  children: React.ReactNode;
  //One of those role
  requirePermissions: AclPermission[];
  errorElement: React.ReactNode;
  hiddenOnUnauthorized?: boolean;
};

export const ProtectedComponent: React.FC<ProtectedRouteProps> = ({ children, errorElement, hiddenOnUnauthorized = true, requirePermissions }) => {
  const isAuthorized = isAnyPermissions(requirePermissions);

  return <>{isAuthorized ? children : hiddenOnUnauthorized ? undefined : errorElement}</>;
};
