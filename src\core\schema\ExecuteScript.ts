import { z } from 'zod';
import { ExecutionSchema } from './Execution';
import { QuerySqlExecuteSchema } from './QuerySqlExecute';
import { ExecutionStatusEnum } from '@common/constants/ExecutionConstants';

export const ExecuteScriptSchema = ExecutionSchema.extend({
  sqlExecutionResponse: QuerySqlExecuteSchema.optional(),
  scriptResponse: z.string().optional(),
  scriptError: z.string().optional(),
  status: z.nativeEnum(ExecutionStatusEnum),
});

export type ExecuteScript = z.infer<typeof ExecuteScriptSchema>;
