import { Box } from '@mantine/core';
import React, { useEffect, useRef } from 'react';

interface MentionItemProps extends React.ComponentPropsWithoutRef<'div'> {
  isActive: boolean;
}

export const MentionItem = ({ isActive, style, ...props }: MentionItemProps) => {
  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isActive) {
      ref.current?.scrollIntoView({ block: 'nearest' });
    }
  }, [isActive]);

  return (
    <Box
      ref={ref}
      bg={isActive ? 'gray.1' : 'white'}
      color={isActive ? 'black' : 'dark.7'}
      p={'xs'}
      style={{
        cursor: 'pointer',
        ...style,
      }}
      {...props}
    />
  );
};
