import { MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH } from '@common/constants/ValidationConstant';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { ExecutionGroupModel } from '@models/ExecutionModel';
import { KanbanInput } from 'kanban-design-system';
import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

interface Props {
  form: UseFormReturn<ExecutionGroupModel>;
  readonly: boolean;
}

const ExeuctionGroupForm = ({ form, readonly }: Props) => {
  const { control } = form;
  return (
    <>
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <KanbanInput
            label='Name'
            required
            {...field}
            maxLength={MAX_CHARACTER_NAME_LENGTH}
            description={getMaxLengthMessage(MAX_CHARACTER_NAME_LENGTH)}
            disabled={readonly}
          />
        )}
      />
      <Controller
        name='description'
        control={control}
        render={({ field }) => (
          <KanbanInput
            label='Description'
            {...field}
            maxLength={MAX_DESCRIPTION_LENGTH}
            description={getMaxLengthMessage(MAX_DESCRIPTION_LENGTH)}
            disabled={readonly}
          />
        )}
      />
    </>
  );
};

export default ExeuctionGroupForm;
