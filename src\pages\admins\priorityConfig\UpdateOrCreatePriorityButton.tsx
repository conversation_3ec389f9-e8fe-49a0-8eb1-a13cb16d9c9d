import { useDisclosure } from '@mantine/hooks';
import { KanbanButton, KanbanIconButton } from 'kanban-design-system';
import { IconPlus } from '@tabler/icons-react';
import React, { useCallback, useEffect } from 'react';
import PriorityConfigForm from './PriorityConfigForm';
import { DEFAULT_FORM_VALUE } from './Constants';
import PriorityConfigModal from './PriorityConfgModal';
import useMutate from '@core/hooks/useMutate';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import { AlertPriorityConfigModel, AlertPriorityConfigModelSchema } from '@models/AlertPriorityConfigModel';
import { refetchRequest } from '@common/utils/QueryUtils';
import { useForm, zodResolver } from '@mantine/form';
import { Tooltip } from '@mantine/core';
import { IconEdit } from '@tabler/icons-react';
import useFetch from '@core/hooks/useFetch';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';

interface Props {
  priorityConfigId?: number;
}

const UpdateOrCreatePriorityButton = ({ priorityConfigId }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const isCreateMode = !priorityConfigId;
  const modeTitle = isCreateMode ? 'Create' : 'Update';
  const form = useForm<AlertPriorityConfigModel>({
    initialValues: DEFAULT_FORM_VALUE,
    validate: zodResolver(AlertPriorityConfigModelSchema),
    validateInputOnChange: true,
  });
  const { mutate: createConfigMutate } = useMutate(AlertPriorityConfigApi.save, {
    successNotification: `${modeTitle} Priority Config Success`,
    errorNotification: (error) => ({
      message: error.message,
      title: `${modeTitle} Priority Config failed!`,
    }),
    onSuccess: () => {
      refetchRequest(AlertPriorityConfigApi.findAll({ withDeleted: false }));
      close();
      form.reset();
    },
  });
  const { data: config, isFetching } = useFetch(AlertPriorityConfigApi.findById(priorityConfigId || 0, { withRawValue: true }), {
    enabled: !!priorityConfigId && opened,
  });

  const onSaveButtonClick = useCallback(() => {
    createConfigMutate(AlertPriorityConfigModelSchema.parse(form.values));
  }, [createConfigMutate, form.values]);
  const { initialize, isValid, setValues } = form;

  useEffect(() => {
    if (config?.data && !isFetching) {
      setValues({ ...config.data });
    }
  }, [config?.data, initialize, isFetching, setValues]);

  const onModalClose = useCallback(() => {
    close();
    form.reset();
  }, [close, form]);

  return (
    <>
      {isCreateMode ? (
        <GuardComponent requirePermissions={[AclPermission.priorityConfigCreate]}>
          <KanbanButton onClick={open} leftSection={<IconPlus />}>
            New Config
          </KanbanButton>
        </GuardComponent>
      ) : (
        <GuardComponent requirePermissions={[AclPermission.priorityConfigEdit]}>
          <Tooltip label='Edit Config'>
            <KanbanIconButton variant='transparent' size={'sm'} onClick={open}>
              <IconEdit />
            </KanbanIconButton>
          </Tooltip>
        </GuardComponent>
      )}

      <PriorityConfigModal
        open={opened}
        onClose={onModalClose}
        onSaveClick={onSaveButtonClick}
        disableSaveButton={!isValid()}
        title={`${modeTitle} Alert Priority Config`}>
        <PriorityConfigForm form={form} />
      </PriorityConfigModal>
    </>
  );
};

export default UpdateOrCreatePriorityButton;
