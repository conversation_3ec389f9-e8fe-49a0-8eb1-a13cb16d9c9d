import { Auth<PERSON><PERSON> } from '@api/Auth';
import { CLEAR_WHEN_LOGOUT_KEYS, LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { navigateTo, ROUTE_PATH } from '@common/utils/RouterUtils';
import { callRequest } from '@core/api';
import { getConfigs } from '@core/configs/Configs';
import { currentUserSlice } from '@slices/CurrentUserSlice';
import { directDispath } from '@store';

export const clearOnLogout = () => {
  CLEAR_WHEN_LOGOUT_KEYS.forEach((key) => localStorage.removeItem(key));
};
const config = getConfigs();
let refreshInterval: NodeJS.Timer;
export const clearRefreshInterval = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
};

export const startRefreshTokenInterval = () => {
  const refreshToken = localStorage.getItem(LocalStorageKey.MONITOR_REFRESH_TOKEN);
  if (!refreshToken) {
    return;
  }
  const timeInteval = config.refreshTokenInterval;
  clearRefreshInterval();
  refreshInterval = setInterval(async () => {
    refreshAuthTokens();
  }, timeInteval);
};
const finalizeLogout = () => {
  clearOnLogout();
  clearRefreshInterval();
  directDispath(currentUserSlice.actions.setValue({ isFetching: false, userInfo: undefined }));
  navigateTo(ROUTE_PATH.LOGIN);
};
export const logout = async () => {
  const refreshToken = localStorage.getItem(LocalStorageKey.MONITOR_REFRESH_TOKEN);

  if (!refreshToken) {
    console.error('No refresh token found');
    finalizeLogout();
    return;
  }

  try {
    await callRequest(AuthApi.logout(refreshToken));
  } catch (error) {
    console.error('Logout API failed:', error);
  } finally {
    finalizeLogout();
  }
};
export function setAuthTokens(refreshToken: string, accessToken: string, expiresAt: string): void {
  localStorage.setItem(LocalStorageKey.MONITOR_REFRESH_TOKEN, refreshToken);
  localStorage.setItem(LocalStorageKey.MONITOR_ACCESS_TOKEN, accessToken);
  localStorage.setItem(LocalStorageKey.MONITOR_ACCESS_TOKEN_EXPRIRE_AT, expiresAt);
}
export const refreshAuthTokens = async () => {
  try {
    const refreshToken = localStorage.getItem(LocalStorageKey.MONITOR_REFRESH_TOKEN);
    if (!refreshToken) {
      clearRefreshInterval();
      return;
    }
    const res = await callRequest(AuthApi.refreshToken(refreshToken));
    if (res?.data?.accessToken && res?.data?.expiresAt) {
      localStorage.setItem(LocalStorageKey.MONITOR_ACCESS_TOKEN, res.data.accessToken);
      localStorage.setItem(LocalStorageKey.MONITOR_ACCESS_TOKEN_EXPRIRE_AT, res.data.expiresAt);
    } else {
      clearRefreshInterval();
    }
  } catch (error) {
    console.error('Refresh token failed:', error);
    clearRefreshInterval();
  }
};
