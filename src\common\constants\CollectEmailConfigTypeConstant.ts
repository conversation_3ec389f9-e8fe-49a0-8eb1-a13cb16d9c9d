import { ComboboxItem } from '@mantine/core';

export enum CollectEmailConfigTypeEnum {
  EVENT_BASE_ALERT = 'EVENT_BASE_ALERT',
  ABSENCE_ALERT = 'ABSENCE_ALERT',
}
export const optionCollectEmailConfigTypes: ComboboxItem[] = [
  {
    label: 'Event base alert',
    value: CollectEmailConfigTypeEnum.EVENT_BASE_ALERT,
  },
  {
    label: 'Absence alert',
    value: CollectEmailConfigTypeEnum.ABSENCE_ALERT,
  },
];
