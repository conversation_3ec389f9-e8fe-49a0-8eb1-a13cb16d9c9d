import React, { useMemo } from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { Flex, SimpleGrid } from '@mantine/core';
import { KanbanInput, KanbanSelect, useDebounceCallback } from 'kanban-design-system';
import { CollectEmailConfigModel } from '@models/CollectEmailConfigModel';
import { CustomContentAlert } from './customContentAlert/CustomContentAlert';
import { sortBy } from 'lodash';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import useFetch from '@core/hooks/useFetch';
import { CollectEmailConfig } from '@core/schema/CollectEmailConfig';
import { CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH, CHARACTER_CONTENT_ALERT_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { CollectEmailContentTypeEnum, optionContentTypes } from '@common/constants/CollectEmailConfigConstant';
import { DEFAULT_DEBOUNCE_TIME } from '@components/InfiniteScrollAccordion';
import ServiceApplicationOutputSession from './ServiceApplicationOutputSession';
import { CollectEmailConfigTypeEnum } from '@common/constants/CollectEmailConfigTypeConstant';

interface Props {
  form: UseFormReturn<CollectEmailConfigModel>;
  isViewMode: boolean;
  oldData?: CollectEmailConfig;
}

const OutputSession = ({ form, isViewMode, oldData }: Props) => {
  const { control, setValue } = form;
  const contentType = useWatch({ control, name: 'contentType' });
  const type = useWatch({ control, name: 'type' });
  const { data: priorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });
  const priorityConfigOptions = useMemo(() => {
    return sortBy(
      priorityConfigs?.data?.map((config) => ({ value: `${config.id}`, label: config.name + (config.deleted ? ' (DELETED)' : '') })) || [],
      (option) => option.label.toLowerCase(),
    );
  }, [priorityConfigs?.data]);

  const debouncedOnChangeContentValue = useDebounceCallback((val: string) => {
    setValue('contentValue', val);
  }, DEFAULT_DEBOUNCE_TIME);
  return (
    <Flex direction='column' gap='sm'>
      <form>
        <ServiceApplicationOutputSession form={form} isViewMode={isViewMode} oldData={oldData} />
        <SimpleGrid cols={2}>
          <Controller
            name='recipient'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanInput
                label='Contact'
                disabled={isViewMode}
                maxLength={CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH}
                required
                {...field}
                error={fieldState.error?.message}
              />
            )}
          />
          <Controller
            name='priorityConfigId'
            control={control}
            render={({ field }) => (
              <KanbanSelect
                required
                disabled={isViewMode}
                data={priorityConfigOptions}
                label='Priority'
                {...field}
                value={field.value}
                autoChangeValueByOptions={false}
              />
            )}
          />
        </SimpleGrid>

        {CollectEmailConfigTypeEnum.EVENT_BASE_ALERT === type ? (
          <SimpleGrid cols={2}>
            <Controller
              name='contentType'
              control={control}
              render={({ field }) => (
                <KanbanSelect
                  required
                  disabled={isViewMode}
                  allowDeselect={false}
                  label='Content Type'
                  data={optionContentTypes}
                  {...field}
                  onChange={(val) => {
                    const type = val as CollectEmailContentTypeEnum;
                    setValue('contentType', type, { shouldValidate: true });
                    setValue('content', CollectEmailContentTypeEnum.CUSTOM_CONTENT === type ? '' : undefined, { shouldValidate: true });
                    setValue('contentValue', CollectEmailContentTypeEnum.CUSTOM_CONTENT === type ? '' : undefined, { shouldValidate: true });
                  }}
                />
              )}
            />
            {CollectEmailContentTypeEnum.CUSTOM_CONTENT === contentType && (
              <Controller
                name='content'
                control={control}
                render={({ field }) => (
                  <CustomContentAlert
                    disabled={isViewMode}
                    label='Alert content'
                    value={field.value || '{}'}
                    onChange={(content, val) => {
                      field.onChange(content);
                      debouncedOnChangeContentValue(val || '');
                    }}
                    specialMentions={[{ id: 'subject', name: 'Subject Mail' }]}
                  />
                )}
              />
            )}
          </SimpleGrid>
        ) : (
          <Controller
            name='contentValue'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanInput
                label='Alert content'
                disabled={isViewMode}
                maxLength={CHARACTER_CONTENT_ALERT_MAX_LENGTH}
                required
                {...field}
                error={fieldState.error?.message}
              />
            )}
          />
        )}
      </form>
    </Flex>
  );
};

export default OutputSession;
