import { EnumKey } from '@common/utils/Type';

export enum TaskTimeTypeEnum {
  ONE_TIME = 'ONE_TIME',
  FROM_TIME_TO_TIME = 'FROM_TIME_TO_TIME',
}

export const TaskTimeTypeLabel: EnumKey<TaskTimeTypeEnum> = {
  [TaskTimeTypeEnum.ONE_TIME]: 'One time',
  [TaskTimeTypeEnum.FROM_TIME_TO_TIME]: 'From time to time',
};
export enum TaskTypeEnum {
  TASK = 'TASK',
  SHIFT_HANDOVER_TASK = 'SHIFT_HANDOVER_TASK',
}

export const TaskTypeLabel: EnumKey<TaskTypeEnum> = {
  [TaskTypeEnum.TASK]: 'Task',
  [TaskTypeEnum.SHIFT_HANDOVER_TASK]: 'Shift handover',
};

export const TaskTypeColor: EnumKey<TaskTypeEnum> = {
  [TaskTypeEnum.TASK]: 'var(--mantine-color-green-9)',
  [TaskTypeEnum.SHIFT_HANDOVER_TASK]: 'var(--mantine-color-yellow-9)',
};

export enum TaskShiftEnum {
  SHIFT_1 = 'SHIFT_1',
  SHIFT_2 = 'SHIFT_2',
  SHIFT_3 = 'SHIFT_3',
}

export const TaskShiftLabel: EnumKey<TaskShiftEnum> = {
  [TaskShiftEnum.SHIFT_1]: 'Shift 1',
  [TaskShiftEnum.SHIFT_2]: 'Shift 2',
  [TaskShiftEnum.SHIFT_3]: 'Shift 3',
};

export enum TaskStatusEnum {
  NEW = 'NEW',
  INPROGRESS = 'INPROGRESS',
  DONE = 'DONE',
}

export enum TaskUserTypeEnum {
  ASSIGNEE_USER = 'ASSIGNEE_USER',
  SHIFT_HANDOVER_USER = 'SHIFT_HANDOVER_USER',
}

export const TaskStatusLabel: EnumKey<TaskStatusEnum> = {
  [TaskStatusEnum.NEW]: 'New',
  [TaskStatusEnum.INPROGRESS]: 'Inprogress',
  [TaskStatusEnum.DONE]: 'Done',
};

export const TaskStatusColor: EnumKey<TaskStatusEnum, { backgroundColor: string; color: string }> = {
  [TaskStatusEnum.NEW]: {
    backgroundColor: 'var(--mantine-color-blue-2)',
    color: 'var(--mantine-color-blue-9)',
  },
  [TaskStatusEnum.INPROGRESS]: {
    backgroundColor: 'var(--mantine-color-indigo-2)',
    color: 'var(--mantine-color-indigo-9)',
  },
  [TaskStatusEnum.DONE]: {
    backgroundColor: 'var(--mantine-color-teal-2)',
    color: 'var(--mantine-color-teal-9)',
  },
};
