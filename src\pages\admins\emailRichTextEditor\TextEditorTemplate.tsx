import './Tiptap.scss';
import '@mantine/tiptap/styles.css';

import React, { useEffect, useId, useState } from 'react';
import { Box, Button, FileInput, InputLabel, Tooltip } from '@mantine/core';
import { Link, RichTextEditor } from '@mantine/tiptap';
import classes from './Template.module.scss';
import Superscript from '@tiptap/extension-superscript';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import SubScript from '@tiptap/extension-subscript';
import Color from '@tiptap/extension-color';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import FontFamily from '@tiptap/extension-font-family';
import LineHeight from '@edifice-tiptap-extensions/extension-line-height';
import FontSize from 'tiptap-extension-font-size';
import Highlight from '@tiptap/extension-highlight';
import Table from '@tiptap/extension-table';
import {
  IconLineHeight,
  IconPhoto,
  IconRowInsertTop,
  IconRowInsertBottom,
  IconArrowMerge,
  IconArrowsSplit,
  IconColumnInsertRight,
  IconColumnInsertLeft,
  IconRowRemove,
  IconClearFormatting,
} from '@tabler/icons-react';
import { KanbanSelect } from 'kanban-design-system';
import { fontSizes, lineHeights, fonts } from './DataSelect';
import InsertTableForm from './InsertTableForm';
import HighlightColorPicker from './HighlightColorPicker';
import { CustomImage } from './CustomImage';
import { TextColorPicker } from './TextColorPicker';
import HardBreak from '@tiptap/extension-hard-break';

type TextEditorTemplateProps = {
  value: string | undefined;
  onChange?: (content: string) => void;
  isViewMode?: boolean;
};

const TextEditorTemplate: React.FC<TextEditorTemplateProps> = ({ isViewMode, onChange, value }) => {
  const idImageInput = useId();
  const [keyForImage, setKeyForImage] = useState(0);

  const editor = useEditor({
    editable: !isViewMode,
    extensions: [
      HardBreak.extend({
        addKeyboardShortcuts() {
          return {
            Enter: () => this.editor.commands.setHardBreak(),
          };
        },
      }).configure({
        keepMarks: true,
      }),
      StarterKit.configure({
        paragraph: {
          HTMLAttributes: {
            style: 'margin: 0px;',
          },
        },
        hardBreak: false,
      }),
      CustomImage.configure({
        allowBase64: true,
      }),
      TextStyle,
      Color,
      Underline,
      Link,
      Superscript,
      SubScript,
      TextAlign.configure({
        types: ['paragraph', 'heading', 'tableCell'],
      }),
      Highlight.configure({ multicolor: true }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          style: 'border-collapse: collapse; border: 1px solid black;  width: 1000px;',
        },
      }),
      TableRow,
      TableHeader,
      TableCell.configure({
        HTMLAttributes: {
          style: 'border-collapse: collapse; border: 1px solid black',
        },
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      FontSize,
      LineHeight.configure({
        types: ['textStyle', 'paragraph', 'lineHeight', 'heading'],
      }),
    ],
    onUpdate: ({ editor }) => {
      const updatedContent = editor.getHTML();
      onChange?.(updatedContent);
    },
  });

  useEffect(() => {
    if (editor && editor.getHTML() !== value) {
      editor.commands.setContent(value || '');
    }
  }, [editor, value]);

  useEffect(() => {
    editor?.chain().focus().setMark('textStyle', { fontFamily: 'Times New Roman' }).run();
  }, [editor]);

  const setFontFamily = (fontFamily: string | null) => {
    editor?.chain().focus().setMark('textStyle', { fontFamily }).run();
  };
  const setFontSize = (fontSize: string | null) => {
    editor?.chain().focus().setMark('textStyle', { fontSize }).run();
  };
  const setLineHeight = (lineHeight: string | null) => {
    editor?.chain().focus().setMark('textStyle', { lineHeight }).run();
  };

  const setFileImage = (file: File | null) => {
    if (file && editor) {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result;
        editor?.commands.insertContent({
          type: 'image',
          attrs: {
            src: base64,
          },
        });
      };
      reader.readAsDataURL(file);
      setKeyForImage((prevKey) => prevKey + 1);
    }
  };

  const handleTableInsert = (columns: number, rows: number) => {
    editor
      ?.chain()
      .focus()
      .insertTable({
        rows,
        cols: columns,
        withHeaderRow: false,
      })
      .run();
  };

  function handleHighlightColorChange(color: string | undefined): void {
    if (color) {
      editor?.commands.setHighlight({ color });
    } else {
      editor?.commands.unsetHighlight();
    }
  }

  function handleTextColorChange(color: string | undefined): void {
    if (color) {
      editor?.commands.setColor(color);
    } else {
      editor?.commands.unsetColor();
    }
  }

  return (
    <Box>
      {editor && (
        <RichTextEditor editor={editor}>
          {!isViewMode && (
            <RichTextEditor.Toolbar sticky className={classes.controlsGroup}>
              <RichTextEditor.ControlsGroup className={classes.controlsGroup}>
                <KanbanSelect my='xs' maw='9rem' size='xs' searchable placeholder='Select Font' data={fonts} onChange={setFontFamily} />
                <KanbanSelect my='xs' searchable size='xs' maw='3.8rem' data={fontSizes} onChange={setFontSize} />
                <KanbanSelect
                  my='xs'
                  ml='xs'
                  searchable
                  size='xs'
                  maw='4.6rem'
                  data={lineHeights}
                  onChange={setLineHeight}
                  leftSection={<IconLineHeight size={16} />}
                />
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ControlsGroup className={classes.controlsGroup}>
                <RichTextEditor.Bold />
                <RichTextEditor.Italic />
                <RichTextEditor.Underline />
                <RichTextEditor.Code />
                <TextColorPicker onChange={handleTextColorChange} />
                <HighlightColorPicker onChange={handleHighlightColorChange} />
                <Tooltip label='Clear Format' withArrow position='bottom'>
                  <Button
                    className={classes.mantineButton}
                    onClick={() => {
                      if (editor) {
                        const selection = editor.state.selection;
                        if (selection.empty) {
                          const cursorPos = selection.from;
                          editor.chain().focus().selectParentNode().unsetAllMarks().clearNodes().run();
                          editor.chain().focus().setTextSelection(cursorPos).run();
                        } else {
                          editor.chain().focus().unsetAllMarks().run();
                        }
                      }
                    }}
                    variant='subtle'
                    size='xs'>
                    <IconClearFormatting />
                  </Button>
                </Tooltip>
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ControlsGroup className={classes.controlsGroup}>
                <RichTextEditor.Blockquote />
                <RichTextEditor.Hr />
                <RichTextEditor.BulletList />
                <RichTextEditor.OrderedList />
                <RichTextEditor.Subscript />
                <RichTextEditor.Superscript />
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ControlsGroup className={classes.controlsGroup}>
                <RichTextEditor.Link />
                <RichTextEditor.Unlink />
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ControlsGroup className={classes.controlsGroup}>
                <RichTextEditor.AlignLeft />
                <RichTextEditor.AlignCenter />
                <RichTextEditor.AlignJustify />
                <RichTextEditor.AlignRight />
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ControlsGroup className={classes.controlsGroup}>
                <Tooltip label='Insert Image' withArrow position='bottom'>
                  <InputLabel htmlFor={idImageInput} variant='subtle' size='xs' className={classes.mantineButton}>
                    <IconPhoto size={16} />
                  </InputLabel>
                </Tooltip>
                <FileInput key={keyForImage} accept='image/*' id={idImageInput} onChange={setFileImage} display={'none'} />
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ControlsGroup className={classes.controlsGroup}>
                <InsertTableForm onInsert={handleTableInsert} />
                <Tooltip label='Insert Row Before' withArrow position='bottom'>
                  <Button className={classes.mantineButton} onClick={() => editor.chain().focus().addRowBefore().run()} variant='subtle' size='xs'>
                    <IconRowInsertTop size={16} />
                  </Button>
                </Tooltip>
                <Tooltip label='Insert Row After' withArrow position='bottom'>
                  <Button className={classes.mantineButton} onClick={() => editor.chain().focus().addRowAfter().run()} variant='subtle' size='xs'>
                    <IconRowInsertBottom size={16} />
                  </Button>
                </Tooltip>
                <Tooltip label='Merge Cells' withArrow position='bottom'>
                  <Button className={classes.mantineButton} onClick={() => editor.chain().focus().mergeCells().run()} variant='subtle' size='xs'>
                    <IconArrowMerge size={16} />
                  </Button>
                </Tooltip>
                <Tooltip label='Unmerge Cells' withArrow position='bottom'>
                  <Button className={classes.mantineButton} onClick={() => editor.chain().focus().splitCell().run()} variant='subtle' size='xs'>
                    <IconArrowsSplit size={16} />
                  </Button>
                </Tooltip>
                <Tooltip label='Insert Column After' withArrow position='bottom'>
                  <Button className={classes.mantineButton} onClick={() => editor.chain().focus().addColumnAfter().run()} variant='subtle' size='xs'>
                    <IconColumnInsertRight size={16} />
                  </Button>
                </Tooltip>
                <Tooltip label='Insert Column Before' withArrow position='bottom'>
                  <Button className={classes.mantineButton} onClick={() => editor.chain().focus().addColumnBefore().run()} variant='subtle' size='xs'>
                    <IconColumnInsertLeft size={16} />
                  </Button>
                </Tooltip>
                <Tooltip label='Delete Row' withArrow position='bottom'>
                  <Button className={classes.mantineButton} onClick={() => editor.chain().focus().deleteRow().run()} variant='subtle' size='xs'>
                    <IconRowRemove size={16} />
                  </Button>
                </Tooltip>
              </RichTextEditor.ControlsGroup>
            </RichTextEditor.Toolbar>
          )}
          <RichTextEditor.Content
            style={{
              pointerEvents: isViewMode ? 'none' : 'auto',
              cursor: isViewMode ? 'default' : 'text',
            }}
            mih='28rem'
          />
        </RichTextEditor>
      )}
    </Box>
  );
};

export default TextEditorTemplate;
