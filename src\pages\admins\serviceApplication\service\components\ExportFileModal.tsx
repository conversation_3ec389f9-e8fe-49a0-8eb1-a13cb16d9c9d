import React from 'react';
import { KanbanButton, KanbanInput, KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { useForm, zodResolver } from '@mantine/form';
import { ApplicationPaginationRequest } from '@models/ApplicationModel';
import useMutate from '@core/hooks/useMutate';
import { columns } from '../ServiceCommon';
import { ServiceApi } from '@api/ServiceApi';
import { ExportFileModelSchema, getInitExportFileRequest } from '@models/ExportFileModel';
import { LIMIT_EXPORT_ROWS_LENGTH } from '@common/constants/ValidationConstant';
import { comboboxExportFileType, ExportFileTypeEnum } from '@common/constants/ExportFileTypeConstants';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { MAX_CHARACTER_FILE_NAME_LENGTH } from '@common/constants/ValidationConstant';
import Modal from '@components/Modal';
import MessageExportSuccess from '@components/MessageExportSuccess';

type ExportFileModalProps = {
  opened: boolean;
  onClose: () => void;
  tableAffected: ApplicationPaginationRequest;
};

const ExportFileModal: React.FC<ExportFileModalProps> = ({ onClose, opened, tableAffected }) => {
  const { getInputProps, isValid, reset, values } = useForm({
    validateInputOnChange: true,
    initialValues: getInitExportFileRequest(columns),
    validate: zodResolver(ExportFileModelSchema),
  });

  const { isPending, mutate: exportFile } = useMutate(ServiceApi.export, {
    successNotification: { message: <MessageExportSuccess /> },
    onSuccess: () => handleClose(),
    showLoading: false,
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleExport = () => {
    exportFile({
      exportDataModel: { fileName: values.nameFile, extension: values.typeFile || ExportFileTypeEnum.CSV },
      attributes: values.attributes,
      paginationRequest: {
        ...tableAffected,
        page: 0,
        size: values.numberOfResults ? values.numberOfResults : LIMIT_EXPORT_ROWS_LENGTH,
      },
      numberOfResults: values.numberOfResults,
    });
  };

  return (
    <>
      <Modal
        size={'xl'}
        opened={opened}
        onClose={() => {
          handleClose();
        }}
        title={'Export File'}
        closeOnClickOutside={false}
        actions={
          <GuardComponent requirePermissions={[AclPermission.serviceManageExport]}>
            <KanbanButton loading={isPending} disabled={!isValid()} onClick={handleExport}>
              Export
            </KanbanButton>
          </GuardComponent>
        }>
        <form>
          <KanbanSelect label='Type File' required={true} data={comboboxExportFileType} {...getInputProps('typeFile')} />
          <KanbanInput
            label='File Name'
            description={getMaxLengthMessage(MAX_CHARACTER_FILE_NAME_LENGTH)}
            value={values.nameFile}
            placeholder='optinal'
            {...getInputProps('nameFile')}
            maxLength={MAX_CHARACTER_FILE_NAME_LENGTH}
          />
          <KanbanNumberInput
            placeholder={`leave blank to export ${LIMIT_EXPORT_ROWS_LENGTH} results`}
            label='Number of Results'
            {...getInputProps('numberOfResults')}
            allowDecimal={false}
            allowNegative={false}
            onChange={(val) => {
              const numberValue = val === '' ? undefined : Number(val);
              getInputProps('numberOfResults').onChange(numberValue);
            }}
          />
        </form>
      </Modal>
    </>
  );
};
export default ExportFileModal;
