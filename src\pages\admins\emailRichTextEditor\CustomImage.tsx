import { Node, mergeAttributes } from '@tiptap/core';

export const CustomImage = Node.create({
  name: 'image',
  inline: true,
  group: 'inline',
  draggable: true,

  addAttributes() {
    return {
      src: {
        default: null,
      },
      width: {
        default: 'auto',
      },
      height: {
        default: 'auto',
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'img[src]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['img', mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ({ editor, getPos, node }) => {
      const wrapper = document.createElement('div');
      wrapper.style.display = 'inline-block';
      wrapper.style.position = 'relative';
      wrapper.style.overflow = 'hidden';
      wrapper.style.resize = 'both';
      wrapper.style.width = node.attrs.width || 'auto';
      wrapper.style.height = node.attrs.height || 'auto';

      const img = document.createElement('img');
      img.setAttribute('src', node.attrs.src);
      img.style.width = '100%';
      img.style.height = '100%';
      img.style.objectFit = 'contain';

      img.onload = () => {
        const aspectRatio = img.naturalWidth / img.naturalHeight;
        wrapper.style.height = `${wrapper.offsetWidth / aspectRatio}px`;
      };

      wrapper.appendChild(img);

      const onResize = () => {
        const pos = getPos();
        const node = editor.state.doc.nodeAt(pos);

        const aspectRatio = img.naturalWidth / img.naturalHeight;
        const newWidth = `${wrapper.offsetWidth}px`;
        const newHeight = `${wrapper.offsetWidth / aspectRatio}px`;

        wrapper.style.height = newHeight;

        editor.view.dispatch(
          editor.state.tr.setNodeMarkup(pos, undefined, {
            ...node?.attrs,
            width: newWidth,
            height: newHeight,
          }),
        );
      };

      wrapper.addEventListener('mouseup', onResize);

      let isSelected = false;
      const toggleBorder = () => {
        wrapper.style.border = isSelected ? '1px solid transparent' : '1px dashed #6C6C6C';
        isSelected = !isSelected;
      };

      wrapper.addEventListener('click', toggleBorder);

      document.addEventListener('click', (event) => {
        const target = event.target as HTMLElement;
        if (target && !wrapper.contains(target)) {
          wrapper.style.border = '1px solid transparent';
          isSelected = false;
        }
      });

      wrapper.setAttribute('draggable', 'true');
      wrapper.addEventListener('dragstart', () => {
        wrapper.style.opacity = '0.5';
      });
      wrapper.addEventListener('dragend', () => {
        wrapper.style.opacity = '1';
      });

      return {
        dom: wrapper,
        contentDOM: null,
      };
    };
  },
});
