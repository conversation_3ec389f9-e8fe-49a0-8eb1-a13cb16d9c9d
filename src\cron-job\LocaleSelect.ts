/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import { DefaultLocale } from './TypesSelect';

export const DEFAULT_LOCALE_EN: DefaultLocale = {
  everyText: 'Every',
  emptyMonths: 'Every month',
  emptyMonthDays: 'Every day of the month',
  emptyMonthDaysShort: 'Day of the month',
  emptyWeekDays: 'Every day of the week',
  emptyWeekDaysShort: 'Day of the week',
  emptyHours: 'Every hour',
  emptyMinutes: 'Every minute',
  emptyMinutesForHourPeriod: 'Every',
  yearOption: 'Year',
  monthOption: 'Month',
  weekOption: 'Week',
  dayOption: 'Day',
  hourOption: 'Hour',
  minuteOption: 'Minute',
  rebootOption: 'Reboot',
  prefixPeriod: 'Every',
  prefixMonths: 'In',
  prefixMonthDays: 'On',
  prefixWeekDays: 'On',
  prefixWeekDaysForMonthAndYearPeriod: 'And',
  prefixHours: 'At',
  prefixMinutes: ':',
  prefixMinutesForHourPeriod: 'At',
  suffixMinutesForHourPeriod: 'Minute(s)',
  errorInvalidCron: 'Invalid cron expression',
  clearButtonText: 'Clear',
  weekDays: [
    // Order is important, the index will be used as value
    'Sunday', // Sunday must always be first, it's "0"
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ],
  months: [
    // Order is important, the index will be used as value
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  // Order is important, the index will be used as value
  altWeekDays: [
    'SUN', // Sunday must always be first, it's "0"
    'MON',
    'TUE',
    'WED',
    'THU',
    'FRI',
    'SAT',
  ],
  // Order is important, the index will be used as value
  altMonths: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'],
};
