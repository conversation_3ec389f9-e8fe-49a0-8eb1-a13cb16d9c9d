import Modal from '@components/Modal';
import { Button } from '@mantine/core';
import React, { useCallback, useMemo } from 'react';
import TaskForm from './TaskForm';
import { KanbanButton } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from 'react-hook-form';
import { TaskModel, TaskModelSchema } from '@models/TaskModel';
import { TaskTimeTypeEnum, TaskTypeEnum } from '@common/constants/TaskConstants';
import { zodResolver } from '@hookform/resolvers/zod';
import useMutate from '@core/hooks/useMutate';
import { TaskApi } from '@api/TaskApi';
import dayjs from 'dayjs';

interface Props {
  onCreateTaskSuccess: () => void;
}

const CreateTaskButton = ({ onCreateTaskSuccess }: Props) => {
  const TASK_DEFAULT_VALUE: TaskModel = useMemo(
    () => ({
      name: '',
      description: '',
      type: TaskTypeEnum.TASK,
      startTime: dayjs().format(),
      endTime: dayjs().format(),
      timeType: TaskTimeTypeEnum.ONE_TIME,
    }),
    [],
  );
  const [opended, { close, open }] = useDisclosure();
  const form = useForm({
    defaultValues: TASK_DEFAULT_VALUE,
    resolver: zodResolver(TaskModelSchema),
    mode: 'onChange',
  });
  const { mutate: createOrUpdateTaskMutate } = useMutate(TaskApi.createOrUpdate, {
    onSuccess: () => {
      close();
      form.reset();
      onCreateTaskSuccess();
    },
  });

  const onSave = useCallback(() => {
    const formValue = form.getValues();
    createOrUpdateTaskMutate({ ...formValue, endTime: TaskTimeTypeEnum.ONE_TIME === formValue.timeType ? undefined : formValue.endTime });
  }, [createOrUpdateTaskMutate, form]);
  return (
    <>
      <Button
        variant='outline'
        onClick={() => {
          open();
          form.reset({
            name: '',
            description: '',
            type: TaskTypeEnum.TASK,
            startTime: dayjs().format(),
            endTime: dayjs().format(),
            timeType: TaskTimeTypeEnum.ONE_TIME,
          });
        }}>
        Create Task
      </Button>
      <Modal
        onClose={() => {
          close();
          form.reset();
        }}
        opened={opended}
        size='lg'
        title='Create Task'
        actions={
          <KanbanButton onClick={onSave} disabled={!form.formState.isValid}>
            Save
          </KanbanButton>
        }>
        <TaskForm form={form} />
      </Modal>
    </>
  );
};

export default CreateTaskButton;
