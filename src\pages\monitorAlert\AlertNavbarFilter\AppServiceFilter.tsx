import { ApplicationApi } from '@api/ApplicationApi';
import { ServiceApi } from '@api/ServiceApi';
import ComboboxLoadMore from '@components/ComboboxLoadMore';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { DEFAULT_APPLICATION_PAGINATION_REQUEST, DEFAULT_SERVICE_PAGINATION_REQUEST } from '@pages/admins/groupConfig/Constants';
import React, { useState } from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { FilterForm } from '../Types';

interface Props {
  form: UseFormReturn<FilterForm>;
}

const AppServiceFilter = ({ form }: Props) => {
  const { control, getValues, setValue } = form;
  const services = useWatch({ control, name: 'services' });
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_SERVICE_PAGINATION_REQUEST);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_APPLICATION_PAGINATION_REQUEST);
  const { fetchNextPage: fetchNextPageService, flatData: serviceData } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
  });
  const { fetchNextPage: fetchNextPageApplication, flatData: applicationData } = useInfiniteFetch(
    ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds: services?.map((service) => service.id) || [] }),
    {
      showLoading: false,
    },
  );

  return (
    <>
      <Controller
        name='services'
        control={control}
        render={({ field: { onChange, value } }) => {
          return (
            <ComboboxLoadMore
              options={serviceData}
              onChange={(values) => {
                onChange(values);
                setValue(
                  'applications',
                  getValues('applications').filter((application) => values.some((service) => service.id === application.serviceId)),
                );
              }}
              label='Service Name'
              placeholder='Search service name'
              onSearch={(val) => setServiceSearchParams((prev) => ({ ...prev, name: val }))}
              onScroll={fetchNextPageService}
              onClickedOption={() => setServiceSearchParams(DEFAULT_SERVICE_PAGINATION_REQUEST)}
              renderPillLabel={(data) => data.name}
              renderOptionLabel={(data) => data.name}
              values={value}
              scrollableForValue={true}
              triggerOnChangeWhenOnBlur
              showCountDescription
            />
          );
        }}
      />

      <Controller
        name='applications'
        control={control}
        render={({ field: { onChange, value } }) => (
          <ComboboxLoadMore
            options={applicationData}
            onChange={onChange}
            label='Application Name'
            placeholder='Search application name'
            onSearch={(val) => setApplicationSearchParams((prev) => ({ ...prev, name: val }))}
            onClickedOption={() => setApplicationSearchParams(DEFAULT_APPLICATION_PAGINATION_REQUEST)}
            onScroll={fetchNextPageApplication}
            renderPillLabel={(data) => data.name}
            renderOptionLabel={(data) => data.name}
            values={value}
            groupByKeys={['serviceName']}
            scrollableForValue={true}
            showCountDescription
          />
        )}
      />
    </>
  );
};

export default AppServiceFilter;
