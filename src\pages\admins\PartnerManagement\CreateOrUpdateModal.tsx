import React, { useEffect } from 'react';
import { KanbanButton, KanbanInput, KanbanTagsInput } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import { useForm, zodResolver } from '@mantine/form';
import useMutate from '@core/hooks/useMutate';

import { EmailPartner } from '@core/schema/EmailPartner';
import { EmailPartnerApi } from '@api/EmailPartnerApi';
import { EmailPartnerModel, EmailPartnerModelSchema } from '@models/EmailPartnerModel';
import { MAX_CHARACTER_NAME_LENGTH, MAX_EMAIL_ADDRESSES, MAX_EMAIL_CHARACTER_LENGTH } from '@common/constants/ValidationConstant';
import Modal from '@components/Modal';

type CreateOrUpdateModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  emailPartner?: EmailPartner;
};

const DEFAULT_PARTNER: EmailPartnerModel = {
  name: '',
  addresses: [],
  id: undefined,
};

const CreateOrUpdateModal: React.FC<CreateOrUpdateModalProps> = ({ emailPartner, onClose, opened, refetchList }) => {
  const isUpdateMode = !!emailPartner;
  const { data: emailPartnerDetail } = useFetch(EmailPartnerApi.findById(emailPartner?.id || 0), {
    enabled: isUpdateMode && opened,
  });
  const { mutate: saveMutate } = useMutate(EmailPartnerApi.save, {
    successNotification: isUpdateMode ? `Update Partner successfully` : 'Create Partner successfully',
    onSuccess: () => {
      setValues(DEFAULT_PARTNER);
      refetchList();
      onClose();
    },
  });
  const { getInputProps, isValid, setValues, validate, values } = useForm({
    initialValues: DEFAULT_PARTNER,
    validate: zodResolver(EmailPartnerModelSchema),
    validateInputOnChange: true,
  });

  const handleSave = () => {
    if (!validate().hasErrors) {
      saveMutate(EmailPartnerModelSchema.parse(values));
    }
  };

  useEffect(() => {
    if (!isUpdateMode) {
      setValues(DEFAULT_PARTNER);
      return;
    }
    if (emailPartnerDetail?.data) {
      setValues({
        id: emailPartnerDetail?.data?.id || 0,
        name: emailPartnerDetail?.data?.name || '',
        addresses: emailPartnerDetail?.data?.addresses || [],
      });
    }
  }, [emailPartnerDetail?.data, opened, setValues, emailPartner, isUpdateMode]);

  return (
    <Modal
      size={'xl'}
      opened={opened}
      onClose={() => {
        onClose();
        setValues(DEFAULT_PARTNER);
      }}
      title={emailPartner ? `Update Partner ${emailPartner.name}` : 'Create new'}
      actions={
        <KanbanButton onClick={handleSave} disabled={!isValid()}>
          Save
        </KanbanButton>
      }>
      <form>
        <KanbanInput
          maxLength={MAX_CHARACTER_NAME_LENGTH}
          required
          label='Name Partner'
          placeholder='Enter partner name'
          {...getInputProps('name')}
        />
        <KanbanTagsInput
          acceptValueOnBlur={true}
          allowDuplicates={true}
          maxLength={MAX_EMAIL_CHARACTER_LENGTH}
          maxTags={MAX_EMAIL_ADDRESSES}
          description={'Press enter to add contact, require valid email'}
          name='addresses'
          placeholder='Enter partner contact'
          label='Contact Partner'
          required={true}
          {...getInputProps('addresses')}
        />
      </form>
    </Modal>
  );
};

export default CreateOrUpdateModal;
