import { RequestConfig } from '../core/api/BaseApi';
import { BaseURL } from '@common/constants/BaseUrl';
import { PaginationRequest } from './Type';
import { createPageSchema, createResponseSchema, Page, ResponseData } from '@core/schema';
import { BaseCollectEmailConfig, BaseCollectEmailConfigSchema, CollectEmailConfig, CollectEmailConfigSchema } from '@core/schema/CollectEmailConfig';
import { CollectEmailConfigModel } from '@models/CollectEmailConfigModel';

export class CollectEmailConfigApi {
  static findAll(pagination: PaginationRequest): RequestConfig<ResponseData<Page<BaseCollectEmailConfig>>, PaginationRequest> {
    return {
      url: BaseURL.collectEmail,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(BaseCollectEmailConfigSchema)),
    };
  }

  static findById(id: number): RequestConfig<ResponseData<CollectEmailConfig>> {
    return {
      url: `${BaseURL.collectEmail}/:id`,
      method: 'GET',
      schema: createResponseSchema(CollectEmailConfigSchema),
      pathVariable: {
        id,
      },
    };
  }
  static save(body: CollectEmailConfigModel): RequestConfig<ResponseData<CollectEmailConfig>> {
    return {
      url: BaseURL.collectEmail,
      method: 'POST',
      data: body,
    };
  }

  static deleteById(id: number): RequestConfig<string> {
    return {
      url: `${BaseURL.collectEmail}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    };
  }
  static activeOrInactive(id: number): RequestConfig<string> {
    return {
      url: `${BaseURL.collectEmail}/:id/toggle-status`,
      method: 'PUT',
      pathVariable: {
        id,
      },
    };
  }
  static deleteByIdIn(ids: number[]): RequestConfig<string> {
    return {
      url: `${BaseURL.collectEmail}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    };
  }
}
