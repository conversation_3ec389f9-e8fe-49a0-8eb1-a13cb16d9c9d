import React from 'react';
import { Container, Box, Flex } from '@mantine/core';
import { KanbanText } from 'kanban-design-system';
import { format } from 'sql-formatter';
import { sql } from '@codemirror/lang-sql';
import styles from './SqlEditor.module.scss';
import ReactCodeMirror, { EditorState, Extension, Transaction } from '@uiw/react-codemirror';

interface SQLEditorProps {
  value: string;
  onChange: (newValue: string) => void;
  onFormatError?: (error: string) => void;
  label?: string;
  maxLength?: number;
  error?: string;
  disable?: boolean;
  required?: boolean;
}
function maxLengthExtensionWithTruncation(maxLength: number): Extension {
  return EditorState.transactionFilter.of((tr: Transaction) => {
    const newLength = tr.newDoc.length;
    const docLength = tr.startState.doc.length;

    if (newLength > maxLength) {
      const truncatedText = tr.newDoc.sliceString(0, maxLength);
      const transaction = tr.startState.update({
        changes: {
          from: 0,
          to: Math.min(docLength, tr.newDoc.length),
          insert: truncatedText,
        },
      });
      return [transaction];
    }

    return tr;
  });
}
const SQLEditor: React.FC<SQLEditorProps> = ({ disable = false, error, label, maxLength, onChange, onFormatError, required = false, value }) => {
  const handleChange = (newValue: string) => {
    if (disable) {
      return;
    }

    if (maxLength && newValue.length > maxLength) {
      const truncatedValue = newValue.substring(0, maxLength);
      onChange(truncatedValue);
    } else {
      onChange(newValue);
    }
  };

  const handleFormatSQL = () => {
    if (disable) {
      return;
    }
    try {
      const formattedSQL = format(value, {
        keywordCase: 'upper',
        language: 'plsql',
      });
      onChange(formattedSQL);
      onFormatError?.('');
    } catch (e: any) {
      onFormatError?.(e.message);
    }
  };
  return (
    <Box>
      {label && (
        <Flex direction='row' gap='xs'>
          <KanbanText fw='500' mb='0.25rem'>
            {label}
          </KanbanText>
          {required && <KanbanText c='red'>*</KanbanText>}
        </Flex>
      )}

      <Container size='100%' className={styles.container} onBlur={handleFormatSQL}>
        <ReactCodeMirror
          value={value}
          height='300px'
          extensions={[sql(), maxLengthExtensionWithTruncation(maxLength || 1000)]}
          onChange={(val: string) => handleChange(val)}
          editable={!disable}
          readOnly={disable}
          theme='light'
        />
      </Container>
      {error && (
        <KanbanText c='red' size='sm' mt={5}>
          {error}
        </KanbanText>
      )}
    </Box>
  );
};

export default SQLEditor;
