import React, { useCallback, useState } from 'react';
import ComboboxLoadMore, { ComboboxLoadMoreProps } from '@components/ComboboxLoadMore';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { ApplicationApi } from '@api/ApplicationApi';
import { Application } from '@core/schema';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { ServiceApi } from '@api/ServiceApi';
import { Box } from '@mantine/core';
import { DEFAULT_APPLICATION_PAGINATION_REQUEST, DEFAULT_SERVICE_PAGINATION_REQUEST } from './Constants';
import { MaintenanceTimeConfigModel } from '@models/MaintenanceTimeConfigModel';

interface Props {
  form: UseFormReturn<MaintenanceTimeConfigModel>;
  isViewMode?: boolean;
}

const ReferenceSession = ({ form, isViewMode }: Props) => {
  const { control, setValue } = form;
  const services = useWatch({ control, name: 'services' });
  const applications = useWatch({ control, name: 'applications' });
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_SERVICE_PAGINATION_REQUEST);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_APPLICATION_PAGINATION_REQUEST);
  const { fetchNextPage: fetchNextPageService, flatData: serviceData } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
  });
  const { fetchNextPage: fetchNextPageApplication, flatData: applicationData } = useInfiniteFetch(
    ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds: services?.map((service) => service.id) || [] }),
    {
      showLoading: false,
    },
  );

  const renderApplicationPill = useCallback<ComboboxLoadMoreProps<Application>['renderPillLabel']>((application: Application) => {
    return (
      <span>
        {application.serviceName}: {application.name}
      </span>
    );
  }, []);

  return (
    <Box>
      <Controller
        control={control}
        name='services'
        render={({ field: { onChange, value } }) => {
          return (
            <ComboboxLoadMore
              disabled={isViewMode}
              required
              options={serviceData}
              onChange={(services) => {
                onChange(services);
                setValue(
                  'applications',
                  applications?.filter((application) => services.some((service) => service.id === application.serviceId)),
                );
              }}
              label='Service Name'
              placeholder='Search service name'
              onSearch={(val) => setServiceSearchParams((prev) => ({ ...prev, name: val }))}
              onScroll={fetchNextPageService}
              onClickedOption={() => setServiceSearchParams(DEFAULT_SERVICE_PAGINATION_REQUEST)}
              renderPillLabel={(data) => {
                const isServiceWithAllApp = !applications?.some((app) => app.serviceId === data.id);
                return (
                  <span>
                    {data.name}
                    {isServiceWithAllApp ? <b>( All application )</b> : ''}
                  </span>
                );
              }}
              renderOptionLabel={(data) => data.name}
              values={value}
              scrollableForValue={true}
              onClearValue={() => setValue('applications', [])}
              clearable={!isViewMode}
            />
          );
        }}
      />

      <Controller
        control={control}
        name='applications'
        render={({ field: { onChange, value } }) => (
          <ComboboxLoadMore
            disabled={isViewMode || services.length === 0}
            options={applicationData}
            onChange={onChange}
            label='Application Name'
            placeholder='Search application name'
            onSearch={(val) => setApplicationSearchParams((prev) => ({ ...prev, name: val }))}
            onClickedOption={() => setApplicationSearchParams(DEFAULT_APPLICATION_PAGINATION_REQUEST)}
            onScroll={fetchNextPageApplication}
            renderPillLabel={renderApplicationPill}
            renderOptionLabel={(data) => data.name}
            values={value || []}
            groupByKeys={['serviceName']}
            scrollableForValue={true}
            clearable={!isViewMode}
          />
        )}
      />
    </Box>
  );
};

export default ReferenceSession;
