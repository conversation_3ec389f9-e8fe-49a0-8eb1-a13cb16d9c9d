import { EmailComposedModel, EmailComposedModelSchema } from '@models/EmailComposedModel';

export function createEmailComposedFormData(data: EmailComposedModel): FormData {
  const validatedData = EmailComposedModelSchema.parse(data);
  const formData = new FormData();
  validatedData.files?.forEach((file) => {
    formData.append('files', file);
  });

  formData.append(
    'request',
    new Blob([JSON.stringify(data)], {
      type: 'application/json',
    }),
  );
  return formData;
}
