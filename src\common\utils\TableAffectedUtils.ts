import { SortType } from '@common/constants/SortType';
import type { TableAffactedSafeType } from 'kanban-design-system';
import { PaginationRequest } from '@api/Type';

export function tableAffectedToPaginationRequestModel<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequest {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy?.toString(),
    sortOrder: !tableAffected.sortedBy ? SortType.DESC : tableAffected.isReverse ? SortType.ASC : SortType.DESC,
  };
}
