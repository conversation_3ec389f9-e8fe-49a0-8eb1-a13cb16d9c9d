import { Grid, GridCol } from '@mantine/core';
import React from 'react';
import ExecutionGroups from './executionGroups';
import ExecutionDetail from './executionDetail';

const ExecutionPage = () => {
  return (
    <Grid gutter='xs'>
      <GridCol span={2}>
        <ExecutionGroups />
      </GridCol>
      <GridCol span={10}>
        <ExecutionDetail />
      </GridCol>
    </Grid>
  );
};

export default ExecutionPage;
