import React, { useCallback, useEffect, useRef } from 'react';
import { Box, Table as MantineTable, Stack, TableThProps } from '@mantine/core';
import { getCustomTrProps, getDataKey, getDefaultBgColor } from './Utils';
import { merge } from 'lodash';
import { useMemo } from 'react';
import THead from './THead';
import TData from './TData';
import { InfiniteScrollTableProps } from './Types';
import classes from '../table/Table.module.css';
import { useSafetyRenderStateRef } from 'kanban-design-system';

function InfiniteScrollTable<T extends object>({
  columns,
  customProps,
  data,
  dataKey,
  loading = false,
  onScrollToBottom,
  showIndexColumn = true,
  sortEffect,
}: InfiniteScrollTableProps<T>) {
  const customColumnIndexProps = useMemo<TableThProps>(() => {
    return merge(customProps?.indexColumnTh, {
      style: { width: '30px', wordWrap: 'normal' },
    });
  }, [customProps?.indexColumnTh]);
  const observer = useRef<IntersectionObserver>();
  const onScrollToBottomRef = useSafetyRenderStateRef(onScrollToBottom);
  const observerElementRef = useCallback(
    (node: HTMLTableRowElement) => {
      if (loading) {
        return;
      }
      if (!observer.current) {
        observer.current = new IntersectionObserver((entries) => {
          if (entries[0].isIntersecting) {
            const onScrollToBottom = onScrollToBottomRef.current;
            onScrollToBottom();
          }
        });
      }
      if (observer.current) {
        observer.current.disconnect();
      }
      if (node) {
        observer.current.observe(node);
      }
    },
    [loading, onScrollToBottomRef],
  );
  useEffect(() => {
    return () => {
      if (observer.current) {
        observer.current.disconnect(); // Disconnect the observer on unmount or changes
      }
    };
  }, []);
  return (
    <Stack style={{ height: '100%' }}>
      <Box style={{ flexGrow: 1, overflowY: 'auto' }} className={classes.customTableWrapper}>
        <MantineTable {...customProps?.table}>
          <MantineTable.Thead {...customProps?.thead} style={{ position: 'sticky' }}>
            <MantineTable.Tr {...customProps?.theadTr}>
              {showIndexColumn && (
                <MantineTable.Th p='xs' {...customColumnIndexProps}>
                  No.
                </MantineTable.Th>
              )}
              {columns.map((column) => (
                <THead key={column.id} column={column} customProps={customProps} sortEffect={sortEffect} />
              ))}
            </MantineTable.Tr>
          </MantineTable.Thead>
          <MantineTable.Tbody {...customProps?.tbody}>
            {data.map((record, index) => {
              const key = dataKey ? getDataKey(record, dataKey) : index;
              const customTrProps = getCustomTrProps(record, index, customProps?.tbodyTr);
              return (
                <MantineTable.Tr
                  {...customTrProps}
                  style={{ backgroundColor: getDefaultBgColor(index), ...customTrProps?.style }}
                  key={key}
                  ref={data.length === index + 1 ? observerElementRef : null}>
                  {showIndexColumn && (
                    <MantineTable.Td p='xs' {...customProps?.indexColumnTd} style={{ whiteSpace: 'nowrap' }}>
                      {index + 1}
                    </MantineTable.Td>
                  )}
                  {columns.map((column) => (
                    <TData key={column.id + key} column={column} record={record} customProps={customProps} index={index} />
                  ))}
                </MantineTable.Tr>
              );
            })}
          </MantineTable.Tbody>
        </MantineTable>
      </Box>
    </Stack>
  );
}

export default InfiniteScrollTable;
