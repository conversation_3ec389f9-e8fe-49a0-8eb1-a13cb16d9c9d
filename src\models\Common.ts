import { MAX_EMAIL_CHARACTER_LENGTH, MAX_EMAIL_DOMAIN_LENGTH, MAX_EMAIL_USERNAME_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const EmailModel = z
  .string()
  .email()
  .max(MAX_EMAIL_CHARACTER_LENGTH, { message: `Email cannot exceed ${MAX_EMAIL_CHARACTER_LENGTH} characters` })
  .refine(
    (email) => {
      const [username, domain] = email.split('@');
      return username.length <= MAX_EMAIL_USERNAME_LENGTH && domain && domain.length <= MAX_EMAIL_DOMAIN_LENGTH;
    },
    {
      message: `<PERSON><PERSON> must have a username not exceeding ${MAX_EMAIL_USERNAME_LENGTH} characters and a domain not exceeding ${MAX_EMAIL_DOMAIN_LENGTH} characters`,
    },
  );
