import { CollectEmailContentTypeEnum } from '@common/constants/CollectEmailConfigConstant';
import { z } from 'zod';
import { QueryRuleGroupTypeSchema } from './RuleGroupCondition';
import { CollectEmailConfigTypeEnum } from '@common/constants/CollectEmailConfigTypeConstant';

export const BaseCollectEmailConfigSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(CollectEmailConfigTypeEnum),
  emailConfigEmail: z.string().optional(),
  emailConfigIntervalTime: z.number().optional(),
  active: z.boolean().optional(),
  ruleGroup: QueryRuleGroupTypeSchema,
});
export type BaseCollectEmailConfig = z.infer<typeof BaseCollectEmailConfigSchema>;
export const CollectEmailConfigSchema = BaseCollectEmailConfigSchema.extend({
  emailConfigId: z.number(),
  serviceId: z.string(),
  applicationId: z.string(),
  serviceName: z.string(),
  applicationName: z.string(),
  absenceInterval: z.number().optional(),
  alertRepeatInterval: z.number().optional(),
  priorityConfigId: z.number(),
  contentType: z.nativeEnum(CollectEmailContentTypeEnum).optional(),
  content: z.string().optional(),
  contentValue: z.string().optional(),
  recipient: z.string(),
});

export type CollectEmailConfig = z.infer<typeof CollectEmailConfigSchema>;
