import { BaseURL } from '@common/constants/BaseUrl';
import { createResponseSchema } from '@core/schema';
import { z } from 'zod';
import { MaintenanceTimeConfigRequest, PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { BaseMaintenanceTimeConfigSchema, MaintenanceTimeConfigSchema } from '@core/schema/MaintenanceTimeConfig';

export type MaintenanceTimePaginationRequest = PaginationRequest & {
  search?: string;
};
export class MaintenanceTimeConfigApi {
  static findAll() {
    return createRequest({
      url: BaseURL.maintenanceTime,
      method: 'GET',
      schema: createResponseSchema(z.array(BaseMaintenanceTimeConfigSchema)),
    });
  }
  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.maintenanceTime}/:id`,
      method: 'GET',
      schema: createResponseSchema(MaintenanceTimeConfigSchema),
      pathVariable: {
        id,
      },
    });
  }
  static save(data: MaintenanceTimeConfigRequest) {
    return createRequest({
      url: BaseURL.maintenanceTime,
      method: 'POST',
      schema: createResponseSchema(MaintenanceTimeConfigSchema),
      data,
    });
  }
  static delete(id: number) {
    return createRequest({
      url: `${BaseURL.maintenanceTime}/:id`,
      method: 'DELETE',
      schema: createResponseSchema(z.string()),
      pathVariable: {
        id,
      },
    });
  }

  static updateActive({ active, id }: { id: number; active: boolean }) {
    return createRequest({
      url: `${BaseURL.maintenanceTime}/:id`,
      method: 'PUT',
      schema: createResponseSchema(MaintenanceTimeConfigSchema),
      pathVariable: {
        id,
      },
      params: {
        active,
      },
    });
  }
}
