import React from 'react';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { dateToString } from '@common/utils/DateUtils';
import { Column } from '@components/dragTable/Types';
import { Alert } from '@core/schema';
import { ColumnType, KanbanText } from 'kanban-design-system';
import { Box, Text } from '@mantine/core';
import { AlertStatusLabel } from '@common/constants/AlertStatusConstant';

export const COLUMNS: Column<Alert>[] = [
  {
    id: 'serviceName',
    title: 'Service Name',
    render: 'serviceName',
    width: '6%',
  },
  {
    id: 'applicationName',
    title: 'Application Name',
    render: 'applicationName',
    width: '150px',
  },
  {
    id: 'alertContent',
    title: 'Alert Content',
    render: 'content',
    width: '18%',
  },
  {
    id: 'createdTime',
    title: 'Time',
    render: (data) => dateToString(data.createdDate, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS),
    width: '100px',
  },
  {
    id: 'status',
    title: 'Status',
    render: (data) => <KanbanText fw={500}>{AlertStatusLabel[data.status]}</KanbanText>,
    width: '110px',
  },
  {
    id: 'priority',
    title: 'Priority',
    render: (data) => <KanbanText fw={500}>{data.priorityName}</KanbanText>,
  },
  {
    id: 'ackUser',
    title: 'ACK User',
    render: 'closedBy',
    width: '100px',
  },
  {
    id: 'ackTime',
    title: 'ACK Time',
    render: (data) => (data.closedDate ? dateToString(data.closedDate, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS) : ''),
    width: '100px',
  },
  {
    id: 'closedDuration',
    title: 'ACK Duration',
    render: 'closedDuration',
    width: '110px',
  },
  {
    id: 'comments',
    title: 'Comments',
    render: (data) => (
      <Box>
        {data.notes?.map((note) => (
          <Text key={note.id}>
            <Text fw={500} component='span'>
              - {dateToString(note.createdDate, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)} {note.createdBy} :
            </Text>{' '}
            {note.content}
          </Text>
        ))}
      </Box>
    ),
  },

  {
    id: 'contact',
    title: 'Contact',
    render: 'recipient',
    width: '10%',
  },
  {
    id: 'groupId',
    title: 'Group Id',
    render: 'alertGroupId',
    width: '90px',
  },
];

export const columns: ColumnType<Alert>[] = [
  {
    title: 'Service Name',
    name: 'serviceName',
    width: '15%',
    sortable: false,
  },
  {
    title: 'Application Name',
    name: 'applicationName',
    width: '15%',
    sortable: false,
  },
  {
    title: 'Alert Content',
    name: 'content',
    width: '30%',
    sortable: false,
  },
  {
    title: 'Status',
    name: 'status',
    sortable: false,
  },
  {
    title: 'Priority',
    name: 'priorityName',
    sortable: false,
    customRender: (_, record) => record.priorityName,
  },
  {
    name: 'closedBy',
    title: 'ACK User',
    width: 5000,
  },
  {
    name: 'closedDate',
    title: 'ACK Time',
    width: 5000,
  },
  {
    name: 'closedDuration',
    title: 'ACK Duration',
    width: 5000,
  },
  {
    title: 'Contact',
    name: 'recipient',
    width: '20%',
    sortable: false,
  },
  {
    title: 'Group Id',
    name: 'alertGroupId',
    sortable: false,
  },
  {
    title: 'Date',
    name: 'date',
    customRender: (data) => dateToString(data, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS),
    width: 5000,
    sortable: false,
  },
];
