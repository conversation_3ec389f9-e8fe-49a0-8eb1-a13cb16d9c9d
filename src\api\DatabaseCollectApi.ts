import { createPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { PaginationRequest } from './Type';
import { DatabaseCollectSchema } from '@core/schema/DatabaseCollect';
import { DatabaseCollectModel } from '@models/DatabaseCollectModel';
import { SqlExecutionSchema } from '@core/schema/SqlExecution';
import { createRequest } from './Utils';

export class DatabaseCollectApi {
  static findAll(tableAffected: PaginationRequest) {
    return createRequest({
      url: BaseURL.databaseCollect,
      method: 'GET',
      params: tableAffected,
      schema: createResponseSchema(createPageSchema(DatabaseCollectSchema)),
    });
  }

  static deleteById(id: number) {
    return createRequest({
      url: `${BaseURL.databaseCollect}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    });
  }

  static deleteBatch(ids: number[]) {
    return createRequest({
      url: `${BaseURL.databaseCollect}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    });
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.databaseCollect}/:id`,
      method: 'GET',
      schema: createResponseSchema(DatabaseCollectSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(body: DatabaseCollectModel) {
    return createRequest({
      url: BaseURL.databaseCollect,
      method: 'POST',
      data: body,
    });
  }

  static activeById(id: number) {
    return createRequest({
      url: `${BaseURL.databaseCollect}/${id}`,
      method: 'PUT',
      params: { active: true },
    });
  }

  static inactiveById(id: number) {
    return createRequest({
      url: `${BaseURL.databaseCollect}/${id}`,
      method: 'PUT',
      params: { active: false },
    });
  }

  static getSampleData(dataQuery: { connectionId: number; sqlCommand: string; createdDateField: string }) {
    return createRequest({
      url: `${BaseURL.databaseCollect}/query-sample-data`,
      method: 'POST',
      data: dataQuery,
      schema: createResponseSchema(SqlExecutionSchema),
    });
  }
}
