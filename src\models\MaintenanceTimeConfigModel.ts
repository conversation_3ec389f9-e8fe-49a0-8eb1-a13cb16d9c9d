import { DESCRIPTION_MAX_LENGTH, MAX_NAME_LENGTH, MIN_STRING_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { QueryRuleGroupTypeModelSchema } from './RuleGroupTypeModel';
import { ApplicationSchema, ServiceSchema } from '@core/schema';
import { MaintenanceTimeTypeEnum, TIME_UNIT_LABEL } from '@common/constants/MaintenanceTimeConfigConstants';
import { isEmpty } from 'lodash';
import { parseCronString } from 'cron-job/ConverterSelect';
import dayjs from 'dayjs';

export const MAX_VALUE_NEXT_TIME = 100;

export const MaintenanceTimeConfigModelSchema = z
  .object({
    id: z.number().optional(),
    name: z.string().trim().min(MIN_STRING_LENGTH).max(MAX_NAME_LENGTH),
    description: z.string().max(DESCRIPTION_MAX_LENGTH).optional(),
    type: z.nativeEnum(MaintenanceTimeTypeEnum),
    ruleGroup: QueryRuleGroupTypeModelSchema,
    services: z.array(ServiceSchema).min(1),
    applications: z.array(ApplicationSchema).optional(),
    nextTime: z.number().min(1).max(MAX_VALUE_NEXT_TIME).optional(),
    unit: z.nativeEnum(TIME_UNIT_LABEL).optional(),
    cronExpression: z.string().optional(),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
  })
  .superRefine((value, ctx) => {
    if (value.type === MaintenanceTimeTypeEnum.NEXT_TIME) {
      if (!value.nextTime) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['nextTime'], message: 'Time can not be empty' });
      }
      if (!value.unit) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['unit'], message: 'Unit can not be empty' });
      }
    }
    if (value.type === MaintenanceTimeTypeEnum.CRON_JOB) {
      if (isEmpty(value.cronExpression) || !value.cronExpression) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['cronExpression'],
          message: 'Expression can not be empty',
        });
      } else {
        try {
          parseCronString(value.cronExpression);
        } catch (error) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['cronExpression'],
            message: (error as Error).message,
          });
        }
      }
    }

    const now = dayjs();

    if (value.type === MaintenanceTimeTypeEnum.FROM_TIME_TO_TIME) {
      const startTime = value.startTime ? dayjs(value.startTime) : null;
      const endTime = value.endTime ? dayjs(value.endTime) : null;

      if (!value.startTime) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['startTime'], message: 'Start time can not be empty' });
      }
      if (!value.endTime) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['endTime'], message: 'End time can not be empty' });
      }

      if (startTime && endTime) {
        if (!startTime.isBefore(endTime)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['startTime'],
            message: 'Start time must be earlier than end time',
          });
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['endTime'],
            message: 'End time must be later than start time',
          });
        }
        if (!endTime.isAfter(now)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['endTime'],
            message: 'End time must be later than the current time',
          });
        }
      }
    }
  });

export type MaintenanceTimeConfigModel = z.infer<typeof MaintenanceTimeConfigModelSchema>;
