import { createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { SqlExecutionSchema } from '@core/schema/SqlExecution';
import { createRequest } from './Utils';

export class SuperiorsApi {
  /**
   * Executes a SQL query against the superiors database.
   * @param dataQuery - An object containing the SQL query and password.
   * @returns A promise that resolves to the response of the SQL execution.
   */
  static querySql(dataQuery: { sqlQuery: string; password: string }) {
    return createRequest({
      url: `${BaseURL.superiors}/sql-execution`,
      method: 'POST',
      data: dataQuery,
      schema: createResponseSchema(SqlExecutionSchema),
    });
  }
}
