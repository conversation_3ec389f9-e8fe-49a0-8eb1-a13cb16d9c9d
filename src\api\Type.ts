import { SortType } from '@common/constants/SortType';
import { AlertGroupConfigModel } from '@models/AlertGroupConfigModel';
import { MaintenanceTimeConfigModel } from '@models/MaintenanceTimeConfigModel';
import { Omit } from 'lodash';
import { TaskModel } from '@models/TaskModel';
import { FilterAlertConfigModel } from '@models/FilterAlertConfigModel';
import { ModifyAlertConfigModel } from '@models/ModifyAlertConfigModel';

export type PaginationRequest = {
  page?: number;
  size?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: SortType;
  isReverse?: boolean;
};
export type SearchPaginationRequest = PaginationRequest & {
  name?: string;
};

export type PriorityConfigUpdatePositionRequest = {
  alertPriorityConfigFromId: number;
  alertPriorityConfigToId: number;
  fromPosition: number;
  toPosition: number;
};

export type CustomObjectSearchRequest = PaginationRequest & {
  name?: string;
  withDeleted?: boolean;
};

export type AlertGroupSearchRequest = {
  serviceIds: string[];
  applicationIds: string[];
  content: string;
  recipient: string;
  alertPriorityConfigIds: string[];
};

export type AlertGroupConfigUpdatePositionRequest = {
  alertGroupConfigFromId: number;
  alertGroupConfigToId: number;
  fromPosition: number;
  toPosition: number;
};

export type AlertGroupConfigRequest = Omit<AlertGroupConfigModel, 'services' | 'applications'> & { serviceIds: string[]; applicationIds: string[] };

export type MaintenanceTimeConfigRequest = Omit<MaintenanceTimeConfigModel, 'services' | 'applications'> & {
  serviceIds: string[];
  applicationIds: string[];
};

export type CreateOrUpdateTask = Omit<TaskModel, 'tasks'> & { taskIds?: number[] };

export type AssignTaskRequest = {
  taskId: number;
  userName: string;
};

export type FilterAlertConfigRequest = Omit<FilterAlertConfigModel, 'services' | 'applications'> & {
  serviceIds: string[];
  applicationIds: string[];
};

export type ModifyAlertConfigUpdatePositionRequest = {
  modifyAlertConfigFromId: number;
  modifyAlertConfigToId: number;
  fromPosition: number;
  toPosition: number;
};

export type ModifyAlertConfigRequest = Omit<ModifyAlertConfigModel, 'services' | 'applications'> & {
  serviceIds: string[];
  applicationIds: string[];
};
