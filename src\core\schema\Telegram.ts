import { TelegramAlertConfigTypeEnum, TelegramConfigTypeEnum } from '@common/constants/TelegramConstants';
import { z } from 'zod';
import { createPageSchema } from './Common';

export const TelegramConfigSchema = z.object({
  id: z.string().optional(),
  botToken: z.string().optional(),
  tokenPlaceHolder: z.string().optional(),
  defaultGroupChatId: z.string().optional(),
  description: z.string().optional(),
  type: z.nativeEnum(TelegramConfigTypeEnum).optional(),
  isActive: z.boolean().optional(),
});

export const TelegramAlertConfigSchema = z.object({
  id: z.string().optional(),
  telegramConfigId: z.string().optional(),
  serviceId: z.string().optional(),
  serviceName: z.string().optional(),
  applicationId: z.string().optional(),
  applicationName: z.string().optional(),
  type: z.nativeEnum(TelegramAlertConfigTypeEnum).optional(),
  groupChatId: z.string().optional(),
  isActive: z.boolean().optional(),
});

export const TelegramAlertConfigDetailSchema = z.object({
  config: TelegramConfigSchema,
  alertsConfig: createPageSchema(TelegramAlertConfigSchema),
});

export type TelegramAlertConfigDetail = z.infer<typeof TelegramAlertConfigDetailSchema>;

export type TelegramAlertConfig = z.infer<typeof TelegramAlertConfigSchema>;
