import React, { useMemo } from 'react';
import { KanbanSelect } from 'kanban-design-system';
import { DatabaseConnectionApi } from '@api/DatabaseConnectionApi';
import useFetch from '@core/hooks/useFetch';

type DatabaseConnectionComponentProps = {
  opened: boolean;
  value: string;
  error?: string;
  onChange: (val: number) => void;
  isViewMode?: boolean;
  label: string;
};

const DatabaseConnectionComponent: React.FC<DatabaseConnectionComponentProps> = ({ error, isViewMode, label, onChange, opened, value }) => {
  const { data: optionDatabaseConnection } = useFetch(DatabaseConnectionApi.findAll(), {
    showLoading: false,
    enabled: opened && !isViewMode,
  });

  const databaseConnectionComboxOptions = useMemo(() => {
    if (isViewMode) {
      return [{ value: value, label: label }];
    }
    return (
      optionDatabaseConnection?.data
        ?.filter((item) => item.isActive)
        .sort((a, b) => a.name.localeCompare(b.name))
        .map((obj) => ({ value: `${obj.id}`, label: obj.name })) ?? []
    );
  }, [isViewMode, label, optionDatabaseConnection?.data, value]);

  return (
    <>
      <KanbanSelect
        label='Database Connection'
        required
        disabled={isViewMode}
        value={value}
        error={error}
        searchable
        data={databaseConnectionComboxOptions}
        onChange={(value) => {
          if (value) {
            onChange(Number(value));
          }
        }}
      />
    </>
  );
};

export default DatabaseConnectionComponent;
