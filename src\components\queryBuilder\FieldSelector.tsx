import React, { FC, useMemo } from 'react';
import { FieldSelectorProps, OptionGroup, FullField } from 'react-querybuilder';
import { KanbanSelect } from 'kanban-design-system';

function isGroup(opt: FullField | OptionGroup<FullField>): opt is OptionGroup<FullField> {
  return 'options' in opt;
}

const FieldSelector: FC<FieldSelectorProps> = ({ handleOnChange, options, value }) => {
  const data = useMemo(() => {
    return options.flatMap((opt) => {
      if (isGroup(opt)) {
        return opt.options.map((field) => ({
          value: field.name,
          label: field.label,
        }));
      }
      return {
        value: opt.name,
        label: opt.label,
      };
    });
  }, [options]);

  return <KanbanSelect allowDeselect={false} data={data} value={value} onChange={handleOnChange} mt='xs' searchable placeholder='Select Field' />;
};

export default FieldSelector;
