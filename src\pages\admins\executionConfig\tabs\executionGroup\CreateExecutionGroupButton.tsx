import Modal from '@components/Modal';
import { Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton } from 'kanban-design-system';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { ExecutionGroupModel, ExecutionGroupModelSchema } from '@models/ExecutionModel';
import { IconPlus } from '@tabler/icons-react';
import ExeuctionGroupForm from './ExecutionGroupForm';
import useMutate from '@core/hooks/useMutate';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import { zodResolver } from '@hookform/resolvers/zod';

const DEFAULT_EXECUTION_VALUE: ExecutionGroupModel = {
  name: '',
  description: '',
};

interface Props {
  onCreateSuccess: () => void;
}

const CreateExecutionGroupButton = ({ onCreateSuccess }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const form = useForm<ExecutionGroupModel>({
    defaultValues: DEFAULT_EXECUTION_VALUE,
    resolver: zodResolver(ExecutionGroupModelSchema),
  });
  const onCloseModal = useCallback(() => {
    close();
    form.reset();
  }, [close, form]);
  const { mutate: createMutate } = useMutate(ExecutionGroupApi.createOrUpdate, {
    successNotification: 'Create Execution Group successfully.',
    onSuccess: () => {
      onCreateSuccess();
      onCloseModal();
    },
  });
  const onSaveClick = useCallback(() => {
    createMutate(form.getValues());
  }, [createMutate, form]);
  const { formState } = form;
  return (
    <>
      <KanbanButton size='xs' onClick={open} leftSection={<IconPlus />}>
        Create Execution Group
      </KanbanButton>
      <Modal
        size='xl'
        opened={opened}
        onClose={onCloseModal}
        title='Create Execution Group'
        actions={
          <KanbanButton onClick={onSaveClick} disabled={!formState.isValid}>
            Save
          </KanbanButton>
        }>
        <Box p='xs'>
          <ExeuctionGroupForm form={form} readonly={false} />
        </Box>
      </Modal>
    </>
  );
};

export default CreateExecutionGroupButton;
