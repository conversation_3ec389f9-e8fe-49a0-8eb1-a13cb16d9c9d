import { ExecutionStatusEnum, ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { z } from 'zod';
import { createDateTimeSchema } from './Common';
import { DATE_FORMAT } from '@common/constants/DateConstants';

export const ExecutionScriptParamSchema = z.object({
  name: z.string(),
  value: z.string(),
  hidden: z.boolean(),
});

export const ExecutionHistorySchema = z.object({
  id: z.string(),
  executionName: z.string(),
  executionDescription: z.string().optional(),
  executionType: z.nativeEnum(ExecutionTypeEnum),
  executionScript: z.string().optional(),
  executionParams: z.array(ExecutionScriptParamSchema).optional(),
  status: z.nativeEnum(ExecutionStatusEnum),
  result: z.string().optional(),
  error: z.string().optional(),
  executionBy: z.string(),
  startTime: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
  endTime: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS).optional(),
});

export type ExecutionScriptParam = z.infer<typeof ExecutionScriptParamSchema>;
export type ExecutionHistory = z.infer<typeof ExecutionHistorySchema>;
