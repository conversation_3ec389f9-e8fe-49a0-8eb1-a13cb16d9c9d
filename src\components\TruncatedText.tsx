import { Text } from '@mantine/core';
import { useElementSize } from '@mantine/hooks';
import React from 'react';

interface TruncatedTextProps {
  text: string;
  maxLength?: number;
}

const TruncatedText: React.FC<TruncatedTextProps> = ({ maxLength = 500, text }) => {
  const { ref, width } = useElementSize();

  return (
    <>
      <Text ref={ref} size='sm' lineClamp={1} truncate='end' dangerouslySetInnerHTML={{ __html: text || '' }} maw='520px' />
      {width >= maxLength ? '...' : ''}
    </>
  );
};

export default TruncatedText;
