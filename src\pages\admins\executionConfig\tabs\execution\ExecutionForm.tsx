import { DatabaseConnectionApi } from '@api/DatabaseConnectionApi';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import { VariableApi } from '@api/VariableApi';
import { ExecutionTypeEnum, ExecutionTypeLabel, HIDDEN_VARIABLE_PLACEHOLDER } from '@common/constants/ExecutionConstants';
import { EXECUTION_SCRIPT_MAX_LENGTH, MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH } from '@common/constants/ValidationConstant';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import PythonEditor from '@components/editor/PythonEditor';
import SQLEditor from '@components/editor/SQLEditor';
import { Variable } from '@components/editor/Type';
import useFetch from '@core/hooks/useFetch';
import { ComboboxData } from '@mantine/core';
import { ExecutionModel } from '@models/ExecutionModel';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import React, { useMemo } from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';

interface Props {
  form: UseFormReturn<ExecutionModel>;
  fetchGroup: boolean;
  readOnly: boolean;
}

const ExecutionGroupInput = ({ fetchGroup, form, readOnly }: Props) => {
  const { data: executionGroupData } = useFetch(ExecutionGroupApi.findAll({ orderBy: 'name' }), { enabled: fetchGroup });
  const executionGroupOptions = useMemo<ComboboxData>(() => {
    return executionGroupData?.data?.map((group) => ({ label: group.name, value: group.id })) || [];
  }, [executionGroupData?.data]);
  return (
    <Controller
      name='executionGroupId'
      control={form.control}
      render={({ field: { onChange, value } }) => (
        <KanbanSelect
          label='Execution Group'
          required
          data={executionGroupOptions}
          value={value}
          onChange={onChange}
          autoChangeValueByOptions={false}
          disabled={readOnly}
          searchable
        />
      )}
    />
  );
};

const ExecutionEditor = ({ form, readOnly }: Omit<Props, 'fetchGroup'>) => {
  const { control } = form;
  const type = useWatch({ name: 'type', control });
  const { data: variableData } = useFetch(VariableApi.findAll(), {
    showLoading: false,
    enabled: type === ExecutionTypeEnum.PYTHON && !readOnly,
  });
  const variables = useMemo<Variable[]>(() => {
    return (
      variableData?.data?.map((ele) => ({
        id: ele.id,
        name: ele.name,
        value: ele.hidden ? HIDDEN_VARIABLE_PLACEHOLDER : ele.value,
      })) || []
    );
  }, [variableData?.data]);
  return (
    <Controller
      name='script'
      control={control}
      render={({ field: { onChange, value } }) => {
        if (ExecutionTypeEnum.SQL === type) {
          return (
            <SQLEditor
              value={value}
              onChange={(value) => onChange(value)}
              height='350px'
              required
              label='Script'
              readOnly={readOnly}
              maxLength={EXECUTION_SCRIPT_MAX_LENGTH}
            />
          );
        } else {
          return (
            <PythonEditor
              value={value}
              onChange={(value) => onChange(value)}
              variables={variables}
              height='350px'
              required
              label='Script'
              readOnly={readOnly}
              maxLength={EXECUTION_SCRIPT_MAX_LENGTH}
            />
          );
        }
      }}
    />
  );
};

const DatabaseConnectionInput = ({ form, readOnly: readonly }: Omit<Props, 'fetchGroup'>) => {
  const type = useWatch({ control: form.control, name: 'type' });

  const { data: databaseConnectionData } = useFetch(DatabaseConnectionApi.findAll({ orderBy: 'name' }), { showLoading: false });
  const databaesConnectionOptions = useMemo<ComboboxData>(() => {
    return (
      databaseConnectionData?.data
        ?.filter((connection) => connection.isActive)
        ?.map((connection) => ({ label: connection.name, value: `${connection.id}` })) || []
    );
  }, [databaseConnectionData?.data]);
  if (type === ExecutionTypeEnum.PYTHON) {
    return null;
  }
  return (
    <Controller
      name='databaseConnectionId'
      control={form.control}
      render={({ field: { onChange, value } }) => (
        <KanbanSelect
          label='Database Connection'
          required
          data={databaesConnectionOptions}
          value={value ? `${value}` : undefined}
          onChange={(value) => onChange(value ? parseInt(value) : undefined)}
          disabled={readonly}
          autoChangeValueByOptions={false}
          searchable
        />
      )}
    />
  );
};

const ExecutionForm = ({ fetchGroup, form, readOnly: readonly }: Props) => {
  const { control, setValue } = form;
  const id = useWatch({ control: control, name: 'id' });
  const isUpdateMode = !!id;
  return (
    <>
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <KanbanInput
            label='Name'
            required
            {...field}
            maxLength={MAX_CHARACTER_NAME_LENGTH}
            description={getMaxLengthMessage(MAX_CHARACTER_NAME_LENGTH)}
            disabled={readonly}
          />
        )}
      />
      <Controller
        name='description'
        control={control}
        render={({ field }) => (
          <KanbanInput
            label='Description'
            {...field}
            maxLength={MAX_DESCRIPTION_LENGTH}
            description={getMaxLengthMessage(MAX_DESCRIPTION_LENGTH)}
            disabled={readonly}
          />
        )}
      />
      <ExecutionGroupInput fetchGroup={fetchGroup} form={form} readOnly={readonly} />
      <Controller
        name='type'
        control={control}
        render={({ field: { onChange, value } }) => (
          <KanbanSelect
            label='Type'
            required
            data={Object.values(ExecutionTypeEnum).map((key) => ({ value: key, label: ExecutionTypeLabel[key] }))}
            value={value}
            onChange={(value) => {
              onChange(value);
              setValue('script', '');
            }}
            disabled={isUpdateMode || readonly}
          />
        )}
      />
      <DatabaseConnectionInput form={form} readOnly={readonly} />
      <ExecutionEditor form={form} readOnly={readonly} />
    </>
  );
};

export default ExecutionForm;
