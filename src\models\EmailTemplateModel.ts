import { HTML_CONTENT_VALIDATION_REGEX } from '@common/constants/RegexConstant';
import {
  MAX_CHARACTER_NAME_LENGTH,
  MAX_DESCRIPTION_LENGTH,
  MAX_EMAIL_ADDRESSES,
  MAX_EMAIL_SUBJECT_CHARACTER_LENGTH,
} from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { FileStorageModelSchema } from './FileStorageModel';
import { EmailModel } from './Common';

export const EmailTemplateModelSchema = z.object({
  id: z.number().optional(),
  name: z.string().trim().min(1).max(MAX_CHARACTER_NAME_LENGTH).optional(),
  description: z.string().max(MAX_DESCRIPTION_LENGTH, `Description should be at most ${MAX_DESCRIPTION_LENGTH} characters`).optional(),
  subject: z.string().min(1).max(MAX_EMAIL_SUBJECT_CHARACTER_LENGTH, `Subject should be at most ${MAX_EMAIL_SUBJECT_CHARACTER_LENGTH} characters`),
  files: z.array(z.instanceof(File)).optional(),
  fileStorages: z.array(FileStorageModelSchema).optional(),
  to: z
    .array(EmailModel)
    .max(MAX_EMAIL_ADDRESSES, { message: `The number of email addresses cannot exceed ${MAX_EMAIL_ADDRESSES}.` })
    .refine(
      (emails) => {
        const uniqueEmails = new Set(emails);
        return uniqueEmails.size === emails.length;
      },
      {
        message: 'Contact partner cannot be duplicate.',
      },
    )
    .optional(),
  cc: z
    .array(EmailModel)
    .max(MAX_EMAIL_ADDRESSES, { message: `The number of email addresses cannot exceed ${MAX_EMAIL_ADDRESSES}.` })
    .refine(
      (emails) => {
        const uniqueEmails = new Set(emails);
        return uniqueEmails.size === emails.length;
      },
      {
        message: 'Contact partner cannot be duplicate.',
      },
    )
    .optional(),
  content: z.string().refine(
    (value) => {
      const plainText = value.replace(HTML_CONTENT_VALIDATION_REGEX, '').trim();
      return plainText.length > 0;
    },
    {
      message: 'Content cannot be empty.',
    },
  ),
});

export type EmailTemplateModel = z.infer<typeof EmailTemplateModelSchema>;

export function createEmailTemplateFormData(data: EmailTemplateModel): FormData {
  const validatedData = EmailTemplateModelSchema.parse(data);
  const formData = new FormData();
  validatedData.files?.forEach((file) => {
    formData.append('files', file);
  });

  formData.append(
    'request',
    new Blob([JSON.stringify(data)], {
      type: 'application/json',
    }),
  );
  return formData;
}
