import { createRequest } from './Utils';
import { AlertGroupSearchRequest, PaginationRequest } from './Type';
import { BaseURL } from '@common/constants/BaseUrl';
import { AlertSchema, CloseAlertGroupsSchema, createPageSchema, createResponseSchema } from '@core/schema';
import { AlertGroupSchema } from '@core/schema/AlertGroup';
import { AlertGroupStatusEnum } from '@common/constants/AlertGroupStatusConstant';
import { z } from 'zod';

export class AlertGroupApi {
  static findAllByPostMethod(searchRequest: AlertGroupSearchRequest, paginationRequest: PaginationRequest) {
    return createRequest({
      url: `${BaseURL.alertGroup}/search`,
      method: 'POST',
      data: searchRequest,
      params: paginationRequest,
      schema: createResponseSchema(createPageSchema(AlertGroupSchema)),
    });
  }
  static close(alertGroupIds: number[]) {
    return createRequest({
      url: `${BaseURL.alertGroup}/close`,
      method: 'PUT',
      params: {
        alertGroupIds,
      },
      schema: createResponseSchema(CloseAlertGroupsSchema),
    });
  }
  static findAlertRecipientByAlertGroupStatus(alertGroupStatus: AlertGroupStatusEnum) {
    return createRequest({
      url: `${BaseURL.alertGroup}/recipients`,
      method: 'GET',
      schema: createResponseSchema(z.array(z.string())),
      params: {
        alertGroupStatus,
      },
    });
  }
  static findAllByAlertGroupId(id: number, paginationRequest: PaginationRequest) {
    return createRequest({
      url: `${BaseURL.alertGroup}/:id/alerts`,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(AlertSchema)),
      pathVariable: {
        id,
      },
      params: paginationRequest,
    });
  }
}
