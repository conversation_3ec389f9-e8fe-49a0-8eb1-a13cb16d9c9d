import { AlertGroupConfigTypeEnum, AlertGroupOutputEnum } from '@common/constants/AlertGroupConfigConstants';
import {
  MAX_ALERT_GROUP_CONFIG_DESCRIPTION_LENGTH,
  MAX_ALERT_GROUP_CONFIG_NAME_LENGTH,
  MIN_STRING_LENGTH,
} from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { QueryRuleGroupTypeModelSchema } from './RuleGroupTypeModel';
import { isEmpty, trim } from 'lodash';
import { ApplicationSchema, ServiceSchema } from '@core/schema';

export const AlertGroupConfigModelSchema = z
  .object({
    id: z.number().optional(),
    name: z.string().trim().min(MIN_STRING_LENGTH).max(MAX_ALERT_GROUP_CONFIG_NAME_LENGTH),
    description: z.string().max(MAX_ALERT_GROUP_CONFIG_DESCRIPTION_LENGTH).optional(),
    alertOutput: z.nativeEnum(AlertGroupOutputEnum),
    type: z.nativeEnum(AlertGroupConfigTypeEnum),
    customObjectIds: z.array(z.string()),
    ruleGroups: z.array(QueryRuleGroupTypeModelSchema),
    services: z.array(ServiceSchema).min(1),
    applications: z.array(ApplicationSchema),
    customServiceId: z.string().optional(),
    customApplicationId: z.string().optional(),
    customPriorityConfigId: z.string().optional(),
    customContent: z.string().trim().optional(),
    customRecipient: z.string().trim().optional(),
  })
  .superRefine((value, ctx) => {
    if (value.type === AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE && isEmpty(value.customObjectIds)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['customObjectIds'],
        message: 'Custom object can not empty',
      });
    }
    if (value.type === AlertGroupConfigTypeEnum.MULTIPLE_CONDITION && isEmpty(value.ruleGroups)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['ruleGroups'],
        message: 'Rule Group can not empty',
      });
    }
    if (value.alertOutput === AlertGroupOutputEnum.CUSTOM) {
      if (!value.customServiceId) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['customServiceId'], message: 'Service can not be empty' });
      }
      if (!value.customApplicationId) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['customApplicationId'], message: 'Application can not be empty' });
      }
      if (!value.customPriorityConfigId) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['customPriorityConfigId'], message: 'Priority Config can not be empty' });
      }
      if (!trim(value.customContent)) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['customContent'], message: 'Service can not empty' });
      }
      if (!trim(value.customRecipient)) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, path: ['customRecipient'], message: 'Contact can not be empty' });
      }
    }
  });

export type AlertGroupConfigModel = z.infer<typeof AlertGroupConfigModelSchema>;
