import { RequestConfig } from '../core/api/BaseApi';
import { BaseURL } from '@common/constants/BaseUrl';
import { PaginationRequest } from './Type';
import { createPageSchema, createResponseSchema, Page, ResponseData } from '@core/schema';
import { EmailConfig, EmailConfigSchema } from '@core/schema/EmailConfig';
import { BaseCollectEmailConfig, BaseCollectEmailConfigSchema } from '@core/schema/CollectEmailConfig';
import { z } from 'zod';
import { EmailConfigModel, EmailConfigPaginationRequest } from '@models/EmailConfigModel';

export class EmailConfigApi {
  static findAll(pagination: EmailConfigPaginationRequest): RequestConfig<ResponseData<Page<EmailConfig>>, PaginationRequest> {
    return {
      url: BaseURL.emailConfig,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(EmailConfigSchema)),
    };
  }

  static findById(id: number): RequestConfig<ResponseData<EmailConfig>> {
    return {
      url: `${BaseURL.emailConfig}/:id`,
      method: 'GET',
      schema: createResponseSchema(EmailConfigSchema),
      pathVariable: {
        id,
      },
    };
  }
  static activeOrInactive(id: number): RequestConfig<string> {
    return {
      url: `${BaseURL.emailConfig}/:id/toggle-status`,
      method: 'PUT',
      pathVariable: {
        id,
      },
    };
  }
  static findAllByEmailConfigId(id: number): RequestConfig<ResponseData<BaseCollectEmailConfig[]>> {
    return {
      url: `${BaseURL.emailConfig}/:id/collect-email-configs`,
      method: 'GET',
      schema: createResponseSchema(z.array(BaseCollectEmailConfigSchema)),
      pathVariable: {
        id,
      },
    };
  }
  static save(body: EmailConfigModel): RequestConfig<ResponseData<EmailConfig>> {
    return {
      url: BaseURL.emailConfig,
      method: 'POST',
      data: body,
    };
  }
  static testConnection(body: EmailConfigModel): RequestConfig<ResponseData<EmailConfig>> {
    return {
      url: `${BaseURL.emailConfig}/test-connection`,
      method: 'POST',
      data: body,
    };
  }

  static deleteById(id: number): RequestConfig<string> {
    return {
      url: `${BaseURL.emailConfig}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    };
  }

  static deleteByIdIn(ids: number[]): RequestConfig<string> {
    return {
      url: `${BaseURL.emailConfig}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    };
  }
}
