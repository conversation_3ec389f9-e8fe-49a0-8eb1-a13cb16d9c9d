import { DESCRIPTION_MAX_LENGTH, MAX_NAME_LENGTH, MIN_STRING_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { QueryRuleGroupTypeModelSchema } from './RuleGroupTypeModel';

export const FilterAlertConfigModelSchema = z.object({
  id: z.number().optional(),
  name: z.string().trim().min(MIN_STRING_LENGTH).max(MAX_NAME_LENGTH),
  description: z.string().trim().max(DESCRIPTION_MAX_LENGTH).optional(),
  ruleGroup: QueryRuleGroupTypeModelSchema,
});

export type FilterAlertConfigModel = z.infer<typeof FilterAlertConfigModelSchema>;
