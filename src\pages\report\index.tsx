import React, { useState, useCallback } from 'react';
import type { AlertFormFilterModel } from '@models/AlertModel';
import { Box, Flex, Stack, Text, Title } from '@mantine/core';
import { AlertApi } from 'api/AlertApi';
import { useDisclosure } from '@mantine/hooks';
import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import InfiniteScrollTable from '@components/dragTable/InfiniteScrollTable';
import { COLUMNS } from './AlertCommon';
import AlertFilterForm from './components/AlertFilterForm';
import GuardComponent from '@components/GuardComponent';
import { KanbanButton } from 'kanban-design-system';
import { IconFileExport } from '@tabler/icons-react';
import ExportFileModal from './components/ExportFileModal';
import { AclPermission } from '@models/AclPermission';
import { omit } from 'lodash';
import useFetch from '@core/hooks/useFetch';
import { AlertDurationCompareOperatorEnum, AlertDurationCompareUnitEnum, DEAFULT_PAGE_SIZE } from './Constants';

export const ReportViewPage = () => {
  const [openedExportFile, { close: closeExportFile, open: openExportFile }] = useDisclosure(false);
  const [formSearchParamsAlert, setFormSearchParamsAlert] = useState<AlertFormFilterModel>({
    services: [],
    applications: [],
    content: '',
    recipient: '',
    alertPriorityConfigIds: [],
    statuses: [],
    pageSize: DEAFULT_PAGE_SIZE,
    closeDurationOperator: AlertDurationCompareOperatorEnum.LESS_THAN,
    closeDurationUnit: AlertDurationCompareUnitEnum.MINUTE,
  });
  const [firstRender, setFirstRender] = useState(true);
  const { errorUpdateCount, fetchNextPage, flatData, isFetching, refetch } = useInfiniteCursorFetch(
    AlertApi.findAll({
      ...omit(formSearchParamsAlert, 'services', 'applications'),
      serviceIds: formSearchParamsAlert.services?.map((ele) => ele.id) || [],
      applicationIds: formSearchParamsAlert.applications?.map((ele) => ele.id) || [],
    }),
    {
      enabled: !firstRender,
    },
  );

  const {
    data,
    isLoading: isCountingFetching,
    refetch: refetchCounting,
  } = useFetch(
    AlertApi.countAllAlert({
      ...omit(formSearchParamsAlert, 'services', 'applications'),
      pageSize: DEAFULT_PAGE_SIZE,
      nextCursor: undefined,
      serviceIds: formSearchParamsAlert.services?.map((ele) => ele.id) || [],
      applicationIds: formSearchParamsAlert.applications?.map((ele) => ele.id) || [],
    }),
    { showLoading: false, enabled: !firstRender },
  );

  const handleSearchAlert = useCallback(
    (value: AlertFormFilterModel) => {
      setFormSearchParamsAlert({
        ...value,
        pageSize: DEAFULT_PAGE_SIZE,
        nextCursor: undefined,
      });
      refetch();
      refetchCounting();
    },
    [refetch, refetchCounting],
  );

  return (
    <Box pos='relative' h='var(--kanban-appshell-maxheight-content)'>
      <AlertFilterForm
        handleSearchAlert={(value: AlertFormFilterModel) => {
          setFirstRender(false);
          handleSearchAlert(value);
        }}
        searchParams={formSearchParamsAlert}
      />
      <Stack flex={1} h='100%'>
        <Flex justify='space-between'>
          <Title order={3} c='primary'>
            Report
          </Title>
          <Flex align='center' gap='sm'>
            {!!data?.data && !isCountingFetching && (
              <Text
                component='span'
                bg='primary.1'
                style={{ borderRadius: 'var(--mantine-radius-default)' }}
                p='calc(var(--mantine-spacing-md) / 2)'
                size='sm'
                fw='600'
                c='primary.5'>
                Filtered {data?.data || 0} records
              </Text>
            )}
            <GuardComponent requirePermissions={[AclPermission.reportExport]}>
              <KanbanButton
                onClick={() => {
                  openExportFile();
                }}
                disabled={firstRender || !flatData?.length}
                ml='sm'
                size='xs'
                variant='outline'
                leftSection={<IconFileExport />}>
                Export
              </KanbanButton>
            </GuardComponent>
          </Flex>
        </Flex>
        <Box flex={1} style={{ overflow: 'auto' }}>
          <InfiniteScrollTable
            columns={COLUMNS}
            data={flatData || []}
            onScrollToBottom={() => {
              if (errorUpdateCount < 1) {
                fetchNextPage();
              }
            }}
            loading={isFetching}
          />
        </Box>
      </Stack>
      <ExportFileModal opened={openedExportFile} onClose={closeExportFile} tableAffected={formSearchParamsAlert} />
    </Box>
  );
};
export default ReportViewPage;
