import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ColumnType, KanbanButton, KanbanCheckbox, KanbanInput } from 'kanban-design-system';
import Modal from '@components/Modal';
import { useDisclosure } from '@mantine/hooks';
import Table from '@components/table';
import { RoleApi } from '@api/RoleApi';
import { Role } from '@core/schema/Role';
import useFetch from '@core/hooks/useFetch';
import { UserDetail } from '@core/schema/UserDetails';
import useMutate from '@core/hooks/useMutate';
import { UserApi } from '@api/UserApi';
import { refetchRequest } from '@common/utils/QueryUtils';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { Flex, Pill, Title } from '@mantine/core';

interface Props {
  userDetail: UserDetail;
}

const AddRoleToUserModal = ({ userDetail }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const { data: roles } = useFetch(RoleApi.findAll(), {
    enabled: opened,
  });
  const [selectedRoles, setSelectedRoles] = useState<Role[]>(userDetail?.roles || []);
  const [searchValue, setSearchValue] = useState('');
  const tableData = useMemo<Role[]>(
    () =>
      roles?.data
        ?.filter((role) => role.active)
        ?.filter(
          (role) =>
            role.name.toLowerCase().includes(searchValue?.toLowerCase()) || role.description?.toLowerCase()?.includes(searchValue?.toLowerCase()),
        )
        ?.sort((a, b) => a.name.localeCompare(b.name)) || [],
    [roles?.data, searchValue],
  );
  const columns = useMemo<ColumnType<Role>[]>(
    () => [
      {
        name: 'check',
        title: '',
        customRender: (_, record) => (
          <KanbanCheckbox
            checked={selectedRoles?.some((role) => role.id === record.id)}
            onChange={(event) => {
              if (event.currentTarget.checked) {
                setSelectedRoles((prev) => [...prev, record]);
              } else {
                setSelectedRoles((prev) => prev.filter((role) => role.id !== record.id));
              }
            }}
          />
        ),
      },
      {
        name: 'name',
        title: 'Role Name',
        customRender: (_, record) => record.name,
      },
      {
        name: 'description',
        title: 'Description',
        customRender: (_, record) => record.description,
      },
    ],
    [selectedRoles],
  );

  const { mutate: addNewRoleIntoUserMutate } = useMutate(RoleApi.addRolesToUser, {
    successNotification: () => {
      return {
        title: 'Add new role',
        message: `Update role for user ${userDetail.userName} successfully`,
      };
    },
    onSuccess: () => {
      setSelectedRoles([]);
      setSearchValue('');
      refetchRequest(UserApi.findWithRolesById(userDetail.id));
      close();
    },
  });
  useEffect(() => setSelectedRoles(userDetail.roles || []), [userDetail.roles]);
  const onAddRolesToUser = useCallback(() => {
    addNewRoleIntoUserMutate({ userId: userDetail.id, roleIds: selectedRoles.map((role) => role.id) });
  }, [addNewRoleIntoUserMutate, selectedRoles, userDetail.id]);

  return (
    <>
      <KanbanButton onClick={open}>Add Role</KanbanButton>
      <Modal
        size={'xl'}
        opened={opened}
        onClose={() => {
          close();
          setSelectedRoles(userDetail?.roles || []);
          setSearchValue('');
        }}
        title='Add role to user'
        actions={
          userDetail && (
            <GuardComponent requirePermissions={[AclPermission.userManageEdit]}>
              <KanbanButton onClick={onAddRolesToUser}>Save</KanbanButton>
            </GuardComponent>
          )
        }>
        <KanbanInput maxLength={100} placeholder='Search...' value={searchValue} onChange={(event) => setSearchValue(event.target.value)} />
        {selectedRoles?.length > 0 && (
          <Flex my='sm' gap='xs' wrap='wrap'>
            <Title order={5}>Selected roles:</Title>
            {selectedRoles.map((role) => (
              <Pill key={role.id} withRemoveButton onRemove={() => setSelectedRoles((prev) => prev.filter((ele) => role.id !== ele.id))}>
                {role.name}
              </Pill>
            ))}
          </Flex>
        )}
        <Table columns={columns} data={tableData} />
      </Modal>
    </>
  );
};

export default AddRoleToUserModal;
