import _ from 'lodash';
export const formatStandardName = (input: string) => {
  const words = input.trim().split(/\s+/);
  // if(words.length < 2 && input.toUpperCase() !== input ){
  //     const newInput = input.replace(/([A-Z])/g, ' $1').trim();
  //     words = newInput.split(/\s+/);
  // }

  const output = words.join(' ');

  return output.charAt(0).toUpperCase() + output.slice(1).toLowerCase();
};
export const isValidRegex = (pattern: string) => {
  try {
    new RegExp(pattern);
    return true;
  } catch {
    return false;
  }
};

export const trimDeepObject = <T>(value: T): T => {
  if (_.isString(value)) {
    return _.trim(value) as unknown as T;
  } else if (_.isArray(value)) {
    return value.map(trimDeepObject) as unknown as T;
  } else if (_.isPlainObject(value) && value !== null) {
    return _.mapValues(value, trimDeepObject) as T;
  }
  return value;
};
export const areAllArraysEmpty = (data: Record<string, unknown>): boolean => {
  return Object.values(data).every((value) => Array.isArray(value) && value.length === 0);
};
