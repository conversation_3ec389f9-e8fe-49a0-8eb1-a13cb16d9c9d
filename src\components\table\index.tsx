import React from 'react';
import { KanbanTable, KanbanTableProps, KanbanTableSelectHandleMethods } from 'kanban-design-system';
import classes from './Table.module.css';
import { TABLE_INPUT_MAX_LENGTH } from '@common/constants/ValidationConstant';

interface Props<T extends Record<string, any>> extends KanbanTableProps<T> {}
const Table = <T extends Record<string, any>>(props: Props<T>, ref: React.ForwardedRef<KanbanTableSelectHandleMethods>) => {
  return (
    <KanbanTable
      ref={ref}
      showTopBar={false}
      {...props}
      searchable={props.searchable && { ...props.searchable, inputProps: { maxLength: TABLE_INPUT_MAX_LENGTH, ...props.searchable.inputProps } }}
      pagination={props.pagination && { withEdges: false, ...props.pagination }}
      tableWrapperClasses={`${classes.customTableWrapper} ${props.tableWrapperClasses || ''}`}
    />
  );
};

export default React.forwardRef(Table) as typeof KanbanTable;
