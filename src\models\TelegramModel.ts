import { TELEGRAM_GROUP_ID_REGEX } from '@common/constants/RegexConstant';
import { DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { TELEGRAM_GROUP_ID_REGEX_INPUT_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { z } from 'zod';

export const TelegramModelSchema = z.object({
  id: z.string().optional(),
  botToken: z.string().max(100).optional(),
  tokenPlaceHolder: z.string().optional(),
  defaultGroupChatId: z.string().max(30).trim().regex(TELEGRAM_GROUP_ID_REGEX, { message: TELEGRAM_GROUP_ID_REGEX_INPUT_MESSAGE_ERROR }),
  description: z.string().trim().max(DESCRIPTION_MAX_LENGTH).optional(),
  isActive: z.boolean(),
});
export type TelegramModel = z.infer<typeof TelegramModelSchema>;
