import { Box, Button, Flex, Modal, Stack, Title } from '@mantine/core';
import React, { useCallback, useContext, useMemo } from 'react';
import { getTableTitle, groupConsecutiveDates } from '../calendarSection/Utils';
import CreateTaskButton from './CreateTaskButton';
import TaskFilter from './TaskFilter';
import { TaskApi } from '@api/TaskApi';
import InfiniteScrollTable from '@components/dragTable/InfiniteScrollTable';
import { Column } from '@components/dragTable/Types';
import { Task } from '@core/schema/Task';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { TaskTimeTypeEnum, TaskTypeColor } from '@common/constants/TaskConstants';
import { useNavigate } from 'react-router-dom';
import { ROUTE_PATH } from '@common/utils/RouterUtils';

import { refetchEventPageData } from './Utilts';
import { EventPageContext } from '../EventPageContext';
import TaskTableAction from './TaskTableAction';
import TaskStatus from './TaskStatus';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';

import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import { DEFAULT_TASK_PAGE_SIZE } from '../Contants';
import EmptyBox from '@components/EmptyBox';
export const COLUMNS: Column<Task>[] = [
  {
    id: 'taskName',
    title: 'Task Name',
    render: (task) => (
      <Flex align='center' gap='xs'>
        <Box miw={10} mih={10} style={{ borderRadius: 10 }} bg={TaskTypeColor[task.type]}></Box>
        {task.name}
      </Flex>
    ),
    width: '35%',
  },
  {
    id: 'status',
    title: 'Status',
    render: (task) => <TaskStatus status={task.status} />,
  },
  {
    id: 'dateTime',
    title: 'Date time',
    render: (data) => (
      <Stack gap='calc(var(--mantine-spacing-xs) / 2)'>
        <Box>
          <span>Start: </span>
          {data.startTime?.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}
        </Box>
        {data.timeType === TaskTimeTypeEnum.FROM_TIME_TO_TIME && (
          <Box>
            <span>End: </span>
            {data.endTime?.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}
          </Box>
        )}
      </Stack>
    ),
  },
  {
    id: 'creator',
    title: 'Creator',
    render: 'createdBy',
  },
  {
    id: 'assignee',
    title: 'Assignee',
    render: 'currentAssigneeUserName',
  },
  {
    id: 'action',
    title: 'Action',
    render: (task) => <TaskTableAction task={task} />,
  },
];

const TaskListSection = () => {
  const { calendarMode, filterValue } = useContext(EventPageContext);
  const { selectedDates } = calendarMode;
  const navigate = useNavigate();
  const { errorUpdateCount, fetchNextPage, flatData, isFetching } = useInfiniteCursorFetch(
    TaskApi.findAll(
      {
        ...filterValue,
        dateRanges: selectedDates.length
          ? groupConsecutiveDates(selectedDates).map((range) => {
              return {
                fromDate: range.at(0)?.startOf('date').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
                toDate: range
                  .at(range.length - 1)
                  ?.endOf('date')
                  .format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
              };
            })
          : filterValue.dateRanges,
      },
      { pageSize: DEFAULT_TASK_PAGE_SIZE },
    ),
    { showLoading: false, placeholderData: (prev) => prev },
  );

  const hasCreatePermission = useMemo(() => isAnyPermissions([AclPermission.taskCreate]), []);
  const onCreateTaskSuccess = useCallback(() => {
    refetchEventPageData(calendarMode, filterValue);
  }, [calendarMode, filterValue]);
  const tableTitle = getTableTitle(calendarMode);

  return (
    <Modal.Stack>
      <Stack gap='xs' h='var(--kanban-appshell-maxheight-content)' bg='white' p='sm'>
        <Flex align='center' justify='space-between'>
          <Title order={3} style={{ color: 'var(--mantine-primary-color-7)' }}>
            Task Or Shift Handover {tableTitle ? `- ${tableTitle}` : ''}
          </Title>
          {hasCreatePermission && (
            <Flex align='center' justify='center' gap='sm'>
              <Button.Group>
                <Button
                  variant='outline'
                  onClick={() => {
                    navigate({
                      pathname: `${ROUTE_PATH.EVENT}/0`,
                    });
                  }}>
                  Create Shift handover
                </Button>
                <CreateTaskButton onCreateTaskSuccess={onCreateTaskSuccess} />
              </Button.Group>
            </Flex>
          )}
        </Flex>
        <TaskFilter />
        <Box flex={1} style={{ overflow: 'scroll' }}>
          {flatData && flatData.length > 0 ? (
            <InfiniteScrollTable
              columns={COLUMNS}
              data={flatData || []}
              showIndexColumn={false}
              onScrollToBottom={() => {
                if (errorUpdateCount < 1) {
                  fetchNextPage();
                }
              }}
              loading={isFetching}
            />
          ) : (
            <Stack gap='xs' flex={1} align='center' justify='center'>
              <EmptyBox />
              <Title order={4} c='var(--mantine-color-gray-5)'>
                Select a date to view job or event details
              </Title>
            </Stack>
          )}
        </Box>
      </Stack>
    </Modal.Stack>
  );
};

export default TaskListSection;
