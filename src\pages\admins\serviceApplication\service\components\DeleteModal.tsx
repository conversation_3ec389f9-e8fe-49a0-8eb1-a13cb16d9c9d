import React from 'react';
import { KanbanButton } from 'kanban-design-system';
import { ServiceApi } from '@api/ServiceApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { areAllArraysEmpty } from '@common/utils/StringUtils';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

type DeleteModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  serviceId?: string;
  serviceName?: string;
};

const DeleteModal: React.FC<DeleteModalProps> = ({ onClose, opened, refetchList, serviceId, serviceName }) => {
  const { data: dependencies } = useFetch(ServiceApi.findAllDependenciesById(serviceId ?? ''), {
    enabled: !!serviceId && opened,
  });
  const dependencyConfig: DependencyItem[] = [
    {
      dependencyEntity: 'applications',
      dependencies: dependencies?.data?.applications ?? [],
    },
    {
      dependencyEntity: 'webhooks',
      dependencies: dependencies?.data?.webHooks ?? [],
    },
    {
      dependencyEntity: 'collect email configs',
      dependencies: dependencies?.data?.collectEmailConfigs ?? [],
    },
    {
      dependencyEntity: 'collect database configs',
      dependencies: dependencies?.data?.databaseCollects ?? [],
    },
    {
      dependencyEntity: 'alert group configs',
      dependencies: dependencies?.data?.alertGroupConfigs ?? [],
    },
    {
      dependencyEntity: 'maintenance time configs',
      dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
    },
    {
      dependencyEntity: 'modify alert configs',
      dependencies: dependencies?.data?.modifyAlertConfigs ?? [],
    },
    {
      dependencyEntity: 'database threshold configs',
      dependencies: dependencies?.data?.databaseThresholdConfigs ?? [],
    },
  ];
  const { mutate: deleteByIdMutate } = useMutate(ServiceApi.deleteById, {
    successNotification: { message: 'Deleted successfully!' },
    onSuccess: () => {
      refetchList();
      onClose();
    },
  });
  const isDisabledButtonConfirm = !areAllArraysEmpty(dependencies?.data || {});

  return (
    <Modal
      opened={opened}
      size='xl'
      onClose={() => {
        onClose();
      }}
      title={'Delete Service'}
      actions={
        <KanbanButton onClick={() => deleteByIdMutate(serviceId ?? '')} disabled={isDisabledButtonConfirm}>
          Confirm
        </KanbanButton>
      }>
      <DependenciesWarningAlert mainEntity={`Service ${serviceName}`} dependencyConfigs={dependencyConfig} isDeleted={true} />
      {!isDisabledButtonConfirm && <DeleteConfirmMessage name={serviceName} />}
    </Modal>
  );
};

export default DeleteModal;
