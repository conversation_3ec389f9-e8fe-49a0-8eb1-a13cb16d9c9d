import { Grid } from '@mantine/core';
import React from 'react';
import CalendarSection from './calendarSection';
import TaskListSection from './taskListSection';
import { EventPageProvider } from './EventPageContext';

const EventPage = () => {
  return (
    <EventPageProvider>
      <Grid>
        <Grid.Col span={6}>
          <CalendarSection />
        </Grid.Col>
        <Grid.Col span={6}>
          <TaskListSection />
        </Grid.Col>
      </Grid>
    </EventPageProvider>
  );
};

export default EventPage;
