import { z } from 'zod';
import { QueryRuleGroupTypeSchema } from './RuleGroupCondition';
import { ServiceSchema } from './Service';
import { ApplicationSchema } from './Application';
import { MaintenanceTimeTypeEnum, TIME_UNIT_LABEL } from '@common/constants/MaintenanceTimeConfigConstants';

export const BaseMaintenanceTimeConfigSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(MaintenanceTimeTypeEnum),
  active: z.boolean(),
  nextTime: z.number().optional(),
  unit: z.nativeEnum(TIME_UNIT_LABEL).optional(),
  value: z.string().optional(),
  status: z.string().optional(),
  cronExpression: z.string().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  ruleGroup: QueryRuleGroupTypeSchema,
});

export type BaseMaintenanceTimeConfig = z.infer<typeof BaseMaintenanceTimeConfigSchema>;
export const MaintenanceTimeConfigSchema = BaseMaintenanceTimeConfigSchema.extend({
  services: z.array(ServiceSchema).min(1),
  applications: z.array(ApplicationSchema).optional(),
});

export type MaintenanceTimeConfig = z.infer<typeof MaintenanceTimeConfigSchema>;
