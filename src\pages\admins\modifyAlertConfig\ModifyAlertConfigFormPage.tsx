import { isNaN, parseInt } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';
import { KanbanButton, KanbanInput } from 'kanban-design-system';
import classes from './GroupConfigStyle.module.css';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import ConditionSession from './ConditionSession';
import { DEFAULT_FORM_VALUE, ModifyAlertConfigAction } from './Constants';
import ReferenceSession from './ReferenceSession';
import { DESCRIPTION_MAX_LENGTH, MAX_NAME_LENGTH } from '@common/constants/ValidationConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { ModifyAlertConfigModel, ModifyAlertConfigModelSchema } from '@models/ModifyAlertConfigModel';
import { ModifyAlertConfigApi } from '@api/ModifyAlertConfigApi';
import ModifyField from './ModifyField';

const SaveButton = ({ form }: { form: UseFormReturn<ModifyAlertConfigModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveModifyAlertConfigMutate } = useMutate(ModifyAlertConfigApi.save, {
    onSuccess: () => {
      navigate('../');
    },
  });
  const { formState } = form;
  const onSubmit = useCallback(() => {
    const parsedData = ModifyAlertConfigModelSchema.safeParse(form.getValues());

    if (parsedData.success) {
      const data = parsedData.data;
      saveModifyAlertConfigMutate({
        ...data,
        serviceIds: data.services.map((ele) => ele.id),
        applicationIds: data.applications?.map((ele) => ele.id) || [],
      });
    }
  }, [form, saveModifyAlertConfigMutate]);
  return (
    <GuardComponent requirePermissions={[AclPermission.modifyAlertConfigEdit, AclPermission.modifyAlertConfigCreate]}>
      <KanbanButton onClick={onSubmit} disabled={!formState.isValid}>
        Save
      </KanbanButton>
    </GuardComponent>
  );
};
const ModifyAlertConfigFormPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { modifyAlertConfigId: modifyAlertConfigStringId } = useParams();
  const modifyAlertConfigId = !!modifyAlertConfigStringId && !isNaN(modifyAlertConfigStringId) ? parseInt(modifyAlertConfigStringId) : 0;
  const isCreateMode = !modifyAlertConfigId;
  const [currentTab, setCurrentTab] = useState<string>(ModifyAlertConfigAction.CREATE);
  const isViewMode = currentTab === ModifyAlertConfigAction.VIEW;

  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);

  const form = useForm<ModifyAlertConfigModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(ModifyAlertConfigModelSchema),
    mode: 'onChange',
  });

  const { data: modifyAlertConfigData } = useFetch(ModifyAlertConfigApi.findById(modifyAlertConfigId), { enabled: !isCreateMode });

  useEffect(() => {
    if (!isCreateMode && modifyAlertConfigData?.data) {
      const { data } = modifyAlertConfigData;
      form.reset({
        ...data,
        services: data?.services || [],
        applications: data?.applications || [],
      });
    }
  }, [modifyAlertConfigData, modifyAlertConfigData?.data, form, isCreateMode]);
  return (
    <Box>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>
          {isViewMode ? 'View modify alert config' : isCreateMode ? 'Create modify alert config' : 'Update modify alert config'}
        </Title>
        <Flex>
          <Group>
            <KanbanButton variant='outline' onClick={() => navigate('../')}>
              Cancel
            </KanbanButton>
            {!isViewMode && <SaveButton form={form} />}
          </Group>
        </Flex>
      </Flex>
      <Stack gap='md'>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>General Information</Title>
          <Controller
            control={form.control}
            name='name'
            render={({ field, formState }) => {
              return (
                <KanbanInput
                  disabled={isViewMode}
                  label='Config name'
                  required
                  {...field}
                  maxLength={MAX_NAME_LENGTH}
                  error={formState.errors.root?.message}
                />
              );
            }}
          />
          <Controller
            control={form.control}
            name='description'
            render={({ field, formState }) => {
              return (
                <KanbanInput
                  disabled={isViewMode}
                  label='Description'
                  {...field}
                  maxLength={DESCRIPTION_MAX_LENGTH}
                  error={formState.errors.root?.message}
                />
              );
            }}
          />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Reference</Title>
          <ReferenceSession isViewMode={isViewMode} form={form} />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Condition</Title>
          <ConditionSession isViewMode={isViewMode} form={form} />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Modify Field</Title>
          <ModifyField isViewMode={isViewMode} form={form} />
        </Stack>
      </Stack>
    </Box>
  );
};

export default ModifyAlertConfigFormPage;
