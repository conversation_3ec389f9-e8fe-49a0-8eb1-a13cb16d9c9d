import { z } from 'zod';
import { SysLogActionEnum, SysLogFunctionEnum } from '@common/constants/SysLogConstants';
import { DateRangeTypeEnum } from '@components/dateRange/Constants';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@common/constants/DateConstants';

export const SysLogFilterModelSchema = z
  .object({
    functions: z.array(z.nativeEnum(SysLogFunctionEnum)),
    actions: z.array(z.nativeEnum(SysLogActionEnum)),
    userNames: z.array(z.string()),
    message: z.string().optional(),
    fromDate: z.string(),
    toDate: z.string(),
    pageSize: z.number(),
    rangeType: z.nativeEnum(DateRangeTypeEnum).optional(),
  })
  .superRefine((value, ctx) => {
    const fromDate = dayjs(value.fromDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS);
    const toDate = dayjs(value.toDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS);
    if (fromDate.isAfter(toDate)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['fromDate'],
        message: 'From Date can not after To Date',
      });
    } else if (toDate.diff(fromDate, 'year', true) > 1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['fromDate'],
        message: 'Only allow a period of 1 year',
      });
    }
  });

export type SysLogFilterModel = z.infer<typeof SysLogFilterModelSchema>;
