import React, { useMemo } from 'react';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import useFetch from '@core/hooks/useFetch';
import { ActionIcon, Box, Center, Collapse, Flex, Loader, Stack } from '@mantine/core';
import { KanbanCheckbox, KanbanText } from 'kanban-design-system';
import { PERMISSION_MODULE_LABEL, PermissionModuleEnum } from '@common/constants/PermissionModule';
import { IconChevronDown, IconChevronRight } from '@tabler/icons-react';
import { ExecutionGroup } from '@core/schema/ExecutionGroup';
import { useDisclosure } from '@mantine/hooks';
import { ExecutionApi } from '@api/ExecutionApi';
import { RoleModel } from '@models/RoleModel';
import { PermissionSettingModel } from './RoleSetting';
import { PermissionActionEnum } from '@common/constants/PermissionAction';
import { PermissionTypeEnum } from '@common/constants/PermissionType';
type Props = {
  userRoles: RoleModel;
  onChange: (data: PermissionSettingModel) => void;
};

const ExecutionGroupPermission = ({ executionGroup, onChange, userRoles }: { executionGroup: ExecutionGroup } & Props) => {
  const [opened, { toggle }] = useDisclosure(false);
  const {
    data: executionsData,
    isFetching,
    refetch,
  } = useFetch(ExecutionApi.findAllByExecutionGroupId(executionGroup.id), {
    enabled: opened,
    showLoading: false,
  });
  const isGroupChecked = useMemo(() => {
    return userRoles.permissions?.some(
      (permisison) =>
        permisison.module === PermissionModuleEnum.RUN_EXECUTION && permisison.moduleId === executionGroup.id && !permisison.moduleParentId,
    );
  }, [executionGroup.id, userRoles.permissions]);
  const numberExecutionChecked = useMemo(() => {
    if (isGroupChecked) {
      return executionGroup.executionAmount || 0;
    }
    return (
      userRoles?.permissions?.filter(
        (permisison) => permisison.module === PermissionModuleEnum.RUN_EXECUTION && permisison.moduleParentId === executionGroup.id,
      ).length || 0
    );
  }, [executionGroup.executionAmount, executionGroup.id, isGroupChecked, userRoles?.permissions]);

  return (
    <Box>
      <Flex align='center' justify='space-between' mb='xs'>
        <KanbanCheckbox
          styles={{ input: { cursor: 'pointer' } }}
          checked={isGroupChecked}
          onChange={async (event) => {
            const checked = event.target.checked;
            onChange({
              action: PermissionActionEnum.EXECUTE,
              module: PermissionModuleEnum.RUN_EXECUTION,
              type: PermissionTypeEnum.SUB_MODULE,
              isChecked: checked,
              moduleId: executionGroup.id,
            });
            const data = executionsData?.data || (await refetch()).data?.data;
            if (data) {
              data?.forEach((ele) => {
                onChange({
                  action: PermissionActionEnum.EXECUTE,
                  module: PermissionModuleEnum.RUN_EXECUTION,
                  type: PermissionTypeEnum.SUB_MODULE,
                  isChecked: checked,
                  moduleId: ele.id,
                  moduleParentId: executionGroup.id,
                });
              });
            }
          }}
          label={
            <Flex gap='sm' align='center'>
              <KanbanText truncate maw='300px'>
                {executionGroup.name}
              </KanbanText>
              <KanbanText span fw={700} pl={5}>
                ({numberExecutionChecked}/{executionGroup.executionAmount ?? 0})
              </KanbanText>
            </Flex>
          }
        />
        <ActionIcon
          variant='outline'
          size='sm'
          onClick={() => {
            if (!isFetching) {
              toggle();
            }
          }}>
          {isFetching ? <Loader size='xs' /> : opened ? <IconChevronDown /> : <IconChevronRight />}
        </ActionIcon>
      </Flex>
      <Collapse in={opened && (executionsData?.data?.length || 0) > 0}>
        <Stack pl='md' gap='xs'>
          {executionsData?.data?.map((execution) => {
            const isExecutionChecked =
              isGroupChecked ||
              userRoles.permissions?.some(
                (permisison) =>
                  permisison.module === PermissionModuleEnum.RUN_EXECUTION &&
                  permisison.moduleId === execution.id &&
                  permisison.moduleParentId === executionGroup.id,
              );
            return (
              <KanbanCheckbox
                key={execution.id}
                checked={isExecutionChecked}
                onChange={(event) => {
                  const checked = event.target.checked;
                  if (checked) {
                    onChange({
                      action: PermissionActionEnum.EXECUTE,
                      module: PermissionModuleEnum.RUN_EXECUTION,
                      type: PermissionTypeEnum.SUB_MODULE,
                      isChecked: checked,
                      moduleId: execution.id,
                      moduleParentId: executionGroup.id,
                    });
                  } else {
                    // uncheck parent
                    onChange({
                      action: PermissionActionEnum.EXECUTE,
                      module: PermissionModuleEnum.RUN_EXECUTION,
                      type: PermissionTypeEnum.SUB_MODULE,
                      isChecked: false,
                      moduleId: executionGroup.id,
                    });
                    onChange({
                      action: PermissionActionEnum.EXECUTE,
                      module: PermissionModuleEnum.RUN_EXECUTION,
                      type: PermissionTypeEnum.SUB_MODULE,
                      isChecked: false,
                      moduleId: execution.id,
                      moduleParentId: executionGroup.id,
                    });
                    if (isGroupChecked) {
                      executionsData?.data
                        ?.filter((ele) => ele.id !== execution.id)
                        ?.forEach((ele) =>
                          onChange({
                            action: PermissionActionEnum.EXECUTE,
                            module: PermissionModuleEnum.RUN_EXECUTION,
                            type: PermissionTypeEnum.SUB_MODULE,
                            isChecked: true,
                            moduleId: ele.id,
                            moduleParentId: executionGroup.id,
                          }),
                        );
                    }
                  }
                }}
                styles={{ input: { cursor: 'pointer' } }}
                label={
                  <KanbanText truncate maw='300px'>
                    {execution.name}
                  </KanbanText>
                }
              />
            );
          })}
        </Stack>
      </Collapse>
    </Box>
  );
};

export const RunExecutionPermission = ({ onChange, userRoles }: Props) => {
  const { data: executionGroupData } = useFetch(ExecutionGroupApi.findAllWithExecutionAmount());
  return (
    <Stack gap='sm'>
      <Flex align='center' justify='space-between'>
        <KanbanText pb={10} pt={20} fw={700} c='blue'>
          {PERMISSION_MODULE_LABEL[PermissionModuleEnum.RUN_EXECUTION]}
        </KanbanText>
        {(!executionGroupData?.data || executionGroupData?.data?.length === 0) && (
          <Center>
            <KanbanText>No data</KanbanText>
          </Center>
        )}
      </Flex>
      {executionGroupData?.data?.map((group) => (
        <ExecutionGroupPermission key={group.id} executionGroup={group} userRoles={userRoles} onChange={onChange} />
      ))}
    </Stack>
  );
};
export default RunExecutionPermission;
