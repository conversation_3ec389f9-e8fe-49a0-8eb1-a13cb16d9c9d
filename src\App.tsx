import React, { useEffect } from 'react';
import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/spotlight/styles.css';
import '@mantine/dropzone/styles.css';
import { MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { Default } from 'core/themes/Default';
import PageLoadingComponent from '@components/PageLoadingComponent';
import { ZIndexNotification } from '@common/constants/ZIndexConstants';
import { getConfigs } from '@core/configs/Configs';
import { KanbanModalProvider } from 'kanban-design-system';
import { QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, useNavigate } from 'react-router-dom';
import Index from './pages';
import { queryClient } from '@core/configs/QueryClient';
import { HistoryRouter } from '@common/utils/RouterUtils';

const appName = getConfigs().fullname;

function AppContent() {
  const navigate = useNavigate();

  useEffect(() => {
    HistoryRouter.navigate = navigate;
    const styles = ['font-size: 18px', 'color: red'];
    // eslint-disable-next-line no-console
    console.log(
      `%cWelcome to the \`${appName}\` project, developed by \`Kanban\` team. Please notify the administrator if there is a problem`,
      styles.join(';'),
    );
  }, [navigate]);

  return (
    <>
      <Notifications limit={5} zIndex={ZIndexNotification} />
      <PageLoadingComponent />
      <Index />
    </>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <MantineProvider theme={Default}>
          <KanbanModalProvider>
            <AppContent />
          </KanbanModalProvider>
        </MantineProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
}

export default App;
