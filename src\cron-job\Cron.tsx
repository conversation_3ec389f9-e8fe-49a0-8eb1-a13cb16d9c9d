/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import { useCallback, useEffect, useRef, useState } from 'react';

import { getCronStringFromValues, setValuesFromCronString } from './ConverterSelect';
import Hours from './fields/Hours';
import Minutes from './fields/Minutes';
import MonthDays from './fields/MonthDays';
import Months from './fields/Months';
import Period from './fields/Period';
import WeekDays from './fields/WeekDays';
import { DEFAULT_LOCALE_EN } from './LocaleSelect';
import { CronProps, PeriodType, SetValuePeriod } from './TypesSelect';
import { usePrevious } from './UtilsFields';
import React from 'react';
import { Flex, SimpleGrid } from '@mantine/core';

export default function Cron(props: CronProps) {
  const {
    allowClear,
    allowedDropdowns = ['period', 'months', 'month-days', 'week-days', 'hours', 'minutes'],
    allowedPeriods = ['year', 'month', 'week', 'day', 'hour', 'minute', 'reboot'],
    allowEmpty = 'for-default-value',
    className,
    clockFormat,
    defaultPeriod = 'day',
    disabled = false,
    dropdownsConfig,
    humanizeLabels = true,
    humanizeValue = false,
    leadingZero = false,
    locale = DEFAULT_LOCALE_EN,
    mode = 'multiple',
    onError,
    periodicityOnDoubleClick = true,
    readOnly = false,
    setValue,
    shortcuts = ['@yearly', '@annually', '@monthly', '@weekly', '@daily', '@midnight', '@hourly'],
    value,
  } = props;
  const internalValueRef = useRef<string>(value);
  const defaultPeriodRef = useRef<PeriodType>(defaultPeriod);
  const [period, setPeriod] = useState<PeriodType>(defaultPeriod);
  const [monthDays, setMonthDays] = useState<number[] | undefined>();
  const [months, setMonths] = useState<number[] | undefined>();
  const [weekDays, setWeekDays] = useState<number[] | undefined>();
  const [hours, setHours] = useState<number[] | undefined>();
  const [minutes, setMinutes] = useState<number[] | undefined>();
  const [_, setInternalError] = useState<boolean>(false);
  const [valueCleared, setValueCleared] = useState<boolean>(false);
  const previousValueCleared = usePrevious(valueCleared);

  const initializeCron = useRef(() => {
    internalValueRef.current = value;
    setValuesFromCronString(
      value,
      setInternalError,
      onError,
      allowEmpty,
      internalValueRef,
      false,
      locale,
      shortcuts,
      setMinutes,
      setHours,
      setMonthDays,
      setMonths,
      setWeekDays,
      setPeriod,
    );
  });

  useEffect(() => {
    initializeCron.current();
  }, []);

  useEffect(() => {
    if (value !== internalValueRef.current) {
      setValuesFromCronString(
        value,
        setInternalError,
        onError,
        allowEmpty,
        internalValueRef,
        false,
        locale,
        shortcuts,
        setMinutes,
        setHours,
        setMonthDays,
        setMonths,
        setWeekDays,
        setPeriod,
      );
    }
  }, [value, internalValueRef, allowEmpty, shortcuts, onError, locale]);

  const setValueRef = useRef(setValue);

  useEffect(() => {
    setValueRef.current = setValue;
  }, [setValue]);

  useEffect(() => {
    if ((period || minutes || months || monthDays || weekDays || hours) && !valueCleared && !previousValueCleared) {
      const selectedPeriod = period || defaultPeriodRef.current;
      const cron = getCronStringFromValues(selectedPeriod, months, monthDays, weekDays, hours, minutes, humanizeValue, dropdownsConfig);

      setValueRef.current(cron, { selectedPeriod });
      internalValueRef.current = cron;

      onError && onError(undefined);
      setInternalError(false);
    } else if (valueCleared) {
      setValueCleared(false);
    }
  }, [period, monthDays, months, weekDays, hours, minutes, humanizeValue, valueCleared, dropdownsConfig, previousValueCleared, onError]);

  const periodForRender = period || defaultPeriodRef.current;

  const onPeriodChange = useCallback<SetValuePeriod>(
    (value) => {
      if (period !== value) {
        setPeriod(value);
        setMinutes([]);
        setHours([]);
        setMonthDays([]);
        setMonths([]);
        setWeekDays([]);
      }
    },
    [period],
  );

  return (
    <div>
      <Flex direction='column' gap='sm'>
        <SimpleGrid cols={3}>
          {allowedDropdowns.includes('period') && (
            <Period
              value={periodForRender}
              setValue={onPeriodChange}
              locale={locale}
              className={className}
              disabled={dropdownsConfig?.period?.disabled ?? disabled}
              readOnly={dropdownsConfig?.period?.readOnly ?? readOnly}
              shortcuts={shortcuts}
              allowedPeriods={allowedPeriods}
              allowClear={dropdownsConfig?.period?.allowClear ?? allowClear}
            />
          )}

          <Months
            setValue={setMonths}
            locale={locale}
            className={className}
            humanizeLabels={dropdownsConfig?.months?.humanizeLabels ?? humanizeLabels}
            disabled={!(periodForRender === 'year' && allowedDropdowns.includes('months')) || (dropdownsConfig?.months?.disabled ?? disabled)}
            readOnly={dropdownsConfig?.months?.readOnly ?? readOnly}
            period={periodForRender}
            periodicityOnDoubleClick={dropdownsConfig?.months?.periodicityOnDoubleClick ?? periodicityOnDoubleClick}
            mode={dropdownsConfig?.months?.mode ?? mode}
            allowClear={dropdownsConfig?.months?.allowClear ?? allowClear}
            filterOption={dropdownsConfig?.months?.filterOption}
            value={months}
          />

          <MonthDays
            value={monthDays}
            setValue={setMonthDays}
            locale={locale}
            className={className}
            weekDays={weekDays}
            disabled={
              (!(periodForRender === 'year' || periodForRender === 'month') && allowedDropdowns.includes('month-days')) ||
              (dropdownsConfig?.['month-days']?.disabled ?? disabled)
            }
            readOnly={dropdownsConfig?.['month-days']?.readOnly ?? readOnly}
            leadingZero={dropdownsConfig?.['month-days']?.leadingZero ?? leadingZero}
            period={periodForRender}
            periodicityOnDoubleClick={dropdownsConfig?.['month-days']?.periodicityOnDoubleClick ?? periodicityOnDoubleClick}
            mode={dropdownsConfig?.['month-days']?.mode ?? mode}
            allowClear={dropdownsConfig?.['month-days']?.allowClear ?? allowClear}
            filterOption={dropdownsConfig?.['month-days']?.filterOption}
          />
        </SimpleGrid>
      </Flex>

      <Flex direction='column' gap='sm'>
        <SimpleGrid cols={3}>
          <Hours
            key='hours'
            value={hours}
            setValue={setHours}
            locale={locale}
            className={className}
            disabled={
              !(periodForRender !== 'minute' && periodForRender !== 'hour' && allowedDropdowns.includes('hours')) ||
              (dropdownsConfig?.hours?.disabled ?? disabled)
            }
            readOnly={dropdownsConfig?.hours?.readOnly ?? readOnly}
            leadingZero={dropdownsConfig?.hours?.leadingZero ?? leadingZero}
            clockFormat={clockFormat}
            period={periodForRender}
            periodicityOnDoubleClick={dropdownsConfig?.hours?.periodicityOnDoubleClick ?? periodicityOnDoubleClick}
            mode={dropdownsConfig?.hours?.mode ?? mode}
            allowClear={dropdownsConfig?.hours?.allowClear ?? allowClear}
            filterOption={dropdownsConfig?.hours?.filterOption}
          />
          <Minutes
            key='minutes'
            value={minutes}
            setValue={setMinutes}
            locale={locale}
            period={periodForRender}
            className={className}
            disabled={!(periodForRender !== 'minute' && allowedDropdowns.includes('minutes')) || (dropdownsConfig?.minutes?.disabled ?? disabled)}
            readOnly={dropdownsConfig?.minutes?.readOnly ?? readOnly}
            leadingZero={dropdownsConfig?.minutes?.leadingZero ?? leadingZero}
            clockFormat={clockFormat}
            periodicityOnDoubleClick={dropdownsConfig?.minutes?.periodicityOnDoubleClick ?? periodicityOnDoubleClick}
            mode={dropdownsConfig?.minutes?.mode ?? mode}
            allowClear={dropdownsConfig?.minutes?.allowClear ?? allowClear}
            filterOption={dropdownsConfig?.minutes?.filterOption}
          />
          <WeekDays
            value={weekDays}
            setValue={setWeekDays}
            locale={locale}
            className={className}
            humanizeLabels={dropdownsConfig?.['week-days']?.humanizeLabels ?? humanizeLabels}
            monthDays={monthDays}
            disabled={
              (!(periodForRender === 'year' || periodForRender === 'month' || periodForRender === 'week') &&
                allowedDropdowns.includes('week-days')) ||
              (dropdownsConfig?.['week-days']?.disabled ?? disabled)
            }
            readOnly={dropdownsConfig?.['week-days']?.readOnly ?? readOnly}
            period={periodForRender}
            periodicityOnDoubleClick={dropdownsConfig?.['week-days']?.periodicityOnDoubleClick ?? periodicityOnDoubleClick}
            mode={dropdownsConfig?.['week-days']?.mode ?? mode}
            allowClear={dropdownsConfig?.['week-days']?.allowClear ?? allowClear}
            filterOption={dropdownsConfig?.['week-days']?.filterOption}
          />
        </SimpleGrid>
      </Flex>
    </div>
  );
}
