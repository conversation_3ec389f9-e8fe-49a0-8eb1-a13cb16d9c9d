import { BaseURL } from '@common/constants/BaseUrl';
import { CustomObjectSearchRequest } from './Type';
import { createPageSchema, createResponseSchema } from '@core/schema';
import { CustomObject, CustomObjectSchema } from '@core/schema/CustomObject';
import { createRequest } from './Utils';
import { CustomObjectDependenciesSchema } from '@core/schema/CustomObjectDependencies';

export class CustomObjectApi {
  static findAll(searchRequest: CustomObjectSearchRequest) {
    return createRequest({
      url: BaseURL.customObject,
      method: 'GET',
      params: searchRequest,
      schema: createResponseSchema(createPageSchema(CustomObjectSchema)),
    });
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.customObject}/:id`,
      method: 'GET',
      schema: createResponseSchema(CustomObjectSchema),
      pathVariable: {
        id,
      },
    });
  }
  static save(body: CustomObject) {
    return createRequest({
      url: BaseURL.customObject,
      method: 'POST',
      data: body,
    });
  }

  static deleteById(id: number) {
    return createRequest({
      url: `${BaseURL.customObject}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    });
  }

  static deleteByIdIn(ids: number[]) {
    return createRequest<string>({
      url: `${BaseURL.customObject}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    });
  }
  static findAllDependenciesById(id: number) {
    return createRequest({
      url: `${BaseURL.customObject}/:id/dependencies`,
      method: 'GET',
      schema: createResponseSchema(CustomObjectDependenciesSchema),
      pathVariable: {
        id,
      },
    });
  }
}
