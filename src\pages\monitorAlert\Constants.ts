import { SortType } from '@common/constants/SortType';
import { FilterForm, TimeIntervalRefresh } from './Types';
import { AlertGroupSearchRequest, PaginationRequest } from '@api/Type';

export const SELECTED_ALERT_ROW_COLOR = '#D1D4E0';

export const DEFAULT_ALERT_GROUP_PAGINATION_REQUEST: PaginationRequest = {
  page: 0,
  size: 30,
  sortBy: 'createdDate',
  sortOrder: SortType.DESC,
} as const;

export const DEFAULT_SEARCH_PARAM_VALUE: AlertGroupSearchRequest = {
  alertPriorityConfigIds: [],
  content: '',
  recipient: '',
  applicationIds: [],
  serviceIds: [],
};

export const DEFAULT_FILTER_FORM_VALUE: FilterForm = {
  services: [],
  applications: [],
  recipient: '',
  content: '',
  alertPriorityConfigIds: [],
};

export const TIME_INTERVAL_REFRESHS: TimeIntervalRefresh[] = [
  // TODO: Realtime not implement yet
  // { value: 0, label: 'Realtime' },
  { value: 15, label: '15 seconds' },
  { value: 30, label: '30 seconds' },
  { value: 45, label: '45 seconds' },
  { value: 60, label: '60 seconds' },
];

export const DEFAULT_TIME_INTERVAL_REFRESH = 30;

export const DEFAULT_RECIPIENT_VALUE = 'No Contact';

export const COMMENT_MAX_LENGTH = 300;

export const SEARCH_INPUT_MAX_LENGTH = 300;
