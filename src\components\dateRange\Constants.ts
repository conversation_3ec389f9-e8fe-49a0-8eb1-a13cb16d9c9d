import { EnumKey } from '@common/utils/Type';
import dayjs, { Dayjs } from 'dayjs';

export enum DateRangeTypeEnum {
  TODAY = 'TODAY',
  YESTERDAY = 'YESTERDAY',
  PREVIOUS_WEEK = 'PREVIOUS_WEEK',
  PREVIOUS_MONTH = 'PREVIOUS_MONTH',
  LAST_15_MINUTES = 'LAST_15_MINUTES',
  LAST_60_MINUTES = 'LAST_60_MINUTES',
  LAST_4_HOURS = 'LAST_4_HOURS',
  LAST_24_HOURS = 'LAST_24_HOURS',
  LAST_7_DAYS = 'LAST_7_DAYS',
  LAST_30_DAYS = 'LAST_30_DAYS',
  SINCE_SPECIFIC_DATE = 'SINCE_SPECIFIC_DATE',
  BEFORE_SPECIFIC_DATE = 'BEFORE_SPECIFIC_DATE',
  BETWEEN_DATES = 'BETWEEN_DATES',
}

export enum DateRangeGroupEnum {
  PRESET = 'PRESET',
  DATE_TIME = 'DATE_TIME',
}

export const DateRangeGroupLabel: EnumKey<DateRangeGroupEnum> = {
  [DateRangeGroupEnum.PRESET]: 'Preset',
  [DateRangeGroupEnum.DATE_TIME]: 'Date & Time range',
};

export type DateRangeType = {
  label: string;
  getDefaultDateRange: () => { fromDate: Dayjs | undefined; toDate: Dayjs | undefined };
  type: DateRangeGroupEnum;
};

const getDateRange = (fromDate: Dayjs | undefined, toDate: Dayjs | undefined, unit: dayjs.OpUnitType = 'date') => ({
  fromDate: fromDate?.clone()?.startOf(unit),
  toDate: toDate?.clone().endOf(unit),
});

export const DateRangeType: EnumKey<DateRangeTypeEnum, DateRangeType> = {
  [DateRangeTypeEnum.TODAY]: {
    label: 'Today',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => {
      const today = dayjs();
      return getDateRange(today, today);
    },
  },
  [DateRangeTypeEnum.YESTERDAY]: {
    label: 'Yesterday',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => {
      const today = dayjs().add(-1, 'day');
      return getDateRange(today, today);
    },
  },
  [DateRangeTypeEnum.PREVIOUS_WEEK]: {
    label: 'Previous week',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => {
      const fromDate = dayjs().add(-7, 'day').startOf('week');
      const toDate = dayjs().add(-7, 'day').endOf('week');
      return getDateRange(fromDate, toDate);
    },
  },
  [DateRangeTypeEnum.PREVIOUS_MONTH]: {
    label: 'Previous month',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => {
      const fromDate = dayjs().add(-1, 'month').startOf('month');
      const toDate = dayjs().add(-1, 'month').endOf('month');
      return getDateRange(fromDate, toDate);
    },
  },
  [DateRangeTypeEnum.LAST_15_MINUTES]: {
    label: 'Last 15 minutes',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => getDateRange(dayjs().add(-15, 'minute'), dayjs(), 'minute'),
  },
  [DateRangeTypeEnum.LAST_60_MINUTES]: {
    label: 'Last 60 minutes',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => getDateRange(dayjs().add(-60, 'minute'), dayjs(), 'minute'),
  },
  [DateRangeTypeEnum.LAST_4_HOURS]: {
    label: 'Last 4 hours',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => getDateRange(dayjs().add(-4, 'hour'), dayjs(), 'hour'),
  },
  [DateRangeTypeEnum.LAST_24_HOURS]: {
    label: 'Last 24 hours',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => getDateRange(dayjs().add(-24, 'hour'), dayjs(), 'hour'),
  },
  [DateRangeTypeEnum.LAST_7_DAYS]: {
    label: 'Last 7 days',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => getDateRange(dayjs().add(-7, 'day'), dayjs()),
  },
  [DateRangeTypeEnum.LAST_30_DAYS]: {
    label: 'Last 30 days',
    type: DateRangeGroupEnum.PRESET,
    getDefaultDateRange: () => getDateRange(dayjs().add(-30, 'day'), dayjs()),
  },
  [DateRangeTypeEnum.SINCE_SPECIFIC_DATE]: {
    label: 'Since a specific date',
    type: DateRangeGroupEnum.DATE_TIME,
    getDefaultDateRange: () => ({ fromDate: dayjs().startOf('date'), toDate: dayjs() }),
  },
  [DateRangeTypeEnum.BEFORE_SPECIFIC_DATE]: {
    label: 'Before a specific date',
    type: DateRangeGroupEnum.DATE_TIME,
    getDefaultDateRange: () => ({ fromDate: undefined, toDate: dayjs().endOf('date') }),
  },
  [DateRangeTypeEnum.BETWEEN_DATES]: {
    label: 'Between two dates',
    type: DateRangeGroupEnum.DATE_TIME,
    getDefaultDateRange: () => ({ fromDate: dayjs().startOf('date'), toDate: dayjs().endOf('date') }),
  },
};
