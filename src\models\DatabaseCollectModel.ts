import { COMMON_VALID_NAME_REGEX, DATABASE_COLUMN_VALIDATION_REGEX } from '@common/constants/RegexConstant';
import {
  DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH,
  DATABASE_COLLECT_CONTACT_CUSTOM_MAX_LENGTH,
  DATABASE_COLLECT_SQL_COMMAND_MAX_LENGTH,
  DESCRIPTION_MAX_LENGTH,
  NAME_MAX_LENGTH,
} from '@common/constants/ValidationConstant';
import { WEBHOOK_FIELD_TYPE } from '@common/constants/WebHookConstant';
import { DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR, REGEX_INPUT_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { z, ZodIssueCode } from 'zod';

export const DatabaseCollectModelSchema = z
  .object({
    id: z.number().optional(),
    name: z.string().trim().min(1).max(NAME_MAX_LENGTH),
    description: z.string().trim().max(DESCRIPTION_MAX_LENGTH).regex(COMMON_VALID_NAME_REGEX, { message: REGEX_INPUT_MESSAGE_ERROR }).optional(),
    connectionId: z.number().min(1),
    sqlCommand: z.string().trim().min(1).max(DATABASE_COLLECT_SQL_COMMAND_MAX_LENGTH),
    createdDateField: z
      .string()
      .trim()
      .min(1)
      .max(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)
      .regex(DATABASE_COLUMN_VALIDATION_REGEX, { message: DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR }),
    alertIdField: z
      .string()
      .trim()
      .min(1)
      .max(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)
      .regex(DATABASE_COLUMN_VALIDATION_REGEX, { message: DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR }),
    interval: z.number(),
    serviceNameType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
    serviceId: z.string().optional(),
    serviceMapValue: z
      .string()
      .trim()
      .max(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)
      .regex(DATABASE_COLUMN_VALIDATION_REGEX, { message: DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR })
      .optional(),
    applicationType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
    applicationId: z.string().optional(),
    applicationMapValue: z
      .string()
      .trim()
      .max(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)
      .regex(DATABASE_COLUMN_VALIDATION_REGEX, { message: DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR })
      .optional(),
    alertMapValue: z
      .string()
      .trim()
      .max(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)
      .regex(DATABASE_COLUMN_VALIDATION_REGEX, { message: DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR }),
    priorityType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
    priorityId: z.number().optional(),
    priorityMapValue: z
      .string()
      .trim()
      .max(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)
      .regex(DATABASE_COLUMN_VALIDATION_REGEX, { message: DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR })
      .optional(),
    contactType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
    contactMapValue: z
      .string()
      .trim()
      .max(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)
      .regex(DATABASE_COLUMN_VALIDATION_REGEX, { message: DATABASE_COLUMN_REGEX_INPUT_MESSAGE_ERROR })
      .optional(),
    contactCustomValue: z.string().trim().min(1).max(DATABASE_COLLECT_CONTACT_CUSTOM_MAX_LENGTH).optional(),
    isActive: z.boolean(),
  })
  .superRefine((data, ctx) => {
    if (data.serviceNameType === WEBHOOK_FIELD_TYPE.CUSTOM && !data.serviceId) {
      ctx.addIssue({
        message: 'Service Name is not empty',
        path: ['serviceMapValue'],
        code: ZodIssueCode.custom,
      });
    }
    if (data.serviceNameType === WEBHOOK_FIELD_TYPE.FROM_SOURCE && !data.serviceMapValue) {
      ctx.addIssue({
        message: 'Service Name is not empty',
        path: ['serviceId'],
        code: ZodIssueCode.custom,
      });
    }

    if (data.applicationType === WEBHOOK_FIELD_TYPE.CUSTOM && !data.applicationId) {
      ctx.addIssue({
        message: 'Application Name is not empty',
        path: ['applicationMapValue'],
        code: ZodIssueCode.custom,
      });
    }

    if (data.applicationType === WEBHOOK_FIELD_TYPE.FROM_SOURCE && !data.applicationMapValue) {
      ctx.addIssue({
        message: 'Application Name is not empty',
        path: ['applicationId'],
        code: ZodIssueCode.custom,
      });
    }

    if (data.priorityType === WEBHOOK_FIELD_TYPE.FROM_SOURCE && !data.priorityMapValue) {
      ctx.addIssue({
        message: 'Priority is not empty',
        path: ['priorityMapValue'],
        code: ZodIssueCode.custom,
      });
    }

    if (data.priorityType === WEBHOOK_FIELD_TYPE.CUSTOM && !data.priorityId) {
      ctx.addIssue({
        message: 'Priority is not empty',
        path: ['priorityId'],
        code: ZodIssueCode.custom,
      });
    }

    if (data.contactType === WEBHOOK_FIELD_TYPE.FROM_SOURCE && !data.contactMapValue) {
      ctx.addIssue({
        message: 'Contact is not empty',
        path: ['contactMapValue'],
        code: ZodIssueCode.custom,
      });
    }

    if (data.contactType === WEBHOOK_FIELD_TYPE.CUSTOM && !data.contactCustomValue) {
      ctx.addIssue({
        message: 'Contact is not empty',
        path: ['contactCustomValue'],
        code: ZodIssueCode.custom,
      });
    }
  });
export type DatabaseCollectModel = z.infer<typeof DatabaseCollectModelSchema>;
