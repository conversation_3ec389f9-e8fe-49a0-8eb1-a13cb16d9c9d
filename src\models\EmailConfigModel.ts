import { PaginationRequest } from '@api/Type';
import { EmailProtocolSecurityTypeEnum } from '@common/constants/EmailProtocolSecurityTypeConstant';
import { EmailProtocolTypeEnum } from '@common/constants/EmailProtocolTypeConstant';
import { EMAIL_CONFIG_HOST_REGEX } from '@common/constants/RegexConstant';
import { REGEX_INPUT_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { List } from 'lodash';
import { z } from 'zod';

export const EmailConfigModelSchema = z.object({
  id: z.number(),
  host: z.string().trim().min(1).max(253).regex(EMAIL_CONFIG_HOST_REGEX, { message: REGEX_INPUT_MESSAGE_ERROR }),
  description: z.string().optional(),
  protocolType: z.nativeEnum(EmailProtocolTypeEnum),
  port: z.number().min(0).max(65535),
  password: z.string().trim().min(1).max(50),
  username: z.string().trim().min(1).max(253),
  securityType: z.nativeEnum(EmailProtocolSecurityTypeEnum),
  active: z.boolean(),
});

export type EmailConfigModel = z.infer<typeof EmailConfigModelSchema>;
export type EmailConfigPaginationRequest = PaginationRequest & {
  email?: string;
  protocolTypes?: List<EmailProtocolTypeEnum>;
  withInactived: boolean;
};
