import { ActionIcon, Box, Flex, Grid, Stack, Title } from '@mantine/core';
import { TaskModel } from '@models/TaskModel';
import React, { useCallback } from 'react';
import { FieldArrayWithId, useFieldArray, UseFormReturn } from 'react-hook-form';
import AddTaskButton from './AddTaskButton';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { TaskTimeTypeEnum } from '@common/constants/TaskConstants';
import FieldTitle from '@pages/events/FieldTitle';
import AssignUser from '@pages/events/AssignUser';
import { IconTrash } from '@tabler/icons-react';
interface Props {
  form: UseFormReturn<TaskModel>;
  editable: boolean;
}

const AddTaskSession = ({ editable, form }: Props) => {
  const { control } = form;
  const { fields, remove, replace, update } = useFieldArray({ control, name: 'tasks', keyName: 'fieldId' });
  const onAssignUserSuccess = useCallback(
    (index: number, task: FieldArrayWithId<TaskModel, 'tasks', 'fieldId'>) => {
      update(index, task);
    },
    [update],
  );
  return (
    <Stack>
      {fields.map((task, index) => (
        <Flex
          key={task.fieldId}
          bg='var(--mantine-primary-color-0)'
          p='sm'
          px='md'
          style={{ borderRadius: 'var(--mantine-radius-sm)', border: '1px solid var(--mantine-primary-color-2)' }}>
          <Grid gutter='xs' flex={1}>
            <Grid.Col span={12}>
              <Title order={5} c='var(--mantine-primary-color-5)'>
                Task {index + 1}
              </Title>
            </Grid.Col>
            <Grid.Col span={2}>
              <FieldTitle title='Name' />
            </Grid.Col>
            <Grid.Col span={10}>
              {task.name} {task.deleted ? <b>(Deleted)</b> : undefined}
            </Grid.Col>
            {task.description && (
              <>
                <Grid.Col span={2}>
                  <FieldTitle title='Description' />
                </Grid.Col>
                <Grid.Col span={10}>{task.description}</Grid.Col>
              </>
            )}
            <Grid.Col span={2}>
              <FieldTitle title='Time' />
            </Grid.Col>
            <Grid.Col span={10}>
              <Stack gap='calc(var(--mantine-spacing-xs) / 2)'>
                <Box>
                  <span>Start: </span>
                  {task.startTime?.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}
                </Box>
                {task.timeType === TaskTimeTypeEnum.FROM_TIME_TO_TIME && (
                  <Box>
                    <span>End: </span>
                    {task.endTime?.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}
                  </Box>
                )}
              </Stack>
            </Grid.Col>
            {task.currentAssigneeUserName && (
              <>
                <Grid.Col span={2}>
                  <FieldTitle title='Assignee' />
                </Grid.Col>
                <Grid.Col span={10}>{task.currentAssigneeUserName}</Grid.Col>
              </>
            )}
          </Grid>
          {editable && (
            <Stack align='flex-end'>
              <ActionIcon variant='transparent' onClick={() => remove(index)}>
                <IconTrash size={18} />
              </ActionIcon>
              {!task.deleted && (
                <Box mr='xl'>
                  <AssignUser
                    task={task}
                    onAssignSuccess={(ele) => onAssignUserSuccess(index, { ...task, currentAssigneeUserName: ele.currentAssigneeUserName })}
                  />
                </Box>
              )}
            </Stack>
          )}
        </Flex>
      ))}
      {editable && <AddTaskButton selectedTasks={fields} onSave={replace} />}
    </Stack>
  );
};

export default AddTaskSession;
