import React, { useRef } from 'react';
import { ScrollAreaAutosize, ScrollAreaAutosizeProps } from '@mantine/core';

interface Props extends ScrollAreaAutosizeProps {
  onScrollToBottom: () => void;
  children: React.ReactNode;
  offsetBottom?: number;
}

const InfiniteScroll = ({ children, offsetBottom = 0, onScrollToBottom, ...rest }: Props) => {
  const viewport = useRef<HTMLDivElement>(null);
  const onScrollPositionChange = () => {
    if (viewport?.current) {
      const { clientHeight, scrollHeight, scrollTop } = viewport.current;
      const isScrollToBottom = Math.abs(scrollHeight - (scrollTop + clientHeight + offsetBottom)) <= 1;
      if (isScrollToBottom) {
        onScrollToBottom();
      }
    }
  };
  return (
    <ScrollAreaAutosize viewportRef={viewport} onScrollPositionChange={onScrollPositionChange} {...rest}>
      {children}
    </ScrollAreaAutosize>
  );
};

export default InfiniteScroll;
