import React from 'react';
import BaseEditor from './BaseEditor';
import { BaseEditorProps } from './Type';
import { autocompletion, CompletionContext, CompletionResult } from '@codemirror/autocomplete';

const TeamsEditor = (props: BaseEditorProps) => {
  const alertCompletion = (context: CompletionContext): CompletionResult | null => {
    const line = context.state.doc.lineAt(context.pos);

    const lineText = line.text.slice(0, context.pos - line.from);
    const alertVariableDetails = new Map<string, string>();
    alertVariableDetails.set('AlertContent', 'It will append the content of the alert when sending');
    const atAtMatch = /@@([^@]*)$/.exec(lineText);

    if (atAtMatch) {
      const prefix = atAtMatch[1] || '';

      const baseVariableNames = Array.from(alertVariableDetails.keys());
      const filteredBaseVars = prefix
        ? baseVariableNames.filter((varName) => varName.toLowerCase().includes(prefix.toLowerCase()))
        : baseVariableNames;

      if (filteredBaseVars.length === 0) {
        return null;
      }

      return {
        from: context.pos - prefix.length - 2,
        options: filteredBaseVars.map((baseVarName) => {
          const detailText = alertVariableDetails.get(baseVarName) || `Field: ${baseVarName}`;
          return {
            label: `{{${baseVarName}}}`,
            detail: detailText,
            apply: `{{${baseVarName}}}`,
            type: 'variable',
          };
        }),
        filter: false,
      };
    }

    return null;
  };

  return (
    <BaseEditor
      {...props}
      extensions={[
        autocompletion({
          override: [alertCompletion],
        }),
      ]}
    />
  );
};

export default TeamsEditor;
