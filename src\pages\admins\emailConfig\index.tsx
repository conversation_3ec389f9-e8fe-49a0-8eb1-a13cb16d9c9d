import React, { useState, useMemo, useRef, useCallback } from 'react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { Box, Flex, Switch } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { IconEye, IconPlus } from '@tabler/icons-react';
import { EmailConfigApi } from '@api/EmailConfigApi';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { IconEdit } from '@tabler/icons-react';
import { SortType } from '@common/constants/SortType';
import { EmailConfig } from '@core/schema/EmailConfig';
import CreateOrUpdateModal from './CreateOrUpdateModal';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import DeleteModal from './DeleteModal';
import { IconTrash } from '@tabler/icons-react';
import { EmailConfigPaginationRequest } from '@models/EmailConfigModel';
import ConfirmActiveModal from './ConfimActiveModal';
const columns: ColumnType<EmailConfig>[] = [
  {
    title: 'Host',
    name: 'host',
  },
  {
    title: 'Description',
    name: 'description',
  },
  {
    title: 'Port',
    name: 'port',
    width: '5%',
  },
  {
    title: 'Email',
    name: 'email',
  },
  {
    title: 'Protocol type',
    name: 'protocolType',
    width: '5%',
  },
];

export const EmailConfigPage = () => {
  const [tableAffected, setTableAffected] = useState<EmailConfigPaginationRequest>({ ...DEFAULT_PAGINATION_REQUEST, withInactived: true });
  const [openedModalCreateEmailConfig, { close: closeModalCreateEmailConfig, open: openModalCreateEmailConfig }] = useDisclosure(false);
  const [rowSelected, setRowSelected] = useState<EmailConfig | undefined>(undefined);
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);
  const [openedModalActive, { close: closePopupActive, open: openModalActive }] = useDisclosure(false);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const [isViewMode, setIsViewMode] = useState<boolean>(false);

  // API
  const { data: listEmailConfig, refetch: refetchList } = useFetch(EmailConfigApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  //Function update table affected
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<EmailConfig>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const tableViewListRolesProps: KanbanTableProps<EmailConfig> = useMemo(() => {
    return {
      columns: columns,
      data: listEmailConfig?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listEmailConfig?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.emailConnectionEdit]}>
                <KanbanTooltip label={data.active ? 'Inactive' : 'Active'}>
                  <Switch
                    checked={data.active}
                    onClick={() => {
                      setIsViewMode(false);
                      setRowSelected(data);
                      openModalActive();
                    }}
                  />
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailConnectionView]}>
                <KanbanTooltip label='View'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setIsViewMode(true);
                      setRowSelected(data);
                      openModalCreateEmailConfig();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailConnectionEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setIsViewMode(false);
                      setRowSelected(data);
                      openModalCreateEmailConfig();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailConnectionDelete]}>
                <KanbanTooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setIsViewMode(false);
                      openModalDel();
                      setRowSelected(data);
                    }}>
                    <IconTrash color='red' />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    handleUpdateTablePagination,
    listEmailConfig?.data?.content,
    listEmailConfig?.data?.totalElements,
    openModalActive,
    openModalCreateEmailConfig,
    openModalDel,
    tableAffected,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of email connection'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.emailConnectionCreate]}>
            <Flex direction='row' gap='xs' align='center'>
              <KanbanButton
                size='xs'
                onClick={() => {
                  setIsViewMode(false);
                  setRowSelected(undefined);
                  openModalCreateEmailConfig();
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
      <CreateOrUpdateModal
        isViewMode={isViewMode}
        opened={openedModalCreateEmailConfig}
        onClose={closeModalCreateEmailConfig}
        emailConfig={rowSelected}
        refetchList={refetchList}
      />
      <DeleteModal
        opened={openedModalDel}
        onClose={closePopupDel}
        emailConfig={`${String(rowSelected?.protocolType)} ${String(rowSelected?.email)}`}
        emailConfigId={rowSelected?.id}
        refetchList={refetchList}
      />
      <ConfirmActiveModal opened={openedModalActive} onClose={closePopupActive} emailConfig={rowSelected} refetchList={refetchList} />
    </Box>
  );
};
export default EmailConfigPage;
