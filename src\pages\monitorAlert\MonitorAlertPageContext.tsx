import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { createContext } from 'react';
import { useIdle, useLocalStorage } from '@mantine/hooks';
import { FilterForm, NavbarTabTypeEnum, TreeForm } from './Types';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { DEFAULT_ALERT_GROUP_PAGINATION_REQUEST, DEFAULT_FILTER_FORM_VALUE, DEFAULT_SEARCH_PARAM_VALUE } from './Constants';
import { omit } from 'lodash';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { AlertGroupSearchRequest } from '@api/Type';
import { AlertGroup } from '@core/schema/AlertGroup';
import { useSortEffect } from '@components/dragTable/useSortEffect';
import { SortEffect } from '@components/dragTable/Types';
import { refetchRequest } from '@common/utils/QueryUtils';
import { AlertGroupApi } from '@api/AlertGroupApi';
import { ServiceApi } from '@api/ServiceApi';
import { AlertGroupStatusEnum } from '@common/constants/AlertGroupStatusConstant';
import { ApplicationApi } from '@api/ApplicationApi';

export interface MonitorAlertPageState {
  tree: TreeForm;
  filter: FilterForm;
  tab: NavbarTabTypeEnum | undefined;
  searchParams: AlertGroupSearchRequest;
  selectedAlertGroups: AlertGroup[];
  setTree: (val: TreeForm) => void;
  onFilterSubmit: (filter: FilterForm) => void;
  onFilterReset: () => void;
  setTab: (val: NavbarTabTypeEnum | undefined) => void;
  setSelectedAlertGroups: React.Dispatch<React.SetStateAction<AlertGroup[]>>;
  sortEffect: SortEffect;
  reloadData: () => void;
}

export const DEFAULT_TREE_VALUE: TreeForm = { applicationId: '', serviceId: '' };

// 90s
const DESELECT_ALERTS_TIME = 90 * 1000;

export const MonitorAlertPageContext = createContext<MonitorAlertPageState>({
  tree: DEFAULT_TREE_VALUE,
  filter: DEFAULT_FILTER_FORM_VALUE,
  tab: undefined,
  searchParams: DEFAULT_SEARCH_PARAM_VALUE,
  setTree: () => {},
  onFilterSubmit: () => {},
  onFilterReset: () => {},
  setTab: () => {},
  selectedAlertGroups: [],
  setSelectedAlertGroups: () => {},
  sortEffect: {
    sortBy: '',
    direction: DEFAULT_ALERT_GROUP_PAGINATION_REQUEST.sortOrder,
    onChange: () => {},
  },
  reloadData: () => {},
});

interface Props {
  children: React.ReactNode;
}

export const MonitorAlertPageProvider = ({ children }: Props) => {
  const [tree, setTree] = useLocalStorage<TreeForm>({
    key: LocalStorageKey.MONITOR_ALERT_NAVBAR_TREE,
    defaultValue: DEFAULT_TREE_VALUE,
    getInitialValueInEffect: false,
  });
  const [searchParams, setSearchParams] = useLocalStorage<AlertGroupSearchRequest>({
    key: LocalStorageKey.MONITOR_ALERT_NAVBAR_SEARCH_PARAM,
    defaultValue: DEFAULT_SEARCH_PARAM_VALUE,
    getInitialValueInEffect: false,
  });
  const [filter, setFilter] = useLocalStorage<FilterForm>({
    key: LocalStorageKey.MONITOR_ALERT_NAVBAR_FILTER,
    defaultValue: DEFAULT_FILTER_FORM_VALUE,
    getInitialValueInEffect: false,
  });
  const [tab, setTab] = useLocalStorage<NavbarTabTypeEnum | undefined>({
    key: LocalStorageKey.MONITOR_ALERT_NAVBAR_TAB,
    defaultValue: undefined,
    getInitialValueInEffect: false,
  });
  const [selectedAlertGroups, setSelectedAlertGroups] = useState<AlertGroup[]>([]);
  const idle = useIdle(DESELECT_ALERTS_TIME);
  useEffect(() => {
    if (idle) {
      setSelectedAlertGroups([]);
    }
  }, [idle]);
  const onTreeChange = useCallback(
    (treeFilter: TreeForm) => {
      setTree(treeFilter);
      setSearchParams({
        ...DEFAULT_SEARCH_PARAM_VALUE,
        serviceIds: treeFilter.serviceId ? [treeFilter.serviceId] : [],
        applicationIds: treeFilter.applicationId ? [treeFilter.applicationId] : [],
      });
      setFilter(DEFAULT_FILTER_FORM_VALUE);
    },
    [setFilter, setSearchParams, setTree],
  );
  const onFilterSubmit = useCallback(
    (filterForm: FilterForm) => {
      setTree(DEFAULT_TREE_VALUE);
      setFilter(filterForm);
      setSearchParams((state) => ({
        ...state,
        ...omit(filterForm, ['services', 'applications']),
        serviceIds: filterForm.services.map((ele) => ele.id) || [],
        applicationIds: filterForm.applications.map((ele) => ele.id) || [],
        page: DEFAULT_PAGINATION_REQUEST.page,
      }));
    },
    [setFilter, setSearchParams, setTree],
  );
  const onFilterReset = useCallback(() => {
    setTree(DEFAULT_TREE_VALUE);
    setFilter(DEFAULT_FILTER_FORM_VALUE);
    setSearchParams(DEFAULT_SEARCH_PARAM_VALUE);
  }, [setFilter, setSearchParams, setTree]);
  const sortEffect = useSortEffect({
    defaultDirection: DEFAULT_ALERT_GROUP_PAGINATION_REQUEST.sortOrder,
    defaultSortBy: DEFAULT_ALERT_GROUP_PAGINATION_REQUEST.sortBy,
  });
  const reloadData = useCallback(() => {
    refetchRequest(
      AlertGroupApi.findAllByPostMethod(searchParams, {
        ...DEFAULT_ALERT_GROUP_PAGINATION_REQUEST,
        sortBy: sortEffect.sortBy,
        sortOrder: sortEffect.direction,
      }),
    );
    if (tab === NavbarTabTypeEnum.TREE) {
      refetchRequest(ServiceApi.findServiceWithPriorityByAlertGroupStatus(AlertGroupStatusEnum.NEW));
      if (tree.serviceId) {
        refetchRequest(ApplicationApi.findApplicationWithPriorityByAlertGroupStatusAndServiceId(AlertGroupStatusEnum.NEW, tree.serviceId));
      }
    }
  }, [searchParams, sortEffect.direction, sortEffect.sortBy, tab, tree.serviceId]);
  const state = useMemo<MonitorAlertPageState>(
    () => ({
      tree,
      filter,
      tab,
      setTree: onTreeChange,
      onFilterSubmit,
      onFilterReset,
      setTab,
      searchParams,
      selectedAlertGroups,
      setSelectedAlertGroups,
      sortEffect,
      reloadData,
    }),
    [filter, onFilterReset, onFilterSubmit, onTreeChange, reloadData, searchParams, selectedAlertGroups, setTab, sortEffect, tab, tree],
  );
  return <MonitorAlertPageContext.Provider value={state}>{children}</MonitorAlertPageContext.Provider>;
};
