import React, { useContext, useMemo } from 'react';
import useFetch from '@core/hooks/useFetch';
import { KanbanAccordion, KanbanButton } from 'kanban-design-system';
import { Dictionary, keyBy, sortBy } from 'lodash';
import { Box, Flex, Loader, Text } from '@mantine/core';
import { findHighestAlertPriority } from '@common/utils/CommonUtils';
import classes from './AlertNavbarFilter.module.css';
import { ServiceApi } from '@api/ServiceApi';
import { ApplicationApi } from '@api/ApplicationApi';
import { AlertGroupStatusEnum } from '@common/constants/AlertGroupStatusConstant';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import { DEFAULT_TREE_VALUE, MonitorAlertPageContext } from '../MonitorAlertPageContext';
import { AlertPriorityConfig } from '@core/schema';

const ApplicationList = ({ priorityConfigMap, serviceId }: { serviceId: string; priorityConfigMap: Dictionary<AlertPriorityConfig> }) => {
  const { setTree, tree } = useContext(MonitorAlertPageContext);
  const { data: applications, isFetching } = useFetch(
    ApplicationApi.findApplicationWithPriorityByAlertGroupStatusAndServiceId(AlertGroupStatusEnum.NEW, serviceId),
    {
      showLoading: false,
      enabled: serviceId === tree.serviceId,
    },
  );
  return (
    <Box style={{ backgroundColor: 'var(--mantine-color-gray-1)' }}>
      {(applications?.data?.length || 0) === 0 && isFetching ? (
        <Flex align='center' justify='center'>
          <Loader color='primary' type='dots' />
        </Flex>
      ) : (
        sortBy(applications?.data || [], 'name')?.map((application) => {
          const priorityConfigs = application.alertPriorityConfigIds?.map((configId) => priorityConfigMap[configId])?.filter((ele) => ele);
          const highestPriority = findHighestAlertPriority(priorityConfigs);
          return (
            <Flex
              key={application.id}
              justify='flex-start'
              align='center'
              gap={10}
              pl={25}
              py={10}
              classNames={{ root: classes.accordionContent }}
              bg={application.id === tree?.applicationId ? 'primary.1' : undefined}
              onClick={() => setTree({ serviceId: application.serviceId, applicationId: application.id })}>
              <Text bg={highestPriority?.color} className={classes.alertAmount}>
                {application.alertAmount}
              </Text>
              <Text classNames={{ root: classes.treeTitle }}>{application.name}</Text>
            </Flex>
          );
        })
      )}
      {}
    </Box>
  );
};

const TreeServiceFilter = () => {
  const { setTree, tree } = useContext(MonitorAlertPageContext);
  const { data: services } = useFetch(ServiceApi.findServiceWithPriorityByAlertGroupStatus(AlertGroupStatusEnum.NEW), { showLoading: false });
  const { data: priorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: true }), {
    showLoading: false,
  });
  const priorityConfigMap = useMemo(() => keyBy(priorityConfigs?.data, 'id'), [priorityConfigs?.data]);
  const treeData = useMemo(
    () =>
      services?.data
        ?.sort((a, b) => a.name.toLocaleLowerCase().localeCompare(b.name.toLocaleLowerCase()))
        ?.map((service) => {
          const isSelected = tree?.serviceId === service.id;
          const priorityConfigs = service.alertPriorityConfigIds?.map((configId) => priorityConfigMap[configId])?.filter((ele) => ele);
          const highestPriority = findHighestAlertPriority(priorityConfigs);
          return {
            key: service.id,
            title: (
              <Flex justify='flex-start' align='center' gap={10}>
                <Text bg={highestPriority?.color} className={classes.alertAmount}>
                  {service.alertAmount}
                </Text>
                <Text classNames={{ root: classes.treeTitle }}>{service.name}</Text>
              </Flex>
            ),
            styles: {
              control: {
                backgroundColor: isSelected ? 'var(--mantine-primary-color-2)' : 'transparent',
              },
            },
            content: <ApplicationList priorityConfigMap={priorityConfigMap} serviceId={service.id} />,
          };
        }) || [],
    [priorityConfigMap, services?.data, tree?.serviceId],
  );

  return (
    <Box>
      <KanbanButton fullWidth onClick={() => setTree(DEFAULT_TREE_VALUE)} variant='outline' mb={5}>
        All alert
      </KanbanButton>
      <KanbanAccordion
        classNames={{ item: classes.accordionItem }}
        styles={{ item: { borderRight: 0 }, content: { padding: 0 }, label: { paddingTop: 5, paddingBottom: 5 } }}
        value={tree?.serviceId}
        onChange={(serviceId) => setTree({ serviceId: serviceId ? (serviceId as string) : '', applicationId: '' })}
        variant='filled'
        data={treeData}
      />
    </Box>
  );
};

export default TreeServiceFilter;
