import { DEFAULT_ALERT_PRIORITY_CONFIG_ID } from '@common/constants/AlertPriorityConfigConstants';
import { AlertPriorityConfig } from '@core/schema';
import { isEmpty } from 'lodash';

export function findHighestAlertPriority(priorities: AlertPriorityConfig[]) {
  if (isEmpty(priorities)) {
    return null;
  }

  return priorities
    .filter((ele) => ele.deleted === false)
    .reduce((maxConfig, currentConfig) => {
      if (currentConfig.id === DEFAULT_ALERT_PRIORITY_CONFIG_ID) {
        return maxConfig;
      }
      if (maxConfig.id === DEFAULT_ALERT_PRIORITY_CONFIG_ID) {
        return currentConfig;
      }
      return maxConfig.position > currentConfig.position ? currentConfig : maxConfig;
    }, priorities[0]);
}
