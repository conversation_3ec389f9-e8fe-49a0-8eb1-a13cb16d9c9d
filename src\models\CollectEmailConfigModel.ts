import { CollectEmailContentTypeEnum } from '@common/constants/CollectEmailConfigConstant';
import { z } from 'zod';
import { MAX_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_RECIPIENT_LENGTH } from '@common/constants/ValidationConstant';
import { QueryRuleGroupTypeModelSchema } from './RuleGroupTypeModel';
import { CollectEmailConfigTypeEnum } from '@common/constants/CollectEmailConfigTypeConstant';
export const ABSENCE_INTERVAL_MIN = 300;
export const ABSENCE_INTERVAL_MAX = 86400;
export const CollectEmailConfigModelSchema = z
  .object({
    id: z.number(),
    name: z.string().trim().min(1).max(MAX_NAME_LENGTH),
    description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
    emailConfigId: z.number().min(1),
    intervalTime: z.number(),
    type: z.nativeEnum(CollectEmailConfigTypeEnum),
    absenceInterval: z.number().optional(),
    alertRepeatInterval: z.number().optional(),
    ruleGroup: QueryRuleGroupTypeModelSchema,
    serviceId: z.string().min(1),
    applicationId: z.string().min(1),
    priorityConfigId: z.string().min(1),
    contentType: z.nativeEnum(CollectEmailContentTypeEnum).optional(),
    contentValue: z.string().optional(),
    content: z.string().optional(),
    recipient: z.string().trim().min(1).max(MAX_RECIPIENT_LENGTH),
    active: z.boolean(),
  })
  .superRefine((data, ctx) => {
    if (CollectEmailConfigTypeEnum.ABSENCE_ALERT === data.type) {
      if (!data.absenceInterval || data.absenceInterval < ABSENCE_INTERVAL_MIN) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Check is last must greater than or equal ${ABSENCE_INTERVAL_MIN}`,
          path: ['absenceInterval'],
        });
      }
      if (!data.alertRepeatInterval || data.alertRepeatInterval < ABSENCE_INTERVAL_MIN) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `The last alert more than must greater than or equal ${ABSENCE_INTERVAL_MIN}`,
          path: ['alertRepeatInterval'],
        });
      }
      if (!data.contentValue) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Alert content is not empty`,
          path: ['contentValue'],
        });
      }
    }
  });

export type CollectEmailConfigModel = z.infer<typeof CollectEmailConfigModelSchema>;
