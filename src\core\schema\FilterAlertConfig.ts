import { z } from 'zod';
import { QueryRuleGroupTypeSchema } from './RuleGroupCondition';
import { ServiceSchema } from './Service';
import { ApplicationSchema } from './Application';

export const BaseFilterAlertConfigSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  active: z.boolean(),
  ruleGroup: QueryRuleGroupTypeSchema,
});

export type BaseFilterAlertConfig = z.infer<typeof BaseFilterAlertConfigSchema>;
export const FilterAlertConfigSchema = BaseFilterAlertConfigSchema.extend({
  services: z.array(ServiceSchema).optional(),
  applications: z.array(ApplicationSchema).optional(),
});

export type FilterAlertConfig = z.infer<typeof FilterAlertConfigSchema>;
