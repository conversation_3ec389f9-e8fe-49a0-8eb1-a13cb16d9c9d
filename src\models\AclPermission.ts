import { PermissionActionEnum } from '@common/constants/PermissionAction';
import { PermissionModuleEnum } from '@common/constants/PermissionModule';
import { PermissionTypeEnum } from '@common/constants/PermissionType';

export class AclPermission {
  static createAcl = (
    module: PermissionModuleEnum,
    action: PermissionActionEnum,
    type?: PermissionTypeEnum,
    moduleId?: string,
    moduleParentId?: string,
  ) => {
    const acl = new AclPermission(module, action);
    acl.type = type;
    acl.moduleId = moduleId;
    acl.moduleParentId = moduleParentId;
    return acl;
  };

  static superAdmin = new AclPermission(PermissionModuleEnum.SUPPER_ADMIN, PermissionActionEnum.UNKNOWN);

  static monitorAlertView = new AclPermission(PermissionModuleEnum.MONITOR_ALERT, PermissionActionEnum.VIEW);
  static monitorAlertAcknowledge = new AclPermission(PermissionModuleEnum.MONITOR_ALERT, PermissionActionEnum.ACKNOWLEDGE);
  static monitorAlertComment = new AclPermission(PermissionModuleEnum.MONITOR_ALERT, PermissionActionEnum.COMMENT);

  static reportView = new AclPermission(PermissionModuleEnum.REPORT, PermissionActionEnum.VIEW);
  static reportExport = new AclPermission(PermissionModuleEnum.REPORT, PermissionActionEnum.EXPORT);

  static webHookView = new AclPermission(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.VIEW);
  static webHookCreate = new AclPermission(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.CREATE);
  static webHookEdit = new AclPermission(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.EDIT);
  static webHookDelete = new AclPermission(PermissionModuleEnum.WEBHOOK_CONFIG, PermissionActionEnum.DELETE);

  static userManageView = new AclPermission(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.VIEW);
  static userManageCreate = new AclPermission(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.CREATE);
  static userManageEdit = new AclPermission(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.EDIT);
  static userManageDelete = new AclPermission(PermissionModuleEnum.USER_MANAGEMENT, PermissionActionEnum.DELETE);

  static roleManageView = new AclPermission(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.VIEW);
  static roleManageCreate = new AclPermission(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.CREATE);
  static roleManageEdit = new AclPermission(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.EDIT);
  static roleManageDelete = new AclPermission(PermissionModuleEnum.ROLE_MANAGEMENT, PermissionActionEnum.DELETE);

  static serviceManageView = new AclPermission(PermissionModuleEnum.SERVICE_MANAGEMENT, PermissionActionEnum.VIEW);
  static serviceManageCreate = new AclPermission(PermissionModuleEnum.SERVICE_MANAGEMENT, PermissionActionEnum.CREATE);
  static serviceManageEdit = new AclPermission(PermissionModuleEnum.SERVICE_MANAGEMENT, PermissionActionEnum.EDIT);
  static serviceManageDelete = new AclPermission(PermissionModuleEnum.SERVICE_MANAGEMENT, PermissionActionEnum.DELETE);
  static serviceManageExport = new AclPermission(PermissionModuleEnum.SERVICE_MANAGEMENT, PermissionActionEnum.EXPORT);

  static applicationManageView = new AclPermission(PermissionModuleEnum.APPLICATION_MANAGEMENT, PermissionActionEnum.VIEW);
  static applicationManageCreate = new AclPermission(PermissionModuleEnum.APPLICATION_MANAGEMENT, PermissionActionEnum.CREATE);
  static applicationManageEdit = new AclPermission(PermissionModuleEnum.APPLICATION_MANAGEMENT, PermissionActionEnum.EDIT);
  static applicationManageDelete = new AclPermission(PermissionModuleEnum.APPLICATION_MANAGEMENT, PermissionActionEnum.DELETE);
  static applicationManageExport = new AclPermission(PermissionModuleEnum.APPLICATION_MANAGEMENT, PermissionActionEnum.EXPORT);

  static priorityConfigView = new AclPermission(PermissionModuleEnum.PRIORITY_CONFIG, PermissionActionEnum.VIEW);
  static priorityConfigCreate = new AclPermission(PermissionModuleEnum.PRIORITY_CONFIG, PermissionActionEnum.CREATE);
  static priorityConfigEdit = new AclPermission(PermissionModuleEnum.PRIORITY_CONFIG, PermissionActionEnum.EDIT);
  static priorityConfigDelete = new AclPermission(PermissionModuleEnum.PRIORITY_CONFIG, PermissionActionEnum.DELETE);

  static customObjectView = new AclPermission(PermissionModuleEnum.CUSTOM_OBJECT, PermissionActionEnum.VIEW);
  static customObjectCreate = new AclPermission(PermissionModuleEnum.CUSTOM_OBJECT, PermissionActionEnum.CREATE);
  static customObjectEdit = new AclPermission(PermissionModuleEnum.CUSTOM_OBJECT, PermissionActionEnum.EDIT);
  static customObjectDelete = new AclPermission(PermissionModuleEnum.CUSTOM_OBJECT, PermissionActionEnum.DELETE);

  static emailTemplateView = new AclPermission(PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, PermissionActionEnum.VIEW);
  static emailTemplateCreate = new AclPermission(PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, PermissionActionEnum.CREATE);
  static emailTemplateEdit = new AclPermission(PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, PermissionActionEnum.EDIT);
  static emailTemplateDelete = new AclPermission(PermissionModuleEnum.EMAIL_TEMPLATE_CONFIG, PermissionActionEnum.DELETE);

  static emailPartnerView = new AclPermission(PermissionModuleEnum.EMAIL_PARTNER_CONFIG, PermissionActionEnum.VIEW);
  static emailPartnerCreate = new AclPermission(PermissionModuleEnum.EMAIL_PARTNER_CONFIG, PermissionActionEnum.CREATE);
  static emailPartnerEdit = new AclPermission(PermissionModuleEnum.EMAIL_PARTNER_CONFIG, PermissionActionEnum.EDIT);
  static emailPartnerDelete = new AclPermission(PermissionModuleEnum.EMAIL_PARTNER_CONFIG, PermissionActionEnum.DELETE);

  static emailConnectionView = new AclPermission(PermissionModuleEnum.EMAIL_CONNECTION, PermissionActionEnum.VIEW);
  static emailConnectionCreate = new AclPermission(PermissionModuleEnum.EMAIL_CONNECTION, PermissionActionEnum.CREATE);
  static emailConnectionEdit = new AclPermission(PermissionModuleEnum.EMAIL_CONNECTION, PermissionActionEnum.EDIT);
  static emailConnectionDelete = new AclPermission(PermissionModuleEnum.EMAIL_CONNECTION, PermissionActionEnum.DELETE);

  static emailCollectView = new AclPermission(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.VIEW);
  static emailCollectCreate = new AclPermission(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.CREATE);
  static emailCollectEdit = new AclPermission(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.EDIT);
  static emailCollectDelete = new AclPermission(PermissionModuleEnum.EMAIL_COLLECT, PermissionActionEnum.DELETE);

  static databaseConnectionView = new AclPermission(PermissionModuleEnum.DATABASE_CONNECTION, PermissionActionEnum.VIEW);
  static databaseConnectionCreate = new AclPermission(PermissionModuleEnum.DATABASE_CONNECTION, PermissionActionEnum.CREATE);
  static databaseConnectionEdit = new AclPermission(PermissionModuleEnum.DATABASE_CONNECTION, PermissionActionEnum.EDIT);
  static databaseConnectionDelete = new AclPermission(PermissionModuleEnum.DATABASE_CONNECTION, PermissionActionEnum.DELETE);

  static databaseCollectView = new AclPermission(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW);
  static databaseCollectCreate = new AclPermission(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.CREATE);
  static databaseCollectEdit = new AclPermission(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.EDIT);
  static databaseCollectDelete = new AclPermission(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.DELETE);

  static dashboardView = new AclPermission(PermissionModuleEnum.DASHBOARD, PermissionActionEnum.VIEW);
  static sendEmail = new AclPermission(PermissionModuleEnum.SEND_EMAIL, PermissionActionEnum.SEND_EMAIL);

  static alertGroupConfigView = new AclPermission(PermissionModuleEnum.ALERT_GROUP_CONFIG, PermissionActionEnum.VIEW);
  static alertGroupConfigCreate = new AclPermission(PermissionModuleEnum.ALERT_GROUP_CONFIG, PermissionActionEnum.CREATE);
  static alertGroupConfigEdit = new AclPermission(PermissionModuleEnum.ALERT_GROUP_CONFIG, PermissionActionEnum.EDIT);
  static alertGroupConfigDelete = new AclPermission(PermissionModuleEnum.ALERT_GROUP_CONFIG, PermissionActionEnum.DELETE);

  static maintenanceTimeConfigView = new AclPermission(PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, PermissionActionEnum.VIEW);
  static maintenanceTimeConfigCreate = new AclPermission(PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, PermissionActionEnum.CREATE);
  static maintenanceTimeConfigEdit = new AclPermission(PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, PermissionActionEnum.EDIT);
  static maintenanceTimeConfigDelete = new AclPermission(PermissionModuleEnum.MAINTENANCE_TIME_CONFIG, PermissionActionEnum.DELETE);

  static taskView = new AclPermission(PermissionModuleEnum.TASK, PermissionActionEnum.VIEW);
  static taskAssign = new AclPermission(PermissionModuleEnum.TASK, PermissionActionEnum.ASSIGN);
  static taskDelete = new AclPermission(PermissionModuleEnum.TASK, PermissionActionEnum.DELETE);
  static taskCreate = new AclPermission(PermissionModuleEnum.TASK, PermissionActionEnum.CREATE);
  static taskEdit = new AclPermission(PermissionModuleEnum.TASK, PermissionActionEnum.EDIT);

  static filterAlertConfigView = new AclPermission(PermissionModuleEnum.FILTER_ALERT_CONFIG, PermissionActionEnum.VIEW);
  static filterAlertConfigCreate = new AclPermission(PermissionModuleEnum.FILTER_ALERT_CONFIG, PermissionActionEnum.CREATE);
  static filterAlertConfigEdit = new AclPermission(PermissionModuleEnum.FILTER_ALERT_CONFIG, PermissionActionEnum.EDIT);
  static filterAlertConfigDelete = new AclPermission(PermissionModuleEnum.FILTER_ALERT_CONFIG, PermissionActionEnum.DELETE);

  static telegramAlertConfig = new AclPermission(PermissionModuleEnum.TELEGRAM_ALERT_CONFIG, PermissionActionEnum.CONFIG);
  static modifyAlertConfigView = new AclPermission(PermissionModuleEnum.MODIFY_ALERT_CONFIG, PermissionActionEnum.VIEW);
  static modifyAlertConfigCreate = new AclPermission(PermissionModuleEnum.MODIFY_ALERT_CONFIG, PermissionActionEnum.CREATE);
  static modifyAlertConfigEdit = new AclPermission(PermissionModuleEnum.MODIFY_ALERT_CONFIG, PermissionActionEnum.EDIT);
  static modifyAlertConfigDelete = new AclPermission(PermissionModuleEnum.MODIFY_ALERT_CONFIG, PermissionActionEnum.DELETE);

  static syslogView = new AclPermission(PermissionModuleEnum.SYSLOG, PermissionActionEnum.VIEW);
  static syslogExport = new AclPermission(PermissionModuleEnum.SYSLOG, PermissionActionEnum.EXPORT);

  static databaseThresholdConfigView = new AclPermission(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.VIEW);
  static databaseThresholdConfigCreate = new AclPermission(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.CREATE);
  static databaseThresholdConfigEdit = new AclPermission(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.EDIT);
  static databaseThresholdConfigDelete = new AclPermission(PermissionModuleEnum.DATABASE_THRESHOLD_CONFIG, PermissionActionEnum.DELETE);

  static teamsAlertConfig = new AclPermission(PermissionModuleEnum.TEAMS_ALERT_CONFIG, PermissionActionEnum.CONFIG);
  static teamsAlertSend = new AclPermission(PermissionModuleEnum.TEAMS_ALERT_CONFIG, PermissionActionEnum.SEND);

  static executionView = new AclPermission(PermissionModuleEnum.EXECUTION, PermissionActionEnum.VIEW);
  static executionCreate = new AclPermission(PermissionModuleEnum.EXECUTION, PermissionActionEnum.CREATE);
  static executionEdit = new AclPermission(PermissionModuleEnum.EXECUTION, PermissionActionEnum.EDIT);
  static executionDelete = new AclPermission(PermissionModuleEnum.EXECUTION, PermissionActionEnum.DELETE);

  static executionGroupView = new AclPermission(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.VIEW);
  static executionGroupCreate = new AclPermission(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.CREATE);
  static executionGroupEdit = new AclPermission(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.EDIT);
  static executionGroupDelete = new AclPermission(PermissionModuleEnum.EXECUTION_GROUP, PermissionActionEnum.DELETE);

  static variableView = new AclPermission(PermissionModuleEnum.VARIABLE, PermissionActionEnum.VIEW);
  static variableCreate = new AclPermission(PermissionModuleEnum.VARIABLE, PermissionActionEnum.CREATE);
  static variableEdit = new AclPermission(PermissionModuleEnum.VARIABLE, PermissionActionEnum.EDIT);
  static variableDelete = new AclPermission(PermissionModuleEnum.VARIABLE, PermissionActionEnum.DELETE);

  static executionHistoryView = new AclPermission(PermissionModuleEnum.EXECUTION_HISTORY, PermissionActionEnum.VIEW);

  // check any permission submodule sql
  static anyExecution = this.createAcl(PermissionModuleEnum.RUN_EXECUTION, PermissionActionEnum.EXECUTE, PermissionTypeEnum.SUB_MODULE);

  static adminView = [
    AclPermission.webHookView,
    AclPermission.databaseCollectView,
    AclPermission.emailCollectView,
    AclPermission.serviceManageView,
    AclPermission.applicationManageView,
    AclPermission.priorityConfigView,
    AclPermission.alertGroupConfigView,
    AclPermission.emailPartnerView,
    AclPermission.emailConnectionView,
    AclPermission.emailTemplateView,
    AclPermission.databaseConnectionView,
    AclPermission.userManageView,
    AclPermission.roleManageView,
    AclPermission.customObjectView,
    AclPermission.maintenanceTimeConfigView,
    AclPermission.filterAlertConfigView,
    AclPermission.modifyAlertConfigView,
    AclPermission.syslogView,
    AclPermission.executionView,
    AclPermission.executionGroupView,
    AclPermission.variableView,
    AclPermission.executionHistoryView,
  ];

  action: PermissionActionEnum;
  module?: PermissionModuleEnum;
  type?: PermissionTypeEnum;
  moduleId?: string;
  moduleParentId?: string;

  constructor(module: PermissionModuleEnum, action: PermissionActionEnum) {
    this.module = module;
    this.action = action;
    this.type = PermissionTypeEnum.MODULE;
  }
}
