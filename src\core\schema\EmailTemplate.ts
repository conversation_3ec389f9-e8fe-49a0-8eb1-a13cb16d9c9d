import { z } from 'zod';
import { FileStorageScheme } from './FileStorage';

export const EmailTemplateSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  content: z.string().optional(),
  subject: z.string().optional(),
  files: z.array(z.instanceof(File)).optional(),
  fileStorages: z.array(FileStorageScheme).optional(),
  to: z.array(z.string()).optional(),
  cc: z.array(z.string()).optional(),
});

export type EmailTemplate = z.infer<typeof EmailTemplateSchema>;
