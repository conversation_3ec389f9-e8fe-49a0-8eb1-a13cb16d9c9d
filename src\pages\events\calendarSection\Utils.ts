import { Dayjs } from 'dayjs';
import { CalendarMode } from '../Types';
import { ViewModeEnum } from '../Contants';

export function getAllDatesOfMonth(date: Dayjs): Dayjs[][] {
  const currentMonth = date.get('month');
  const datesOfMonth: Dayjs[][] = [];
  const startDate = date.startOf('month');
  let temp = startDate.clone();
  datesOfMonth.push(getAllDateOfWeek(temp));
  const TRUE = true;
  while (TRUE) {
    temp = temp.add(7, 'day');
    const datesOfWeek = getAllDateOfWeek(temp);
    // So sanh ngay cuoi trong tuan xem co con thuoc thang do hay khong
    if (datesOfWeek.at(-1)?.get('month') === currentMonth || datesOfWeek.at(0)?.get('month') === currentMonth) {
      datesOfMonth.push(datesOfWeek);
    } else {
      break;
    }
  }
  return datesOfMonth;
}

export function getAllDateOfWeek(day: Dayjs): Dayjs[] {
  const datesOfWeek: Dayjs[] = [];
  // Start date of week in dayjs is Sunday change to Monday
  const startDate = day.startOf('week').add(1, 'day');
  datesOfWeek.push(startDate);
  for (let i = 0; i <= 5; i++) {
    const date = startDate.add(i + 1, 'day');
    datesOfWeek.push(date);
  }
  return datesOfWeek;
}

export function getCalendarTitle(calendarMode: CalendarMode) {
  if (calendarMode.viewMode === ViewModeEnum.MONTH) {
    return calendarMode.day.format('MMMM YYYY');
  }
  return calendarMode.day.format('YYYY');
}

export function getTableTitle(calendarMode: CalendarMode) {
  if (calendarMode.selectedDates.length === 1) {
    return calendarMode.selectedDates[0].format('DD MMMM YYYY');
  }
  if (calendarMode.selectedDates.length > 1) {
    return '';
  }
  return getCalendarTitle(calendarMode);
}

export function getDateRange(fromDate: Dayjs, toDate: Dayjs) {
  const step = fromDate.isAfter(toDate) ? -1 : 1;
  const result: Dayjs[] = [];
  let temp = fromDate.clone();
  const TRUE = true;
  while (TRUE) {
    if (temp.isSame(toDate, 'day')) {
      result.push(temp);
      break;
    }
    result.push(temp);
    temp = temp.add(step, 'day');
  }
  return result;
}

export function groupConsecutiveDates(dates: Dayjs[]): Dayjs[][] {
  if (!dates.length) {
    return [];
  }
  const sortedDates = [...dates].sort((e1, e2) => e1.diff(e2, 'day'));
  return sortedDates.reduce<Dayjs[][]>((groups, currentDate) => {
    const lastGroup = groups.at(groups.length - 1);
    if (lastGroup) {
      const lastDate = lastGroup.at(lastGroup.length - 1);
      if (lastDate && lastDate.add(1, 'day').isSame(currentDate, 'day')) {
        lastGroup.push(currentDate);
        return groups;
      } else {
        return [...groups, [currentDate]];
      }
    } else {
      return [[currentDate]];
    }
    return groups;
  }, []);
}
