import { PaginationRequest } from '@api/Type';
import { SortType } from './SortType';

export const LIMIT_SIZE_LENGTH = 1_000_000;
export const DEFAULT_PAGINATION_REQUEST: PaginationRequest = {
  page: 0,
  size: 10,
  sortBy: 'createdDate',
  sortOrder: SortType.DESC,
  search: '',
};
export const DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME: PaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortBy: 'name',
  sortOrder: SortType.ASC,
};
