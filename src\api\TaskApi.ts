import { createCursorPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { TaskSchema } from '@core/schema/Task';
import { AssignTaskRequest, CreateOrUpdateTask } from './Type';
import { AmountOfTaskSchema } from '@core/schema/AmountOfTask';
import { z } from 'zod';
import { TaskUserSchema } from '@core/schema/TaskUser';
import { FilterTaskModel } from '@models/FilterTaskModel';
import { TaskCursor, TaskCursorSchema } from '@core/schema/TaskCursor';
import { CursorPagingRequestModel } from '@models/CursorPagingRequestModel';

export class TaskApi {
  static createOrUpdate(taskModel: CreateOrUpdateTask) {
    return createRequest({
      url: `${BaseURL.task}`,
      method: 'POST',
      data: taskModel,
      schema: createResponseSchema(TaskSchema),
    });
  }
  static findAll(searchRequest: FilterTaskModel, cursor: CursorPagingRequestModel<TaskCursor>) {
    return createRequest({
      url: `${BaseURL.task}/search`,
      method: 'POST',
      data: searchRequest,
      params: cursor,
      schema: createResponseSchema(createCursorPageSchema(TaskSchema, TaskCursorSchema)),
    });
  }
  static countTask(searchRequest: FilterTaskModel) {
    return createRequest({
      url: `${BaseURL.task}/count`,
      method: 'POST',
      data: searchRequest,
      schema: createResponseSchema(z.array(AmountOfTaskSchema)),
    });
  }
  static deleteTask(taskId: number) {
    return createRequest({
      url: `${BaseURL.task}/:id`,
      method: 'DELETE',
      pathVariable: {
        id: taskId,
      },
      schema: createResponseSchema(z.string()),
    });
  }
  static completeTask(taskId: number) {
    return createRequest({
      url: `${BaseURL.task}/complete`,
      method: 'POST',
      params: {
        id: taskId,
      },
      schema: createResponseSchema(TaskSchema),
    });
  }
  static assignTask(assignTaskRequest: AssignTaskRequest) {
    return createRequest({
      url: `${BaseURL.task}/assign`,
      method: 'POST',
      data: assignTaskRequest,
      schema: createResponseSchema(TaskSchema),
    });
  }
  static findByTaskId(taskId: number) {
    return createRequest({
      url: `${BaseURL.task}/:id`,
      method: 'GET',
      pathVariable: {
        id: taskId,
      },
      schema: createResponseSchema(TaskSchema),
    });
  }
  static getAssigneeHistory(taskId: number) {
    return createRequest({
      url: `${BaseURL.task}/:id/histories`,
      method: 'GET',
      pathVariable: {
        id: taskId,
      },
      schema: createResponseSchema(z.array(TaskUserSchema)),
    });
  }
}
