import React, { useMemo, useState } from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { Flex } from '@mantine/core';
import { ABSENCE_INTERVAL_MAX, ABSENCE_INTERVAL_MIN, CollectEmailConfigModel } from '@models/CollectEmailConfigModel';
import { KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import { SelectWithPage } from '@components/SelectWithPage';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { EmailConfigApi } from '@api/EmailConfigApi';
import { DEFAULT_EMAIL_CONFIG_PAGINATION_REQUEST } from '@common/constants/EmailConfigConstant';
import { CollectEmailConfig } from '@core/schema/CollectEmailConfig';
import { optionIntervalTime } from '@common/constants/CollectEmailConfigConstant';
import { CollectEmailConfigTypeEnum } from '@common/constants/CollectEmailConfigTypeConstant';

interface Props {
  form: UseFormReturn<CollectEmailConfigModel>;
  isViewMode?: boolean;
  oldData?: CollectEmailConfig;
}

const ConnectionSession = ({ form, isViewMode, oldData }: Props) => {
  const { control } = form;
  const type = useWatch({
    control,
    name: 'type',
  });
  const [emailConfigSearchParams, setEmailConfigSearchParams] = useState(DEFAULT_EMAIL_CONFIG_PAGINATION_REQUEST);
  const {
    fetchNextPage: fetchNextPageEmailConfig,
    flatData: optionsEmailConfig,
    isFetching: isEmailConfigFetching,
  } = useInfiniteFetch(EmailConfigApi.findAll(emailConfigSearchParams), {
    showLoading: false,
  });
  const emailConfigComboxOptions = useMemo(() => {
    return optionsEmailConfig.map((obj) => ({ value: `${obj.id}`, label: `${obj.email}-${obj.protocolType}` }));
  }, [optionsEmailConfig]);

  return (
    <Flex direction='column' gap='sm'>
      <form>
        <Controller
          name='emailConfigId'
          control={control}
          render={({ field }) => (
            <SelectWithPage
              label='Connection'
              disabled={isViewMode}
              required
              options={emailConfigComboxOptions}
              handleScrollToBottom={fetchNextPageEmailConfig}
              onChange={(_, data) => {
                const valueId = Number(data?.value);
                field.onChange(valueId);
              }}
              onSearch={(val) => {
                setEmailConfigSearchParams((prev) => ({ ...prev, page: 0, email: val }));
              }}
              onBlur={() => {
                setEmailConfigSearchParams(DEFAULT_EMAIL_CONFIG_PAGINATION_REQUEST);
              }}
              value={
                [
                  ...emailConfigComboxOptions,
                  {
                    value: field.value.toString(),
                    label: oldData?.emailConfigEmail || '',
                  },
                ].filter((e) => e.value === field.value.toString())[0] || undefined
              }
              isLoading={isEmailConfigFetching}
            />
          )}
        />
        <Controller
          name='intervalTime'
          control={control}
          render={({ field }) => (
            <KanbanSelect
              disabled={isViewMode}
              allowDeselect={false}
              label='Interval Time'
              required
              data={optionIntervalTime}
              {...field}
              value={field.value.toString()}
              onChange={(val) => field.onChange(Number(val))}
            />
          )}
        />
        {CollectEmailConfigTypeEnum.ABSENCE_ALERT === type && (
          <>
            <Controller
              name='absenceInterval'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanNumberInput
                  required
                  label='Check in last (seconds)'
                  {...field}
                  disabled={isViewMode}
                  min={ABSENCE_INTERVAL_MIN}
                  max={ABSENCE_INTERVAL_MAX}
                  allowDecimal={false}
                  allowNegative={false}
                  error={fieldState.error?.message}
                />
              )}
            />

            <Controller
              name='alertRepeatInterval'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanNumberInput
                  required
                  label='The last alert is more than'
                  {...field}
                  disabled={isViewMode}
                  min={ABSENCE_INTERVAL_MIN}
                  max={ABSENCE_INTERVAL_MAX}
                  allowDecimal={false}
                  allowNegative={false}
                  error={fieldState.error?.message}
                />
              )}
            />
          </>
        )}
      </form>
    </Flex>
  );
};

export default ConnectionSession;
