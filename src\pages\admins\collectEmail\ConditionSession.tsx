import React, { useEffect, useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Flex } from '@mantine/core';
import { CollectEmailConfigModel } from '@models/CollectEmailConfigModel';
import QueryBuilderComponent, { QueryBuilderField } from '@components/queryBuilder';
import { RuleGroupType, RuleType } from 'react-querybuilder';
import { QueryRuleGroupTypeModel } from '@models/RuleGroupTypeModel';
import { baseFields } from './Fields';
import useFetch from '@core/hooks/useFetch';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST } from '../groupConfig/Constants';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { CollectEmailOperatorEnum } from '@common/constants/CollectEmailConfigConstant';

interface Props {
  form: UseFormReturn<CollectEmailConfigModel>;
  isViewMode?: boolean;
}
const BASE_RULE_DEFAULT: RuleType = { field: 'subject', operator: QueryBuilderOperatorEnum.IS, value: '' };
const ConditionSession = ({ form, isViewMode }: Props) => {
  const { control, setValue } = form;
  const [fields, setFields] = useState<QueryBuilderField[]>(baseFields);
  const { data: listCustomObject } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), {
    placeholderData: (prev) => prev,
  });
  useEffect(() => {
    if (listCustomObject?.data) {
      const customObjecFields: QueryBuilderField[] =
        listCustomObject?.data.content.map((customObj) => ({
          name: customObj.id.toString(),
          label: customObj.name,
          operators: Object.values(QueryBuilderOperatorEnum),
        })) ?? [];

      setFields((prev) => [...prev, ...customObjecFields]);
    }
  }, [listCustomObject?.data]);
  return (
    <Flex direction='column' gap='sm'>
      <Controller
        name='ruleGroup'
        control={control}
        render={({ field, fieldState }) => (
          <QueryBuilderComponent
            value={field.value as RuleGroupType}
            disabled={isViewMode}
            onChange={(val) => {
              setValue('ruleGroup', val as QueryRuleGroupTypeModel);
            }}
            fields={fields}
            baseRule={BASE_RULE_DEFAULT}
            operators={Object.values(CollectEmailOperatorEnum)}
            error={fieldState.error?.message}
          />
        )}
      />
    </Flex>
  );
};

export default ConditionSession;
