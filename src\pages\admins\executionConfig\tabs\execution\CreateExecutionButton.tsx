import Modal from '@components/Modal';
import { Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton } from 'kanban-design-system';
import React, { useCallback } from 'react';
import ExecutionForm from './ExecutionForm';
import { useForm } from 'react-hook-form';
import { ExecutionModel, ExecutionModelSchema } from '@models/ExecutionModel';
import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { IconPlus } from '@tabler/icons-react';
import useMutate from '@core/hooks/useMutate';
import { ExecutionApi } from '@api/ExecutionApi';
import { zodResolver } from '@hookform/resolvers/zod';

const DEFAULT_EXECUTION_VALUE: ExecutionModel = {
  name: '',
  type: ExecutionTypeEnum.PYTHON,
  script: '',
};

interface Props {
  onCreateSuccess: () => void;
}

const CreateExecutionButton = ({ onCreateSuccess }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const form = useForm<ExecutionModel>({ defaultValues: DEFAULT_EXECUTION_VALUE, resolver: zodResolver(ExecutionModelSchema) });
  const onCloseModal = useCallback(() => {
    close();
    form.reset();
  }, [close, form]);
  const { mutate } = useMutate(ExecutionApi.createOrUpdate, {
    successNotification: 'Create Execution successfully.',
    onSuccess: () => {
      onCreateSuccess();
      onCloseModal();
    },
  });
  const { formState } = form;
  const onSaveClick = useCallback(() => {
    mutate(form.getValues());
  }, [form, mutate]);

  return (
    <>
      <KanbanButton size='xs' onClick={open} leftSection={<IconPlus />}>
        Create Execution
      </KanbanButton>
      <Modal
        size='xl'
        opened={opened}
        onClose={onCloseModal}
        title='Create Execution'
        actions={
          <KanbanButton onClick={onSaveClick} disabled={!formState.isValid}>
            Save
          </KanbanButton>
        }>
        <Box p='xs'>
          <ExecutionForm form={form} fetchGroup={opened} readOnly={false} />
        </Box>
      </Modal>
    </>
  );
};

export default CreateExecutionButton;
