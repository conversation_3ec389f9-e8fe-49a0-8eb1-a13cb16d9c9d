import axios, { AxiosInstance, AxiosRequestConfig, Method } from 'axios';
import { authenticationInterceptor, requestIdInterceptor } from './inteceptors/RequestInterceptors';
import { handleResponseInterceptor } from './inteceptors/ResponseInterceptors';
import { generatePath, PathParam } from 'react-router-dom';
import { z } from 'zod';
import { directDispath } from '@store';
import { pageLoadingSlice } from '@slices/PageLoadingSlice';
import { AppConfig } from '@core/hooks/AppConfigTypes';

const axiosInstance: AxiosInstance = axios.create({
  baseURL: '',
});
axiosInstance.interceptors.request.use(requestIdInterceptor);
axiosInstance.interceptors.request.use(authenticationInterceptor);
handleResponseInterceptor(axiosInstance.interceptors.response);

const extraConfigsDefault = {
  useLoading: true,
  useErrorNotification: true,
};
export type ExtraConfigApiType = Partial<typeof extraConfigsDefault>;

export type PathVariableType = {
  [key in PathParam<string>]: string | null;
};

export type RequestConfig<Response, SearchParam = unknown, BodyData = unknown> = {
  url: string;
  schema?: z.ZodType<Response>;
  method: Method;
  pathVariable?: PathVariableType;
  params?: SearchParam;
  data?: BodyData;
} & Omit<AxiosRequestConfig, 'method' | 'url' | 'data' | 'params'>;

export async function callRequest<Response, SearchParam = unknown, BodyData = unknown>(
  requestConfig: RequestConfig<Response, SearchParam, BodyData>,
  appConfig?: Pick<AppConfig<Response>, 'showLoading' | 'throwParsedError'>,
) {
  const { data, method, params, pathVariable, schema, url, ...otherConfig } = requestConfig;
  const { showLoading = true, throwParsedError = true } = appConfig || {};
  const mainUrl = new URL(url);
  const remainUrl = `${mainUrl.pathname}${mainUrl.search}${mainUrl.hash}`;
  const parsedUrl = mainUrl.origin + generatePath(remainUrl, pathVariable);
  try {
    if (showLoading) {
      directDispath(pageLoadingSlice.actions.increment());
    }
    const result = await axiosInstance({
      url: `${parsedUrl}`,
      method,
      params,
      data,
      ...otherConfig,
    });
    if (schema) {
      const parseResult = schema.safeParse(result.data);
      if (parseResult.success) {
        return parseResult.data;
      }
      console.error(`Call request success but parsed response error : (Method: ${method} - Url: ${parsedUrl})`);
      console.error(parseResult.error.message);
      if (throwParsedError) {
        throw parseResult.error;
      }
    }
    return result.data as Response;
  } finally {
    if (showLoading) {
      directDispath(pageLoadingSlice.actions.decrement());
    }
  }
}
