import React, { useMemo } from 'react';
import { PaginationRequest } from '@api/Type';
import { UserApi } from '@api/UserApi';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { useDebouncedState } from '@mantine/hooks';
import { DEFAULT_DEBOUNCE_TIME } from '@common/constants/ValidationConstant';
import ComboboxLoadMore, { ComboboxLoadMoreData } from './ComboboxLoadMore';

const USER_PAGEGING_DEFAULT: PaginationRequest = { page: 0, search: '', sortBy: 'userName' };

interface Props {
  value: string[] | undefined;
  onChange: (userNames: string[] | undefined) => void;
  description?: string;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  justShowActiveUser?: boolean;
  withSystemUser?: boolean;
}

export const SystemUserName = 'kanban_system';

const UserMultipleSelect = ({
  description,
  disabled,
  justShowActiveUser = true,
  label,
  onChange,
  placeholder,
  value,
  withSystemUser = false,
}: Props) => {
  const [searchValue, setSearchValue] = useDebouncedState('', DEFAULT_DEBOUNCE_TIME);
  const { fetchNextPage, flatData } = useInfiniteFetch(UserApi.findAll({ ...USER_PAGEGING_DEFAULT, search: searchValue }), {
    showLoading: false,
  });
  const userOptions = useMemo<ComboboxLoadMoreData[]>(() => {
    let options: ComboboxLoadMoreData[] = [];
    if (justShowActiveUser) {
      options = flatData.filter((user) => user.isActive).map((user) => ({ name: user.userName, id: user.userName }));
    }
    options = flatData.map((user) => ({ name: user.userName, id: user.userName }));
    if (withSystemUser) {
      return [{ name: 'System user', id: SystemUserName }, ...options];
    }
    return options;
  }, [flatData, justShowActiveUser, withSystemUser]);

  return (
    <ComboboxLoadMore
      options={userOptions}
      onChange={(selectedValue) => onChange(selectedValue?.map((user) => user.id))}
      label={label}
      description={description}
      placeholder={placeholder}
      onSearch={setSearchValue}
      onScroll={fetchNextPage}
      onClickedOption={() => setSearchValue('')}
      values={value?.map((userName) => ({ id: userName, name: userName })) || []}
      renderPillLabel={(data) => data.name}
      renderOptionLabel={(data) => data.name}
      scrollableForValue={true}
      disabled={disabled}
      clearable={!disabled}
      searchable={!disabled}
      clearSearchWhenBlur
    />
  );
};

export default UserMultipleSelect;
