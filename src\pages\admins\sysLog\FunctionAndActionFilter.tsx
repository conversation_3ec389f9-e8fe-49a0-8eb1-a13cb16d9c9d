import { SysLogActionEnum, SysLogActionFunction, SysLogFunctionEnum, SysLogFunctionLabel } from '@common/constants/SysLogConstants';
import { ComboboxData, ComboboxItem, ComboboxItemGroup } from '@mantine/core';
import { SysLogFilterModel } from '@models/SysLogFilterModel';
import { KanbanMultiSelect } from 'kanban-design-system';
import React from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { groupBy, sortBy } from 'lodash';
import { formatActionLogLabel } from './Utils';

const FunctionOptions: ComboboxData = sortBy(
  Object.keys(SysLogFunctionEnum).map((key) => ({
    value: key,
    label: SysLogFunctionLabel[key as SysLogFunctionEnum],
  })),
  'label',
);

const FunctionActionsMap = groupBy(Object.keys(SysLogActionEnum), (actionKey: SysLogActionEnum) => SysLogActionFunction[actionKey]);

const ActionOptions: ComboboxItemGroup<ComboboxItem>[] = Object.keys(FunctionActionsMap).map((functionKey) => ({
  group: SysLogFunctionLabel[functionKey as SysLogFunctionEnum],
  items: sortBy(
    FunctionActionsMap[functionKey].map((key) => ({
      value: key as SysLogActionEnum,
      label: formatActionLogLabel(key as SysLogActionEnum),
    })),
    'label',
  ),
}));

interface Props {
  form: UseFormReturn<SysLogFilterModel>;
}
const FunctionAndActionFilter = ({ form }: Props) => {
  const { control, getValues, setValue } = form;
  const functions = useWatch({ control, name: 'functions' });

  return (
    <>
      <Controller
        name='functions'
        control={control}
        render={({ field: { onChange, value } }) => (
          <KanbanMultiSelect
            data={FunctionOptions}
            searchable
            label='Function'
            placeholder='Search function'
            value={value}
            rightSectionPointerEvents='all'
            onChange={(value) => {
              onChange(value);
              const currentActions = getValues('actions');
              setValue(
                'actions',
                currentActions?.filter((action) => value?.includes(SysLogActionFunction[action])),
              );
            }}
            styles={{ root: { marginBottom: 0 } }}
          />
        )}
      />
      <Controller
        name='actions'
        control={control}
        render={({ field: { onChange, value } }) => (
          <KanbanMultiSelect
            data={
              functions.length > 0
                ? ActionOptions?.filter((ele) => functions?.some((functionKey) => ele.group === SysLogFunctionLabel[functionKey]))
                : ActionOptions
            }
            searchable
            label='Action'
            placeholder='Search actions'
            value={value}
            rightSectionPointerEvents='all'
            onChange={onChange}
            styles={{ root: { marginBottom: 0 } }}
          />
        )}
      />
    </>
  );
};

export default FunctionAndActionFilter;
