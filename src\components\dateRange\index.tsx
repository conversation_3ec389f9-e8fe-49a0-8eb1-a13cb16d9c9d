import { Box, Combobox, Grid, GridCol, InputBase, SimpleGrid, useCombobox } from '@mantine/core';
import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useState } from 'react';
import { DateRangeGroupEnum, DateRangeGroupLabel, DateRangeType, DateRangeTypeEnum } from './Constants';
import DateTimePickerComponent from '@components/DateTimePickerComponent';
import { KanbanText } from 'kanban-design-system';

interface Props {
  fromDate: Dayjs | undefined;
  toDate: Dayjs | undefined;
  onChange: (fromDate: Dayjs | undefined, toDate: Dayjs | undefined, rangeType: DateRangeTypeEnum) => void;
  rangeType?: DateRangeTypeEnum;
  error: string | undefined;
}

const DateRange = ({ error, fromDate, onChange, rangeType, toDate }: Props) => {
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });
  const [type, setType] = useState<DateRangeTypeEnum | undefined>(rangeType || DateRangeTypeEnum.TODAY);
  const dateRangeType = DateRangeType[type as DateRangeTypeEnum];

  const onOptionSubmit = useCallback(
    (selectedOption: string) => {
      setType(selectedOption as DateRangeTypeEnum);
      const dateRangeTypeValue = DateRangeType[selectedOption as DateRangeTypeEnum];
      const defaultDateRange = dateRangeTypeValue.getDefaultDateRange();
      onChange(defaultDateRange.fromDate, defaultDateRange.toDate, selectedOption as DateRangeTypeEnum);
      combobox.closeDropdown();
    },
    [combobox, onChange],
  );

  return (
    <Combobox store={combobox} onOptionSubmit={onOptionSubmit}>
      <Combobox.Target>
        <InputBase rightSection={<Combobox.Chevron />} component='button' label='Range date' onClick={() => combobox.toggleDropdown()}>
          {dateRangeType?.label}
        </InputBase>
      </Combobox.Target>
      <Box>
        {type === DateRangeTypeEnum.SINCE_SPECIFIC_DATE && (
          <Grid align='center'>
            <GridCol span={6}>
              <KanbanText>{dateRangeType.label}:</KanbanText>
            </GridCol>
            <GridCol span={6}>
              <DateTimePickerComponent
                onChange={(value) => onChange(value, dateRangeType.getDefaultDateRange().toDate, DateRangeTypeEnum.SINCE_SPECIFIC_DATE)}
                value={fromDate || dayjs()}
              />
            </GridCol>
          </Grid>
        )}
        {type === DateRangeTypeEnum.BEFORE_SPECIFIC_DATE && (
          <Grid align='center'>
            <GridCol span={6}>
              <KanbanText>{dateRangeType.label}:</KanbanText>
            </GridCol>
            <GridCol span={6}>
              <DateTimePickerComponent
                onChange={(value) => onChange(dateRangeType.getDefaultDateRange().fromDate, value, DateRangeTypeEnum.BEFORE_SPECIFIC_DATE)}
                value={toDate || dayjs()}
              />
            </GridCol>
          </Grid>
        )}
        {type === DateRangeTypeEnum.BETWEEN_DATES && (
          <Grid align='center'>
            <GridCol span={6}>
              <DateTimePickerComponent
                label='From date'
                onChange={(value) => onChange(value, toDate, DateRangeTypeEnum.BETWEEN_DATES)}
                value={fromDate || dayjs()}
              />
            </GridCol>
            <GridCol span={6}>
              <DateTimePickerComponent
                label='To date'
                onChange={(value) => onChange(fromDate, value, DateRangeTypeEnum.BETWEEN_DATES)}
                value={toDate || dayjs()}
              />
            </GridCol>
          </Grid>
        )}
        {error && (
          <SimpleGrid cols={1}>
            <KanbanText c='red'>{error}</KanbanText>
          </SimpleGrid>
        )}
      </Box>

      <Combobox.Dropdown>
        <Combobox.Options>
          <Combobox.Group label={DateRangeGroupLabel[DateRangeGroupEnum.PRESET]}>
            <Grid gutter='xs'>
              {Object.keys(DateRangeType)
                .filter((key) => DateRangeType[key as DateRangeTypeEnum].type === DateRangeGroupEnum.PRESET)
                .map((key) => (
                  <Grid.Col span={6} key={key}>
                    <Combobox.Option value={key}>{DateRangeType[key as DateRangeTypeEnum].label}</Combobox.Option>
                  </Grid.Col>
                ))}
            </Grid>
          </Combobox.Group>
          <Combobox.Group label={DateRangeGroupLabel[DateRangeGroupEnum.DATE_TIME]}>
            {Object.keys(DateRangeType)
              .filter((key) => DateRangeType[key as DateRangeTypeEnum].type === DateRangeGroupEnum.DATE_TIME)
              .map((key) => (
                <Combobox.Option key={key} value={key}>
                  {DateRangeType[key as DateRangeTypeEnum].label}
                </Combobox.Option>
              ))}
          </Combobox.Group>
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};

export default DateRange;
