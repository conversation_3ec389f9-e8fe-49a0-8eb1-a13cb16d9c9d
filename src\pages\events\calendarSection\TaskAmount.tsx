import { TaskTypeColor, TaskTypeEnum } from '@common/constants/TaskConstants';
import { Box, Flex, Text } from '@mantine/core';
import React from 'react';

interface Props {
  isSmallView: boolean;
  taskAmount?: number;
  taskType: TaskTypeEnum;
}

const TaskAmount = ({ isSmallView, taskAmount, taskType }: Props) => {
  if (!taskAmount) {
    return null;
  }
  return (
    <Flex align='center' gap='xs'>
      <Box bg={TaskTypeColor[taskType]} mih={10} miw={10} style={{ borderRadius: 10 }}></Box>
      {!isSmallView && (
        <Text c={TaskTypeColor[taskType]} fw={600} size='sm'>
          {taskAmount} {taskType === TaskTypeEnum.TASK ? 'Tasks' : 'Handovers'}
        </Text>
      )}
    </Flex>
  );
};

export default TaskAmount;
