import React, { useCallback, useEffect, useMemo } from 'react';
import { ActionIcon, Box, ComboboxData, Flex, Grid, Stack, Title } from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { KanbanButton, KanbanInput, KanbanSelect, KanbanText } from 'kanban-design-system';
import { useNavigate, useParams } from 'react-router-dom';
import { isNaN } from 'lodash';
import { TaskShiftEnum, TaskShiftLabel, TaskStatusEnum, TaskTypeEnum } from '@common/constants/TaskConstants';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';
import { TaskModel, TaskModelSchema } from '@models/TaskModel';
import AddTaskSession from './AddTaskSession';
import useMutate from '@core/hooks/useMutate';
import { TaskApi } from '@api/TaskApi';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { zodResolver } from '@hookform/resolvers/zod';
import dayjs from 'dayjs';
import useFetch from '@core/hooks/useFetch';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import CompleteTaskButton from '../taskListSection/CompleteTaskButton';
import { MAX_DESCRIPTION_LENGTH } from '@common/constants/ValidationConstant';
import { Task } from '@core/schema/Task';
import UserMultipleSelect from '@components/UserMultipleSelect';

const Session = ({ children, title }: { children: React.ReactNode; title: string }) => {
  return (
    <Stack p='sm' bg='white' gap='sm'>
      <Title order={4} c='primary'>
        {title}
      </Title>
      {children}
    </Stack>
  );
};

const CompleteButton = ({ form, task }: { form: UseFormReturn<TaskModel>; task: Task }) => {
  const navigate = useNavigate();
  const { getValues, watch } = form;
  const childrenTasks = watch('tasks');
  const { mutate: completeTaskMutate } = useMutate(TaskApi.completeTask, {
    onSuccess: () => navigate('../'),
  });
  const { mutate: saveBeforeCompleteMutate } = useMutate(TaskApi.createOrUpdate, {
    onSuccess: () => completeTaskMutate(task.id),
    successNotification: { enable: false },
    confirm: {
      title: 'Complete task',
      children: (
        <KanbanText>
          Are you sure to complete task <b>{task?.name || ''}</b>?
        </KanbanText>
      ),
    },
  });
  const onSave = useCallback(() => {
    const value = getValues();
    saveBeforeCompleteMutate({
      ...value,
      endTime: undefined,
      startTime: task?.startTime ? dayjs(task?.startTime).format() : dayjs().format(),
      taskIds: value?.tasks?.map((ele) => ele.id),
    });
  }, [getValues, saveBeforeCompleteMutate, task?.startTime]);
  return <CompleteTaskButton disabled={childrenTasks?.some((ele) => !ele.deleted && !ele.currentAssigneeUserName)} task={task} onClick={onSave} />;
};

const SHIFT_OPTIONS: ComboboxData = Object.keys(TaskShiftEnum).map((key) => ({ value: key, label: TaskShiftLabel[key as TaskShiftEnum] }));

const CreateOrUpdateShiftHandoverPage = () => {
  const currentUser = useSelector(getCurrentUser).userInfo;
  const TASK_DEFAULT_VALUE: TaskModel = useMemo(
    () => ({
      name: '',
      description: '',
      type: TaskTypeEnum.SHIFT_HANDOVER_TASK,
    }),
    [],
  );
  const navigate = useNavigate();
  const { taskId: taskIdString } = useParams();
  const taskId = !!taskIdString && !isNaN(taskIdString) ? parseInt(taskIdString) : 0;
  const isCreateMode = !taskId;
  const form = useForm<TaskModel>({
    defaultValues: TASK_DEFAULT_VALUE,
    mode: 'onChange',
    resolver: zodResolver(TaskModelSchema),
  });
  const { control, formState, getValues, register, reset } = form;
  const { data: taskData } = useFetch(TaskApi.findByTaskId(taskId), { enabled: !!taskId });
  const { mutate: createOrUpdateTaskMutate } = useMutate(TaskApi.createOrUpdate, {
    onSuccess: () => {
      close();
      form.reset();
      navigate('../');
    },
  });
  useEffect(() => {
    const task = taskData?.data;
    if (task) {
      reset({
        ...task,
        startTime: task.startTime ? dayjs(task.startTime).format() : undefined,
        endTime: task.endTime ? dayjs(task.endTime).format() : undefined,
        handoverUsers: task.handoverUsers?.map((user) => user.userName),
        tasks: task?.childrenTasks,
      });
    }
  }, [reset, taskData?.data]);
  const onSave = useCallback(() => {
    const value = getValues();
    createOrUpdateTaskMutate({
      ...value,
      endTime: undefined,
      startTime: taskData?.data?.startTime ? dayjs(taskData?.data?.startTime).format() : dayjs().format(),
      taskIds: value?.tasks?.map((ele) => ele.id),
    });
  }, [createOrUpdateTaskMutate, getValues, taskData?.data?.startTime]);
  const hasEditPermission = useMemo(() => isAnyPermissions([AclPermission.taskEdit]), []);
  const isDone = TaskStatusEnum.DONE === taskData?.data?.status;
  const editable = isCreateMode || (!isDone && (hasEditPermission || taskData?.data?.createdBy === currentUser?.userName));
  const { isValid } = formState;
  return (
    <Stack gap='md' mah='var(--kanban-appshell-maxheight-content)'>
      <Flex align='center' justify='space-between'>
        <Flex align='center' gap='sm'>
          <ActionIcon variant='outline' onClick={() => navigate('../')}>
            <IconArrowLeft />
          </ActionIcon>
          <Title order={3}>{isCreateMode ? 'Create' : 'Update'} Shift Handover Task</Title>
        </Flex>
        <Flex align='center' gap='sm'>
          {taskData?.data && <CompleteButton form={form} task={taskData.data} />}
          {editable && (
            <KanbanButton onClick={onSave} disabled={!isValid}>
              Save
            </KanbanButton>
          )}
          <KanbanButton variant='outline' onClick={() => navigate('../')}>
            Cancel
          </KanbanButton>
        </Flex>
      </Flex>
      <Stack gap='md' style={{ flexGrow: 1 }}>
        <Session title='General information'>
          <Grid>
            <Grid.Col span={3}>
              <Controller
                control={control}
                name='handoverUsers'
                render={({ field }) => (
                  <UserMultipleSelect label='Handover User' placeholder='Select handover user' {...field} disabled={!editable} />
                )}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Controller
                control={control}
                name='shift'
                render={({ field }) => (
                  <KanbanSelect disabled={!editable} label='Shift' data={SHIFT_OPTIONS} placeholder='Select Shift' {...field} allowDeselect={false} />
                )}
              />
            </Grid.Col>
          </Grid>
          <Grid>
            <Grid.Col span={6}>
              <KanbanInput
                disabled={!editable}
                label='Description'
                placeholder='Enter description'
                {...register('description')}
                maxLength={MAX_DESCRIPTION_LENGTH}
              />
            </Grid.Col>
          </Grid>
        </Session>
        <Box style={{ flexGrow: 1, overflow: 'auto' }}>
          <Session title='List tasks'>
            <AddTaskSession form={form} editable={editable} />
          </Session>
        </Box>
      </Stack>
    </Stack>
  );
};

export default CreateOrUpdateShiftHandoverPage;
