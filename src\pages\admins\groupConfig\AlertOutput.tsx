import React, { use<PERSON>emo, useState } from 'react';
import { Flex, SimpleGrid } from '@mantine/core';
import { AlertGroupConfigModel } from '@models/AlertGroupConfigModel';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { SelectWithPage } from '@components/SelectWithPage';
import { DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME } from '@common/constants/PaginationRequestConstant';
import { ServiceApi } from '@api/ServiceApi';
import { ApplicationApi } from '@api/ApplicationApi';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import useFetch from '@core/hooks/useFetch';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import { sortBy } from 'lodash';
import { CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH, CHARACTER_CONTENT_ALERT_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { AlertGroupConfig } from '@core/schema/AlertGroupConfig';
import { ALERT_GROUP_OUTPUT_LABEL, AlertGroupOutputEnum } from '@common/constants/AlertGroupConfigConstants';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';

interface Props {
  disabled: boolean;
  form: UseFormReturn<AlertGroupConfigModel>;
  groupConfig?: AlertGroupConfig;
}

const AlertOutput = ({ disabled, form, groupConfig }: Props) => {
  const { control, register, setValue } = form;
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME);
  const serviceId = useWatch({ control, name: 'customServiceId' });
  const applicationId = useWatch({ control, name: 'customApplicationId' });
  const alertOutput = useWatch({ control, name: 'alertOutput' });
  const {
    fetchNextPage: fetchNextPageService,
    flatData: services,
    isFetching: isServiceFetching,
  } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
  });
  const {
    fetchNextPage: fetchNextPageApplication,
    flatData: applications,
    isFetching: isApplicationFetching,
  } = useInfiniteFetch(ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds: serviceId ? [serviceId] : [] }), {
    showLoading: false,
  });
  const { data: priorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });
  const serviceComboxOptions = useMemo(() => {
    return services.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [services]);

  const applicationComboxOptions = useMemo(() => {
    return applications.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [applications]);
  const priorityConfigOptions = useMemo(() => {
    return sortBy(
      priorityConfigs?.data?.map((config) => ({ value: `${config.id}`, label: config.name + (config.deleted ? ' (DELETED)' : '') })) || [],
      (option) => option.label.toLowerCase(),
    );
  }, [priorityConfigs?.data]);
  return (
    <>
      <Controller
        name='alertOutput'
        control={control}
        render={({ field }) => (
          <KanbanSelect
            disabled={disabled}
            required
            data={Object.values(AlertGroupOutputEnum).map((key) => ({ value: key, label: ALERT_GROUP_OUTPUT_LABEL[key] }))}
            {...field}
          />
        )}
      />
      {AlertGroupOutputEnum.CUSTOM === alertOutput && (
        <Flex direction='column' gap='sm'>
          <SimpleGrid cols={2}>
            <Controller
              name='customServiceId'
              control={control}
              render={({ field }) => (
                <SelectWithPage
                  label='Service Name'
                  disabled={disabled}
                  required={true}
                  options={serviceComboxOptions}
                  handleScrollToBottom={fetchNextPageService}
                  onChange={(value) => {
                    if (value !== field.value) {
                      setValue('customApplicationId', undefined);
                      field.onChange(value);
                    }
                  }}
                  onSearch={(val) => {
                    setServiceSearchParams((prev) => ({ ...prev, page: 0, name: val }));
                  }}
                  value={
                    [
                      ...serviceComboxOptions,
                      {
                        value: groupConfig?.customService?.id || '',
                        label: groupConfig?.customService?.name || '',
                      },
                    ].filter((e) => e.value === field.value)[0] || undefined
                  }
                  isLoading={isServiceFetching}
                />
              )}
            />
            <Controller
              name='customApplicationId'
              control={control}
              render={({ field }) => {
                return (
                  <SelectWithPage
                    disabled={disabled}
                    label='Application Name'
                    required={true}
                    options={applicationComboxOptions}
                    handleScrollToBottom={fetchNextPageApplication}
                    onChange={(value) => field.onChange(value)}
                    onSearch={(val) => {
                      setApplicationSearchParams((prev) => ({ ...prev, page: 0, name: val }));
                    }}
                    value={
                      applicationId
                        ? [
                            ...applicationComboxOptions,
                            {
                              value: groupConfig?.customApplication?.id || '',
                              label: groupConfig?.customApplication?.name || '',
                            },
                          ].find((e) => e.value === applicationId) || undefined
                        : undefined
                    }
                    isLoading={isApplicationFetching}
                  />
                );
              }}
            />
            <KanbanInput
              label='Contact'
              disabled={disabled}
              maxLength={CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH}
              required
              {...register('customRecipient')}
            />
            <Controller
              name='customPriorityConfigId'
              control={control}
              render={({ field }) => (
                <KanbanSelect
                  required
                  disabled={disabled}
                  data={priorityConfigOptions}
                  label='Priority'
                  searchable
                  allowDeselect={false}
                  {...field}
                  onChange={(value) => field.onChange(value || undefined)}
                />
              )}
            />
          </SimpleGrid>
          <KanbanInput
            label='Alert content'
            {...register('customContent')}
            required
            description={getMaxLengthMessage(CHARACTER_CONTENT_ALERT_MAX_LENGTH)}
            maxLength={CHARACTER_CONTENT_ALERT_MAX_LENGTH}
          />
        </Flex>
      )}
    </>
  );
};

export default AlertOutput;
