import { KanbanTabs, KanbanTabsType } from 'kanban-design-system';
import React, { useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ExecutionConfigTabEnum, TAB_SEARCH_PARAM_KEY } from './Constants';
import ExecutionConfig from './tabs/execution';
import ExecutionGroupConfig from './tabs/executionGroup';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';

const ExecutionConfigPage = () => {
  const hasExecutionViewPermission = isAnyPermissions([AclPermission.executionView]);
  const hasExecutionGroupViewPermission = isAnyPermissions([AclPermission.executionGroupView]);

  const defaultTab = useMemo<ExecutionConfigTabEnum>(() => {
    if (hasExecutionGroupViewPermission) {
      return ExecutionConfigTabEnum.EXECUTION_GROUP;
    }
    return ExecutionConfigTabEnum.EXECUTION;
  }, [hasExecutionGroupViewPermission]);
  const [searchParams, setSearchParams] = useSearchParams({ [TAB_SEARCH_PARAM_KEY]: defaultTab });
  const tabs = useMemo<KanbanTabsType>(() => {
    return {
      ...(hasExecutionGroupViewPermission && {
        [ExecutionConfigTabEnum.EXECUTION_GROUP]: {
          title: 'Execution Group',
          content: <ExecutionGroupConfig />,
        },
      }),
      ...(hasExecutionViewPermission && {
        [ExecutionConfigTabEnum.EXECUTION]: {
          title: 'Execution',
          content: <ExecutionConfig />,
        },
      }),
    };
  }, [hasExecutionGroupViewPermission, hasExecutionViewPermission]);

  const handleChangeTab = useCallback(
    (value: string | null) => {
      if (value) {
        setSearchParams((params) => {
          params.set(TAB_SEARCH_PARAM_KEY, value);
          return params;
        });
      }
    },
    [setSearchParams],
  );

  return (
    <KanbanTabs
      configs={{
        defaultValue: defaultTab,
        value: searchParams.get(TAB_SEARCH_PARAM_KEY),
        onChange: handleChangeTab,
      }}
      tabs={tabs}
    />
  );
};

export default ExecutionConfigPage;
