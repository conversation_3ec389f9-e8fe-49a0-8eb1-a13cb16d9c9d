import { EnumKey } from '@common/utils/Type';

export enum AlertDurationCompareOperatorEnum {
  EQUAL = 'EQUAL',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
}

export const OperatorLabel: Enum<PERSON>ey<AlertDurationCompareOperatorEnum> = {
  [AlertDurationCompareOperatorEnum.EQUAL]: 'Equal',
  [AlertDurationCompareOperatorEnum.GREATER_THAN]: 'Greate Than',
  [AlertDurationCompareOperatorEnum.LESS_THAN]: 'Less Than',
};

export enum AlertDurationCompareUnitEnum {
  HOUR = 'HOUR',
  MINUTE = 'MINUTE',
  SECOND = 'SECOND',
}

export const DurationUnitLabel: EnumKey<AlertDurationCompareUnitEnum> = {
  [AlertDurationCompareUnitEnum.HOUR]: 'Hour',
  [AlertDurationCompareUnitEnum.MINUTE]: 'Minute',
  [AlertDurationCompareUnitEnum.SECOND]: 'Second',
};

export const DEAFULT_PAGE_SIZE = 30;

export const MAX_LENGTH_CHARACTER = 300;

export const MAX_ALERT_GROUP_ID = 999999999999999;

export const MAX_LENGTH_DESCRIPTION = `The input field allows a maximum of ${MAX_LENGTH_CHARACTER} characters.`;

export const DEFAULT_DURATION_OPERATOR: AlertDurationCompareOperatorEnum = AlertDurationCompareOperatorEnum.LESS_THAN;

export const DEFAULT_DURATION_UNIT: AlertDurationCompareUnitEnum = AlertDurationCompareUnitEnum.MINUTE;
