import React from 'react';
import { KanbanButton } from 'kanban-design-system';
import { ApplicationApi } from '@api/ApplicationApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { areAllArraysEmpty } from '@common/utils/StringUtils';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

type DeleteModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  applicationId?: string;
  applicationName?: string;
};

const DeleteModal: React.FC<DeleteModalProps> = ({ applicationId, applicationName, onClose, opened, refetchList }) => {
  const { data: dependencies } = useFetch(ApplicationApi.findAllDependenciesById(applicationId || ''), {
    enabled: !!applicationId && opened,
  });
  const dependencyConfig: DependencyItem[] = [
    {
      dependencyEntity: 'webhooks',
      dependencies: dependencies?.data?.webHooks ?? [],
    },
    {
      dependencyEntity: 'collect email configs',
      dependencies: dependencies?.data?.collectEmailConfigs ?? [],
    },
    {
      dependencyEntity: 'collect database configs',
      dependencies: dependencies?.data?.databaseCollects ?? [],
    },
    {
      dependencyEntity: 'alert group configs',
      dependencies: dependencies?.data?.alertGroupConfigs ?? [],
    },
    {
      dependencyEntity: 'maintenance time configs',
      dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
    },
    {
      dependencyEntity: 'modify alert configs',
      dependencies: dependencies?.data?.modifyAlertConfigs ?? [],
    },
    {
      dependencyEntity: 'database threshold configs',
      dependencies: dependencies?.data?.databaseThresholdConfigs ?? [],
    },
  ];
  const { mutate: deleteByIdMutate } = useMutate(ApplicationApi.deleteById, {
    successNotification: { message: 'Deleted successfully!' },
    onSuccess: () => {
      refetchList();
      onClose();
    },
  });
  const isDisabledButtonConfirm = !areAllArraysEmpty(dependencies?.data || {});
  return (
    <Modal
      size='xl'
      opened={opened}
      onClose={() => {
        onClose();
      }}
      title={'Delete Application'}
      actions={
        <KanbanButton onClick={() => deleteByIdMutate(applicationId ?? '')} disabled={isDisabledButtonConfirm}>
          Confirm
        </KanbanButton>
      }>
      <DependenciesWarningAlert mainEntity={`Application ${applicationName}`} dependencyConfigs={dependencyConfig} isDeleted={true} />
      {!isDisabledButtonConfirm && <DeleteConfirmMessage name={applicationName} />}
    </Modal>
  );
};

export default DeleteModal;
