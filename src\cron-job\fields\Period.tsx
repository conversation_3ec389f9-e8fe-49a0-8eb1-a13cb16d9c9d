/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import { Select } from '@mantine/core';
import React, { useCallback, useMemo } from 'react';

import { DEFAULT_LOCALE_EN } from '../LocaleSelect';
import { PeriodProps, PeriodType } from '../TypesSelect';

export default function Period(props: PeriodProps) {
  const { allowClear, allowedPeriods, disabled, locale, readOnly, setValue, shortcuts, value } = props;

  const options = useMemo(() => {
    const result: { value: string; label: string }[] = [];

    if (allowedPeriods.includes('year')) {
      result.push({
        value: 'year',
        label: locale.yearOption || DEFAULT_LOCALE_EN.yearOption,
      });
    }

    if (allowedPeriods.includes('month')) {
      result.push({
        value: 'month',
        label: locale.monthOption || DEFAULT_LOCALE_EN.monthOption,
      });
    }

    if (allowedPeriods.includes('week')) {
      result.push({
        value: 'week',
        label: locale.weekOption || DEFAULT_LOCALE_EN.weekOption,
      });
    }

    if (allowedPeriods.includes('day')) {
      result.push({
        value: 'day',
        label: locale.dayOption || DEFAULT_LOCALE_EN.dayOption,
      });
    }

    if (allowedPeriods.includes('hour')) {
      result.push({
        value: 'hour',
        label: locale.hourOption || DEFAULT_LOCALE_EN.hourOption,
      });
    }

    if (allowedPeriods.includes('minute')) {
      result.push({
        value: 'minute',
        label: locale.minuteOption || DEFAULT_LOCALE_EN.minuteOption,
      });
    }

    if (allowedPeriods.includes('reboot') && shortcuts && (shortcuts === true || shortcuts.includes('@reboot'))) {
      result.push({
        value: 'reboot',
        label: locale.rebootOption || DEFAULT_LOCALE_EN.rebootOption,
      });
    }

    return result;
  }, [allowedPeriods, locale, shortcuts]);

  const handleChange = useCallback(
    (newValue: string | null) => {
      // Convert string to PeriodType if necessary
      if (newValue && !readOnly) {
        setValue(newValue as PeriodType); // Type-casting as PeriodType
      }
    },
    [setValue, readOnly],
  );

  return (
    <div>
      {locale.prefixPeriod !== '' && <span>{locale.prefixPeriod || DEFAULT_LOCALE_EN.prefixPeriod}</span>}
      <Select
        value={value}
        onChange={handleChange}
        data={options}
        disabled={disabled}
        readOnly={readOnly}
        clearable={allowClear}
        data-testid='select-period'
        searchable
      />
    </div>
  );
}
