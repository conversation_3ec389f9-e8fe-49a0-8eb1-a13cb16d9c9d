import React, { useMemo, useState } from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { Flex, SimpleGrid } from '@mantine/core';
import { CollectEmailConfigModel } from '@models/CollectEmailConfigModel';
import { SelectWithPage } from '@components/SelectWithPage';
import { DEFAULT_PAGINATION_REQUEST, DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME } from '@common/constants/PaginationRequestConstant';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { ServiceApi } from '@api/ServiceApi';
import { ApplicationApi } from '@api/ApplicationApi';
import { CollectEmailConfig } from '@core/schema/CollectEmailConfig';

interface Props {
  form: UseFormReturn<CollectEmailConfigModel>;
  isViewMode: boolean;
  oldData?: CollectEmailConfig;
}

const ServiceApplicationOutputSession = ({ form, isViewMode, oldData }: Props) => {
  const { control, setValue } = form;
  const serviceIdWatchForm = useWatch({ control, name: 'serviceId' });
  const serviceIds = serviceIdWatchForm === '' ? [] : [serviceIdWatchForm];
  const applicationId = useWatch({ control, name: 'applicationId' });
  //API
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME);
  const {
    fetchNextPage: fetchNextPageService,
    flatData: services,
    isFetching: isServiceFetching,
  } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
  });
  const {
    fetchNextPage: fetchNextPageApplication,
    flatData: applications,
    isFetching: isApplicationFetching,
  } = useInfiniteFetch(ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds }), {
    showLoading: false,
    enabled: serviceIds.length > 0,
  });
  const serviceComboxOptions = useMemo(() => {
    return services.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [services]);

  const applicationComboxOptions = useMemo(() => {
    return applications.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [applications]);
  return (
    <Flex direction='column' gap='sm'>
      <SimpleGrid cols={2}>
        <Controller
          name='serviceId'
          disabled={isViewMode}
          control={control}
          render={({ field }) => (
            <SelectWithPage
              label='Service Name'
              disabled={isViewMode}
              required={true}
              options={serviceComboxOptions}
              handleScrollToBottom={fetchNextPageService}
              onChange={(_, data) => {
                const valueId = data?.value?.toString() || '';
                setValue('applicationId', '');
                field.onChange(valueId);
              }}
              onSearch={(val) => {
                setServiceSearchParams((prev) => ({ ...prev, page: 0, name: val }));
              }}
              onBlur={() => {
                setApplicationSearchParams(DEFAULT_PAGINATION_REQUEST);
              }}
              value={
                [
                  ...serviceComboxOptions,
                  {
                    value: field.value,
                    label: oldData?.serviceName || '',
                  },
                ].filter((e) => e.value === field.value)[0] || undefined
              }
              isLoading={isServiceFetching}
            />
          )}
        />
        <Controller
          name='applicationId'
          control={control}
          render={({ field }) => {
            return (
              <SelectWithPage
                disabled={isViewMode || serviceIds.length === 0}
                label='Application Name'
                required={true}
                options={applicationComboxOptions}
                handleScrollToBottom={fetchNextPageApplication}
                onChange={(_, data) => {
                  const valueId = data?.value?.toString() || '';
                  setValue('applicationId', valueId);
                  field.onChange(valueId);
                }}
                onSearch={(val) => {
                  setApplicationSearchParams((prev) => ({ ...prev, page: 0, name: val }));
                }}
                onBlur={() => {
                  setApplicationSearchParams(DEFAULT_PAGINATION_REQUEST);
                }}
                value={
                  applicationId
                    ? [
                        ...applicationComboxOptions,
                        {
                          value: field.value,
                          label: oldData?.applicationName || '',
                        },
                      ].filter((e) => e.value === applicationId)[0] || undefined
                    : undefined
                }
                isLoading={isApplicationFetching}
              />
            );
          }}
        />
      </SimpleGrid>
    </Flex>
  );
};

export default ServiceApplicationOutputSession;
