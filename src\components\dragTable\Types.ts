import { SortType } from '@common/constants/SortType';
import { TableProps as MantineTableProps, TableTheadProps, TableTrProps, TableThProps, TableTbodyProps, TableTdProps } from '@mantine/core';
import { CSSProperties, ReactNode } from 'react';

export type Key = string | number;
export type FieldNamesByValueType<T extends object, ValueType> = {
  [K in keyof T]: T[K] extends ValueType ? (K extends symbol ? never : K) : never;
}[keyof T];

export type ColumnRender<T extends object> = FieldNamesByValueType<T, ReactNode> | ((record: T) => ReactNode);
export type DataKey<T extends object> = FieldNamesByValueType<T, Key> | ((record: T) => Key);
export type ColumnCustomTdProps<T extends object> = TableTdProps | ((record: T, index: number) => TableTdProps);
export type ColumnCustomTrProps<T extends object> = TableTrProps | ((record: T, index: number) => TableTrProps);

export interface ColumnCustomProps<T extends object> {
  th?: TableThProps;
  td?: TableTdProps | ((record: T) => TableTdProps);
}

export interface SortEffect {
  sortBy: string;
  direction: SortType | undefined;
  onChange: (sortBy: string, direction: SortType) => void;
}

export interface Column<T extends object> {
  render: ColumnRender<T>;
  id: string;
  title: ReactNode;
  customProps?: ColumnCustomProps<T>;
  textAlign?: CSSProperties['textAlign'];
  width?: CSSProperties['width'];
  sortable?: boolean;
}

export interface TableCustomProps<T extends object> {
  table?: MantineTableProps;
  thead?: TableTheadProps;
  theadTr?: TableTrProps;
  th?: TableThProps;
  tbody?: TableTbodyProps;
  tbodyTr?: ColumnCustomTrProps<T>;
  td?: ColumnCustomTdProps<T>;
  indexColumnTh?: TableThProps;
  indexColumnTd?: TableTdProps;
}

export interface BaseTableProps<T extends object> {
  columns: Column<T>[];
  data: T[];
  dataKey?: DataKey<T>;
  showIndexColumn?: boolean;
  customProps?: TableCustomProps<T>;
}

export type OnDragHandler<T extends object> = (dragItem: T, overItem: T, dragIndex: number, overIndex: number) => void;

export type DisableDraggable<T extends object> = boolean | ((record: T) => boolean);

export interface DragTableRowProps<T extends object> {
  columns: Column<T>[];
  record: T;
  showIndexColumn?: boolean;
  itemKey: string | number;
  customProps?: TableCustomProps<T>;
  index: number;
  disableDraggable: DisableDraggable<T>;
}

export interface DragTableProps<T extends object> extends Omit<BaseTableProps<T>, 'dataKey'> {
  dataKey: DataKey<T>;
  onDragHandler: OnDragHandler<T>;
  disableDraggable?: DisableDraggable<T>;
  staticIds?: (number | string)[];
  position?: 'top' | 'bottom';
}

export interface InfiniteScrollTableProps<T extends object> extends BaseTableProps<T> {
  onScrollToBottom: () => void;
  stickyHeader?: number;
  loading?: boolean;
  sortEffect?: SortEffect;
}

export interface TDataProps<T extends object> {
  column: Column<T>;
  record: T;
  customProps?: TableCustomProps<T>;
  index: number;
}

export interface THeadProps<T extends object> {
  column: Column<T>;
  customProps?: TableCustomProps<T>;
  sortEffect?: SortEffect;
}
