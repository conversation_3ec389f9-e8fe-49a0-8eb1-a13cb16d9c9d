import { HIDDEN_VARIABLE_PLACEHOLDER } from '@common/constants/ExecutionConstants';
import { VARIABLE_MAX_LENGTH, MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH } from '@common/constants/ValidationConstant';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { VariableModel } from '@models/VariableModel';
import { KanbanCheckbox, KanbanInput } from 'kanban-design-system';
import React from 'react';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';

interface Props {
  form: UseFormReturn<VariableModel>;
  readonly: boolean;
}

const ValueInput = ({ form, readonly }: Props) => {
  const { control } = form;
  const isHidden = useWatch({ control, name: 'hidden' });
  const isCreateMode = !useWatch({ control, name: 'id' });
  return (
    <Controller
      name='value'
      control={control}
      render={({ field }) => (
        <KanbanInput
          label='Value'
          {...field}
          maxLength={VARIABLE_MAX_LENGTH}
          description={getMaxLengthMessage(VARIABLE_MAX_LENGTH)}
          placeholder={isHidden ? HIDDEN_VARIABLE_PLACEHOLDER : ''}
          required={isCreateMode || !isHidden}
          disabled={readonly}
        />
      )}
    />
  );
};

const ExeuctionVariableForm = ({ form, readonly }: Props) => {
  const { control } = form;
  return (
    <>
      <Controller
        name='name'
        control={control}
        render={({ field }) => (
          <KanbanInput
            label='Name'
            required
            {...field}
            maxLength={MAX_CHARACTER_NAME_LENGTH}
            description={getMaxLengthMessage(MAX_CHARACTER_NAME_LENGTH)}
            disabled={readonly}
          />
        )}
      />
      <Controller
        name='description'
        control={control}
        render={({ field }) => (
          <KanbanInput
            label='Description'
            {...field}
            maxLength={MAX_DESCRIPTION_LENGTH}
            description={getMaxLengthMessage(MAX_DESCRIPTION_LENGTH)}
            disabled={readonly}
          />
        )}
      />
      <ValueInput form={form} readonly={readonly} />
      <Controller
        name='hidden'
        control={control}
        render={({ field }) => (
          <KanbanCheckbox label='Hidden' checked={field.value} onChange={field.onChange} labelPosition='left' disabled={readonly} />
        )}
      />
    </>
  );
};

export default ExeuctionVariableForm;
