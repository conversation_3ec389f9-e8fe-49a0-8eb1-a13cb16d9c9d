import React, { useState, useMemo, useRef, useCallback } from 'react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanText,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { IconEdit } from '@tabler/icons-react';
import { SortType } from '@common/constants/SortType';
import { CustomObject } from '@core/schema/CustomObject';
import { PaginationRequest } from '@api/Type';
import CreateOrUpdateModal from './CreateOrUpdateModal';
import { CustomObjectTypeEnum } from '@common/constants/CustomObjectTypeConstant';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import DeleteModal from './DeleteModal';

const CustomRenderValueComponent: React.FC<CustomObject> = (rowData) => {
  const contentMap: Record<CustomObjectTypeEnum, JSX.Element> = {
    [CustomObjectTypeEnum.REGEX]: (
      <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
        Regex: {rowData.regex}
      </KanbanText>
    ),
    [CustomObjectTypeEnum.INDEX_TO_INDEX]: (
      <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
        From index: {rowData.fromIndex} To index: {rowData.toIndex}
      </KanbanText>
    ),
    [CustomObjectTypeEnum.KEYWORD_TO_KEYWORD]: (
      <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
        From keyword: {rowData.fromKeyword} To keyword: {rowData.toKeyword}
      </KanbanText>
    ),
  };

  return contentMap[rowData.type];
};

const columns: ColumnType<CustomObject>[] = [
  {
    title: 'Name',
    name: 'name',
  },
  {
    title: 'Description',
    name: 'description',
  },
  {
    title: 'Type',
    name: 'type',
  },
  {
    title: 'Value',
    sortable: false,
    name: 'value',
    customRender: (_data, rowData) => {
      return <CustomRenderValueComponent {...rowData} />;
    },
  },
];
export const CustomObjectPage = () => {
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const [openedModalCreateCustomObject, { close: closeModalCreateCustomObject, open: openModalCreateCustomObject }] = useDisclosure(false);
  const [rowSelected, setRowSelected] = useState<CustomObject | undefined>(undefined);
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  // API
  const { data: listCustomObject, refetch: refetchList } = useFetch(CustomObjectApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });

  //Function update table affected
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<CustomObject>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const tableViewListRolesProps: KanbanTableProps<CustomObject> = useMemo(() => {
    return {
      columns: columns,
      data: listCustomObject?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listCustomObject?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.customObjectEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setRowSelected(data);
                      openModalCreateCustomObject();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.customObjectDelete]}>
                <KanbanTooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      openModalDel();
                      setRowSelected(data);
                    }}>
                    <IconTrash color='red' />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    handleUpdateTablePagination,
    listCustomObject?.data?.content,
    listCustomObject?.data?.totalElements,
    openModalCreateCustomObject,
    openModalDel,
    tableAffected,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of custom object'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.customObjectCreate]}>
            <Flex direction='row' gap='xs' align='center'>
              <KanbanButton
                size='xs'
                onClick={() => {
                  setRowSelected(undefined);
                  openModalCreateCustomObject();
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} maxHeight={1000} />
      <CreateOrUpdateModal
        opened={openedModalCreateCustomObject}
        onClose={closeModalCreateCustomObject}
        customObject={rowSelected}
        refetchList={refetchList}
      />
      <DeleteModal
        customObjectName={rowSelected?.name}
        opened={openedModalDel}
        onClose={closePopupDel}
        customObjectId={rowSelected?.id}
        refetchList={refetchList}
      />
    </Box>
  );
};
export default CustomObjectPage;
