import type { AclPermission } from 'models/AclPermission';
import { ProtectedRoute } from '@core/auth/hocs/ProtectedRoute';
import ForbiddenPage from '@pages/base/ForbiddenPage';
import React from 'react';

export type GuardRouteType = {
  requirePermissions: AclPermission[];
  children: React.ReactNode;
};

export const GuardRoute = (props: GuardRouteType) => {
  return (
    <>
      <ProtectedRoute errorElement={<ForbiddenPage />} requirePermissions={props.requirePermissions}>
        {props.children}
      </ProtectedRoute>
    </>
  );
};

export default GuardRoute;
