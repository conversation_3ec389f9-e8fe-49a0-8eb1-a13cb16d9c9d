import { BaseURL } from '@common/constants/BaseUrl';
import { createResponseSchema, ResponseData } from '@core/schema';
import { RoleSchema } from '@core/schema/Role';
import { RoleModel } from '@models/RoleModel';
import { createRequest } from './Utils';
import { z } from 'zod';

export class RoleApi {
  static findAll() {
    return createRequest({
      url: `${BaseURL.role}`,
      method: 'GET',
      schema: createResponseSchema(z.array(RoleSchema)),
    });
  }

  static findRoleWithPermissionById(id: number) {
    return createRequest({
      url: `${BaseURL.role}/:id/with-permissions`,
      method: 'GET',
      schema: createResponseSchema(RoleSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(role: RoleModel) {
    return createRequest({
      url: `${BaseURL.role}`,
      method: 'POST',
      schema: createResponseSchema(RoleSchema),
      data: role,
    });
  }

  static deleteBatch(ids: number[]) {
    return createRequest<ResponseData<string>>({
      url: `${BaseURL.role}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    });
  }

  static deleteById(id: number) {
    return createRequest<ResponseData<string>>({
      url: `${BaseURL.role}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    });
  }

  static activeById(id: number) {
    return createRequest<ResponseData<string>>({
      url: `${BaseURL.role}/:id/status`,
      method: 'PUT',
      params: { active: true },
      pathVariable: {
        id,
      },
    });
  }

  static inactiveById(id: number) {
    return createRequest<ResponseData<string>>({
      url: `${BaseURL.role}/:id/status`,
      method: 'PUT',
      params: { active: false },
      pathVariable: {
        id,
      },
    });
  }
  static deleteRoleFromUser({ roleId, userId }: { roleId: number; userId: number }) {
    return createRequest<ResponseData<string>>({
      url: `${BaseURL.role}/:roleId/users/:userId`,
      method: 'DELETE',
      pathVariable: {
        roleId,
        userId,
      },
    });
  }
  static addRolesToUser(body: { userId: number; roleIds: number[] }) {
    return createRequest({
      url: `${BaseURL.role}/add-user-roles`,
      method: 'PUT',
      data: body,
    });
  }
}
