import { DATE_FORMAT } from '@common/constants/DateConstants';
import { TaskStatusEnum } from '@common/constants/TaskConstants';
import { FilterTaskModel } from '@models/FilterTaskModel';
import dayjs from 'dayjs';
import { CalendarMode } from './Types';

export enum ViewModeEnum {
  MONTH = 'MONTH',
  YEAR = 'YEAR',
}

export const DEFAULT_TASK_PAGE_SIZE = 30;

export const DEFAULT_FILTER_VALUE: FilterTaskModel = {
  assigneeUsers: [],
  creatorUsers: [],
  search: '',
  statuses: [TaskStatusEnum.INPROGRESS, TaskStatusEnum.NEW],
  types: [],
  dateRanges: [
    {
      fromDate: dayjs().startOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
      toDate: dayjs().endOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
    },
  ],
};

export const DEFAULT_CALENDAR_MODE: CalendarMode = {
  day: dayjs(),
  viewMode: ViewModeEnum.MONTH,
  selectedDates: [],
};
