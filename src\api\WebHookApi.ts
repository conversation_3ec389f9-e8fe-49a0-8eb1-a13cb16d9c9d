import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { createPageSchema, createResponseSchema, Page, ResponseData } from '@core/schema';
import { WebHook, WebHookSchema } from '@core/schema/WebHook';
import { WebHookModel } from '@models/WebHookModel';
import { PaginationRequest } from './Type';

export class WebHookApi {
  static findAll(pagination?: PaginationRequest): RequestConfig<ResponseData<Page<WebHook>>> {
    return {
      url: BaseURL.webHook,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(WebHookSchema)),
      params: pagination,
    };
  }

  static findById(id: number): RequestConfig<ResponseData<WebHook>> {
    return {
      url: `${BaseURL.webHook}/:id`,
      method: 'GET',
      schema: createResponseSchema(WebHookSchema),
      pathVariable: {
        id,
      },
    };
  }
  static deleteByIdIn(ids: number[]): RequestConfig<void> {
    return {
      url: `${BaseURL.webHook}/batch`,
      method: 'DELETE',
      params: {
        ids,
      },
    };
  }

  static deleteById(id: number): RequestConfig<void> {
    return {
      url: `${BaseURL.webHook}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    };
  }

  static save(data: WebHookModel): RequestConfig<ResponseData<WebHook>> {
    return {
      url: BaseURL.webHook,
      method: 'POST',
      schema: createResponseSchema(WebHookSchema),
      data,
    };
  }

  static refreshToken(id: number): RequestConfig<void> {
    return {
      url: `${BaseURL.webHook}/:id/refresh`,
      method: 'PUT',
      pathVariable: {
        id,
      },
    };
  }
}
