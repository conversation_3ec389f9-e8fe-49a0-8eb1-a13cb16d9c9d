import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { Text } from '@mantine/core';
import React from 'react';
import { Link } from 'react-router-dom';

const MessageExportSuccess = () => {
  return (
    <>
      <Text fw='bold'>Queued report for search</Text>
      <Text size='sm' c='gray'>
        Track its progress in <Link to={ROUTE_PATH.EXPORT_DATA}>Export Data</Link>
      </Text>
    </>
  );
};

export default MessageExportSuccess;
