import { NotificationData, notifications } from '@mantine/notifications';

const DEFAULT_NOTIFICATION_PROPS: Omit<NotificationData, 'message'> = {
  radius: 'md',
  withBorder: true,
  withCloseButton: true,
};

export const NotificationSuccess = (data: NotificationData) => {
  notifications.show({
    color: 'primary',
    bg: 'primary.1',
    title: 'Success',
    ...DEFAULT_NOTIFICATION_PROPS,
    ...data,
  });
};
export const NotificationError = (data: NotificationData) => {
  notifications.show({
    color: 'red',
    bg: 'red.1',
    title: 'Error',
    ...DEFAULT_NOTIFICATION_PROPS,
    ...data,
  });
};
export const NotificationWarning = (data: NotificationData) => {
  notifications.show({
    color: 'yellow',
    bg: 'yellow.1',
    title: 'Warning',
    ...DEFAULT_NOTIFICATION_PROPS,
    ...data,
  });
};
