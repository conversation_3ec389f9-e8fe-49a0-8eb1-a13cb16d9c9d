import React, { useState, useMemo, useEffect, useRef } from 'react';
import { KanbanButton, KanbanInput, KanbanSelect } from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { IconSearch, IconSend } from '@tabler/icons-react';

import EmailPartnerSelection from './Partner';
import EmailComposingPage from './EmailComposing';
import { EmailTemplateApi } from '@api/EmailTemplateApi';
import { EmailTemplate } from '@core/schema/EmailTemplate';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { EmailPartnerApi } from '@api/EmailPartnerApi';
import { EmailComposedModel, EmailComposedModelSchema } from '@models/EmailComposedModel';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import EmailComposedViewPage from './EmailComposedView';
import { SortType } from '@common/constants/SortType';
import { DEFAULT_PAGINATION_REQUEST, DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME, LIMIT_SIZE_LENGTH } from '@common/constants/PaginationRequestConstant';
import { EmailConfigApi } from '@api/EmailConfigApi';
import { EmailProtocolTypeEnum } from '@common/constants/EmailProtocolTypeConstant';
import { getConfigs } from '@core/configs/Configs';
import { NotificationError } from '@common/utils/NotificationUtils';
import { EmailPartner } from '@core/schema/EmailPartner';

const DEFAULT_EMAIL_COMPOSED: EmailComposedModel = {
  content: '',
  fileStorages: [],
  to: [],
  cc: [],
  subject: '',
  isOneEmail: false,
  emailSender: '',
};
export const SendEmailViewPage = () => {
  const [selectedPartners, setSelectedPartners] = useState<Record<number, Record<string, boolean>>>({});
  const [isViewMode, setIsViewMode] = useState(false);
  const [templateId, setTemplateId] = useState<number>(0);
  const maxEmailTotalSize = getConfigs().maxEmailAttachSize;
  const [searchPartner, setSearchPartner] = useState('');
  const [partners, setPartners] = useState<EmailPartner[]>([]);

  const form = useForm<EmailComposedModel>({
    defaultValues: DEFAULT_EMAIL_COMPOSED,
    resolver: zodResolver(EmailComposedModelSchema),
    mode: 'onChange',
  });

  const {
    control,
    formState: { isValid },
    getValues,
    reset,
    setValue,
    trigger,
  } = form;

  const { data: listEmailPartnerData } = useFetch(
    EmailPartnerApi.findAll({
      ...DEFAULT_PAGINATION_REQUEST,
      sortBy: 'name',
      sortOrder: SortType.ASC,
      size: LIMIT_SIZE_LENGTH,
    }),
    {
      placeholderData: (prev) => prev,
    },
  );
  useEffect(() => {
    if (searchPartner) {
      setPartners(
        listEmailPartnerData?.data?.content.filter(
          (e: EmailPartner) =>
            e.name.toLowerCase().includes(searchPartner.toLowerCase()) ||
            e.addresses.some((address: string) => address.includes(searchPartner.toLowerCase())),
        ) || [],
      );
    } else {
      setPartners(listEmailPartnerData?.data?.content || []);
    }
  }, [listEmailPartnerData?.data?.content, searchPartner]);
  const { data: emailConfigs } = useFetch(
    EmailConfigApi.findAll({
      ...DEFAULT_PAGINATION_REQUEST,
      sortBy: 'email',
      sortOrder: SortType.ASC,
      size: LIMIT_SIZE_LENGTH,
      withInactived: false,
    }),
    {
      placeholderData: (prev) => prev,
    },
  );

  const { data: emailTemplates } = useFetch(EmailTemplateApi.findAll({ ...DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME, size: LIMIT_SIZE_LENGTH }), {
    placeholderData: (prev) => prev,
  });

  const emailTemplatesCombobox = useMemo(() => {
    return emailTemplates?.data?.content?.map((emailTemplate: EmailTemplate) => ({ value: `${emailTemplate.id}`, label: emailTemplate.name })) || [];
  }, [emailTemplates?.data]);

  const emailConfigsCombobox = useMemo(() => {
    return emailConfigs?.data?.content
      ?.filter((e) => e.protocolType === EmailProtocolTypeEnum.SMTP)
      .map((obj) => ({ value: `${obj.email}`, label: obj.email }));
  }, [emailConfigs?.data]);

  const { data: templateData } = useFetch(EmailTemplateApi.findById(templateId), {
    placeholderData: (prev) => prev,
    enabled: !!templateId,
  });
  const isInitialMount = useRef(true);

  useEffect(() => {
    const emailSender = getValues('emailSender');
    const isOneEmail = getValues('isOneEmail');
    const values =
      templateId && templateData
        ? {
            ...DEFAULT_EMAIL_COMPOSED,
            content: templateData?.data?.content || '',
            fileStorages: templateData?.data?.fileStorages || [],
            to: templateData?.data?.to || [],
            cc: templateData?.data?.cc || [],
            subject: templateData?.data?.subject || '',
            emailSender,
            isOneEmail,
          }
        : {
            ...DEFAULT_EMAIL_COMPOSED,
            isOneEmail,
            emailSender,
          };
    Object.entries(values).forEach(([key, value]) => {
      setValue(key as keyof EmailComposedModel, value);
    });
    if (!isInitialMount.current) {
      trigger();
    } else {
      isInitialMount.current = false;
    }
  }, [templateId, templateData, setValue, getValues, trigger]);

  useEffect(() => {
    const emailPartnerSelected = listEmailPartnerData?.data?.content
      ?.map((partner) => {
        const selectedAddresses = selectedPartners[partner.id];
        if (!selectedAddresses) {
          return null;
        }
        const addressesWithTrue = partner.addresses.filter((address) => selectedAddresses[address]);
        return addressesWithTrue.length > 0 ? { ...partner, addresses: addressesWithTrue } : null;
      })
      .filter((partner): partner is { id: number; name: string; addresses: string[] } => partner !== null);
    setValue('partners', emailPartnerSelected);
  }, [selectedPartners, listEmailPartnerData, setValue]);

  const handlePartnerCheck = (partnerId: number) => {
    const partnerAddresses = listEmailPartnerData?.data?.content.find((partner) => partner.id === partnerId)?.addresses || [];
    const allSelected = allAddressesChecked(partnerId, partnerAddresses);
    setSelectedPartners((prev) => ({
      ...prev,
      [partnerId]: partnerAddresses.reduce(
        (acc, address) => {
          acc[address] = !allSelected;
          return acc;
        },
        {} as Record<string, boolean>,
      ),
    }));
  };

  const handleAddressCheck = (partnerId: number, address: string) => {
    setSelectedPartners((prev) => ({
      ...prev,
      [partnerId]: {
        ...prev[partnerId],
        [address]: !prev[partnerId]?.[address],
      },
    }));
  };

  const allAddressesChecked = (partnerId: number, addresses: string[]): boolean =>
    addresses.every((address) => selectedPartners[partnerId]?.[address]);

  const selectedCount = (partnerId: number, addresses: string[]): number =>
    addresses.filter((address) => selectedPartners[partnerId]?.[address]).length;

  const handleBack = (isReset: boolean): void => {
    setIsViewMode(false);
    if (isReset) {
      reset();
      setSearchPartner('');
      setSelectedPartners({});
      setTemplateId(0);
      isInitialMount.current = true;
    }
  };

  const isEmailExceedingSizeLimit = (): boolean => {
    const contentSize = new Blob([getValues('content')]).size;
    const filesSize = getValues('files')?.reduce((total, file) => total + file.size, 0) || 0;
    const fileStoragesSize = getValues('fileStorages')?.reduce((total, file) => total + file.size, 0) || 0;
    const totalSizeMB = contentSize + filesSize + fileStoragesSize;
    return totalSizeMB > maxEmailTotalSize;
  };

  return (
    <>
      {!isViewMode && (
        <Flex direction={'row'} h={'var(--kanban-appshell-maxheight-content)'}>
          <Box bg='white' mr='xs' p='sm' w='250px' style={{ overflowY: 'auto' }}>
            <HeaderTitleComponent
              title='Partner'
              rightSection={
                <Flex direction='column' align='flex-end'>
                  <KanbanInput
                    mt='xs'
                    maw={'7rem'}
                    leftSection={<IconSearch size={16} />}
                    value={searchPartner}
                    onChange={(event) => setSearchPartner(event.target.value)}
                    radius='xs'
                    size='xs'
                  />
                </Flex>
              }
            />

            <EmailPartnerSelection
              listEmailPartner={partners}
              selectedPartners={selectedPartners}
              handlePartnerCheck={handlePartnerCheck}
              handleAddressCheck={handleAddressCheck}
              allAddressesChecked={allAddressesChecked}
              selectedCount={selectedCount}
            />
          </Box>
          <Box style={{ flex: 1, overflowY: 'auto', minHeight: 800 }} p='sm' bg='white'>
            <HeaderTitleComponent
              title={'Compose Email'}
              rightSection={
                <Flex direction='row' gap='xs' align='center'>
                  <Controller
                    name='emailSender'
                    control={control}
                    render={({ field }) => {
                      return (
                        <KanbanSelect
                          allowDeselect={false}
                          mt={'xs'}
                          size='xs'
                          searchable
                          placeholder='Email'
                          data={emailConfigsCombobox}
                          {...field}
                        />
                      );
                    }}
                  />
                  <KanbanSelect
                    mt={'xs'}
                    size='xs'
                    searchable
                    placeholder='Email Template'
                    value={String(templateId)}
                    data={emailTemplatesCombobox}
                    allowDeselect={false}
                    clearable={templateId !== 0}
                    onChange={(val) => setTemplateId(Number(val || 0))}
                  />
                  <KanbanButton
                    disabled={!isValid}
                    onClick={() => {
                      if (isEmailExceedingSizeLimit()) {
                        NotificationError({
                          message: `The total size of the email content and attachments exceeds ${maxEmailTotalSize / (1024 * 1024)}.`,
                        });
                        return;
                      }
                      setIsViewMode(true);
                    }}
                    size='xs'
                    leftSection={<IconSend />}>
                    Next
                  </KanbanButton>
                </Flex>
              }
            />
            <EmailComposingPage form={form} />
          </Box>
        </Flex>
      )}
      {isViewMode && <EmailComposedViewPage handleBack={handleBack} emailComposed={getValues()} />}
    </>
  );
};
export default SendEmailViewPage;
