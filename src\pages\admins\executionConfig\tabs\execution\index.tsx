import { Execution<PERSON>pi } from '@api/ExecutionApi';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { SortType } from '@common/constants/SortType';
import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { DEFAULT_DEBOUNCE_TIME } from '@components/InfiniteScrollAccordion';
import Table from '@components/table';
import useFetch from '@core/hooks/useFetch';
import { Execution } from '@core/schema/Execution';
import { Box, Flex } from '@mantine/core';
import { PaginationModel } from '@models/PaginationModel';
import { IconTrash } from '@tabler/icons-react';
import { ColumnType, KanbanIconButton, KanbanTableProps, KanbanText, KanbanTooltip, TableAffactedSafeType } from 'kanban-design-system';
import React, { useCallback, useMemo, useState } from 'react';
import CreateExecutionButton from './CreateExecutionButton';
import UpdateExecutionButton from './UpdateExecutionButton';
import { ExecutionTypeLabel } from '@common/constants/ExecutionConstants';
import useMutate from '@core/hooks/useMutate';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { AclPermission } from '@models/AclPermission';

export const COLUMNS: ColumnType<Execution>[] = [
  {
    title: 'Name',
    name: 'name',
    width: '20%',
  },
  {
    title: 'Description',
    name: 'description',
    width: '20%',
  },
  {
    title: 'Type',
    name: 'type',
    customRender: (_, data) => <KanbanText fw={500}>{ExecutionTypeLabel[data.type]}</KanbanText>,
    width: '20%',
  },
  {
    title: 'Group',
    name: 'executionGroupName',
    width: '20%',
  },
];

const ExecutionConfig = () => {
  const [tableAffected, setTableAffected] = useState<PaginationModel>(DEFAULT_PAGINATION_REQUEST);
  const { data: executionData, refetch } = useFetch(ExecutionApi.findAllWithPaging(tableAffected), { placeholderData: (prev) => prev });
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<Execution>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const { mutate: deleteMutate } = useMutate(ExecutionApi.deleteById, {
    successNotification: 'Delete Execution successfully.',
    onSuccess: () => refetch(),
    confirm: { enable: true, title: '' },
  });
  const tableViewListRolesProps: KanbanTableProps<Execution> = useMemo(() => {
    return {
      columns: COLUMNS,
      data: executionData?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: executionData?.data?.totalElements ?? 0,
        onTableAffected: handleUpdateTablePagination,
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.executionEdit, AclPermission.executionView]}>
                <UpdateExecutionButton executionId={data.id} onUpdateSuccess={refetch} />
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.executionDelete]}>
                <KanbanTooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => deleteMutate(data.id, { confirm: getDefaultDeleteConfirmMessage(data.name) })}>
                    <IconTrash color='red' />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [deleteMutate, executionData?.data?.content, executionData?.data?.totalElements, handleUpdateTablePagination, refetch]);

  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of execution'
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <GuardComponent requirePermissions={[AclPermission.executionCreate]}>
              <CreateExecutionButton onCreateSuccess={refetch} />
            </GuardComponent>
          </Flex>
        }
      />
      <Table {...tableViewListRolesProps} />
    </Box>
  );
};

export default ExecutionConfig;
