import React, { useEffect, useMemo, useState } from 'react';
import {
  KanbanButton,
  KanbanInput,
  KanbanTooltip,
  KanbanIconButton,
  KanbanTitle,
  KanbanSelect,
  KanbanTextarea,
  ColumnType,
} from 'kanban-design-system';
import { useF<PERSON>, zodResolver } from '@mantine/form';
import useMutate from '@core/hooks/useMutate';

import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { DatabaseConnectionApi } from '@api/DatabaseConnectionApi';
import { IconEdit, IconEye, IconPlus } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Card, ComboboxItem, Flex, SimpleGrid } from '@mantine/core';
import {
  DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH,
  DATABASE_COLLECT_CONTACT_CUSTOM_MAX_LENGTH,
  DESCRIPTION_MAX_LENGTH,
  MAX_RECIPIENT_LENGTH,
  NAME_MAX_LENGTH,
} from '@common/constants/ValidationConstant';
import useFetch from '@core/hooks/useFetch';
import { WEBHOOK_FIELD_TYPE } from '@common/constants/WebHookConstant';
import { PaginationRequest } from '@api/Type';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationContant';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { ServiceApi } from '@api/ServiceApi';
import { DatabaseCollectModel, DatabaseCollectModelSchema } from '@models/DatabaseCollectModel';
import { ApplicationApi } from '@api/ApplicationApi';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import { sortBy } from 'lodash';
import { DatabaseCollectApi } from '@api/DatabaseCollectApi';
import Table from '@components/table';
import { SelectWithPage } from '@components/SelectWithPage';
import classes from './DatabaseCollectModal.module.scss';
import Modal from '@components/Modal';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';

type DatabaseCollectModalProps = {
  refetchList: () => void;
  id?: number;
};

const DEFAULT_PAGINATION_CUSTOM_REQUEST: PaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortBy: 'name',
};

const INTERVAL_VALUE = ['10', '20', '30', '60', '300', '600'];
const MESSAGE_TYPE = 'Choose between get data from the database or custom input.';

const dataInitForm: DatabaseCollectModel = {
  name: '',
  connectionId: 0,
  sqlCommand: '',
  createdDateField: '',
  alertIdField: '',
  interval: 30,
  serviceNameType: WEBHOOK_FIELD_TYPE.CUSTOM,
  applicationType: WEBHOOK_FIELD_TYPE.CUSTOM,
  priorityType: WEBHOOK_FIELD_TYPE.CUSTOM,
  contactType: WEBHOOK_FIELD_TYPE.CUSTOM,
  isActive: true,
  alertMapValue: '',
  contactCustomValue: undefined,
  contactMapValue: undefined,
  priorityId: undefined,
  description: undefined,
};
const DatabaseCollectModal: React.FC<DatabaseCollectModalProps> = ({ id, refetchList }) => {
  const [tableQueryKey, setTableQueryKey] = useState<number>(0);
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_PAGINATION_CUSTOM_REQUEST);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_PAGINATION_CUSTOM_REQUEST);
  const isUpdateMode = !!id;
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [comboboxConnections, setComboboxConnections] = useState<ComboboxItem[]>([]);
  const [isViewMode, setIsViewMode] = useState<boolean>(false);

  const [serviceSelected, setServiceSelected] = useState<ComboboxItem | undefined>(undefined);
  const [applicationSelected, setApplicationSelected] = useState<ComboboxItem | undefined>(undefined);
  const {
    data: collectDetail,
    isFetching,
    refetch: refetchData,
  } = useFetch(DatabaseCollectApi.findById(id || 0), {
    enabled: !!id && openedModal,
  });

  const { getInputProps, isValid, setFieldValue, setValues, values } = useForm({
    initialValues: dataInitForm,
    validate: zodResolver(DatabaseCollectModelSchema),
    validateInputOnChange: true,
  });

  useEffect(() => {
    if (collectDetail?.data && !isFetching) {
      setValues(collectDetail?.data);
    }
  }, [isFetching, collectDetail?.data, setValues]);

  const {
    fetchNextPage: fetchNextPageService,
    flatData: services,
    isFetching: isServiceFetching,
  } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
    enabled: openedModal,
  });

  const {
    fetchNextPage: fetchNextPageApplication,
    flatData: applications,
    isFetching: isApplicationFetching,
  } = useInfiniteFetch(ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds: [values.serviceId || '0'] }), {
    showLoading: false,
    enabled: !!values.serviceId && openedModal,
  });

  const serviceComboxOptions = useMemo(() => {
    return services.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [services]);

  const applicationComboxOptions = useMemo(() => {
    return applications.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [applications]);

  const { data: lstDataConnection } = useFetch(DatabaseConnectionApi.findAll(), { enabled: openedModal });

  const comboboxFieldType: ComboboxItem[] = useMemo(() => {
    return Object.keys(WEBHOOK_FIELD_TYPE).map((key) => ({
      value: key,
      label: key,
    }));
  }, []);
  const { data: priorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), {
    showLoading: false,
    enabled: openedModal,
  });

  useEffect(() => {
    if (collectDetail?.data) {
      setServiceSelected({ value: `${collectDetail.data.serviceId}`, label: collectDetail.data.serviceName || '' });
      setApplicationSelected({ value: `${collectDetail.data.applicationId}`, label: collectDetail.data.applicationName || '' });
    }
  }, [collectDetail]);

  useEffect(() => {
    const comboboxDatas: ComboboxItem[] =
      lstDataConnection?.data
        ?.filter((item) => item.isActive) // Filter out items where isActive is false
        .sort((a, b) => a.name.localeCompare(b.name)) // Sort by name
        .map((item) => ({
          value: `${item.id}`,
          label: item.name,
        })) || [];

    setComboboxConnections(comboboxDatas);
  }, [lstDataConnection]);

  const priorityConfigOptions = useMemo(() => {
    return sortBy(
      priorityConfigs?.data?.map((config) => ({ value: `${config.id}`, label: config.name + (config.deleted ? ' (DELETED)' : '') })) || [],
      (option) => option.label.toLowerCase(),
    );
  }, [priorityConfigs?.data]);

  const { mutate: saveMutate } = useMutate(DatabaseCollectApi.save, {
    successNotification: isUpdateMode ? `Update database connection successfully` : 'Create database connection successfully',
    onSuccess: () => {
      closeModal();
      refetchList();
    },
  });

  const { data: dataSqlSample, mutate: getSampleDataMutate } = useMutate(DatabaseCollectApi.getSampleData, {
    successNotification: { enable: false },
  });

  const columns: ColumnType<Record<string, any>>[] = useMemo(() => {
    if (!dataSqlSample?.data?.listColumns || !dataSqlSample?.data?.listColumns?.length) {
      return [];
    }
    const result: ColumnType<Record<string, any>>[] = dataSqlSample?.data?.listColumns.map((x) => {
      return {
        name: x,
        title: x,
      };
    });
    return result;
  }, [dataSqlSample?.data?.listColumns]);

  const listDataQuery: Record<string, any>[] = useMemo(() => {
    setTableQueryKey(Math.random());
    if (!dataSqlSample?.data?.listColumns || !dataSqlSample?.data?.listDataMappings) {
      return [];
    }

    const result: Record<string, any>[] = [];
    for (const item of dataSqlSample.data.listDataMappings) {
      const currentItem: Record<string, any> = {};

      for (const currentRow of item.listSqlMappingColumnDatas) {
        if (currentRow) {
          currentItem[currentRow.column] = (currentRow?.value?.length || 0) > 120 ? `${currentRow?.value?.substring(0, 120)}...` : currentRow.value;
        }
      }
      result.push(currentItem);
    }

    return result;
  }, [dataSqlSample?.data?.listColumns, dataSqlSample?.data?.listDataMappings]);

  const handleSave = () => {
    if (isValid()) {
      saveMutate(DatabaseCollectModelSchema.parse(values));
    }
  };

  const resetFrom = () => {
    setTableQueryKey(0);
    setValues(dataInitForm);
    setApplicationSearchParams(DEFAULT_PAGINATION_CUSTOM_REQUEST);
    setServiceSearchParams(DEFAULT_PAGINATION_CUSTOM_REQUEST);
  };
  return (
    <>
      <GuardComponent requirePermissions={[AclPermission.databaseCollectView]}>
        {!!id && (
          <KanbanTooltip label='View'>
            <KanbanIconButton
              variant='transparent'
              size={'sm'}
              onClick={() => {
                resetFrom();
                setIsViewMode(true);
                openModal();
              }}>
              <IconEye />
            </KanbanIconButton>
          </KanbanTooltip>
        )}
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.databaseCollectEdit]}>
        {!!id && (
          <KanbanTooltip label='Edit'>
            <KanbanIconButton
              variant='transparent'
              size={'sm'}
              onClick={() => {
                setIsViewMode(false);
                resetFrom();
                refetchData();
                openModal();
              }}>
              <IconEdit />
            </KanbanIconButton>
          </KanbanTooltip>
        )}
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.databaseCollectCreate]}>
        {!id && (
          <HeaderTitleComponent
            title='Database Collect'
            rightSection={
              <Flex direction='row' gap='xs' align='center'>
                <KanbanButton
                  size='xs'
                  onClick={() => {
                    setIsViewMode(false);
                    resetFrom();
                    setServiceSelected(undefined);
                    setApplicationSelected(undefined);
                    openModal();
                  }}
                  leftSection={<IconPlus />}>
                  Create New
                </KanbanButton>
              </Flex>
            }
          />
        )}
      </GuardComponent>

      <Modal
        size={'70%'}
        opened={openedModal}
        onClose={() => {
          setTableQueryKey(0);
          closeModal();
        }}
        title={
          isViewMode
            ? `View config database collect ${collectDetail?.data?.name}`
            : isUpdateMode
              ? `Update config database collect ${collectDetail?.data?.name}`
              : 'Create new'
        }
        actions={
          !isViewMode ? (
            <KanbanButton onClick={handleSave} disabled={!isValid()}>
              Save
            </KanbanButton>
          ) : null
        }>
        <form className={classes['from']}>
          <Card shadow='sm' radius='md' m={5} withBorder>
            <KanbanTitle pb={10} order={4}>
              General infomation
            </KanbanTitle>
            <KanbanInput
              disabled={isViewMode}
              required
              label='Config Name'
              description={getMaxLengthMessage(NAME_MAX_LENGTH)}
              placeholder='Config Name'
              {...getInputProps('name')}
              maxLength={NAME_MAX_LENGTH}
            />
            <KanbanTextarea
              disabled={isViewMode}
              label='Description'
              description={getMaxLengthMessage(DESCRIPTION_MAX_LENGTH)}
              placeholder='Description'
              {...getInputProps('description')}
              maxLength={DESCRIPTION_MAX_LENGTH}
            />
          </Card>

          <Card shadow='sm' m={5} mt={10} radius='md' withBorder>
            <KanbanTitle pb={10} order={4}>
              Connection
            </KanbanTitle>
            <KanbanSelect
              disabled={isViewMode}
              allowDeselect={false}
              label='Connection'
              required
              searchable
              value={`${values.connectionId}`}
              data={comboboxConnections}
              onChange={(value) => {
                if (value) {
                  setValues((prev) => ({ ...prev, connectionId: Number(value) }));
                }
              }}></KanbanSelect>
            <KanbanTextarea disabled={isViewMode} rows={10} label='SQL Command' required {...getInputProps('sqlCommand')}></KanbanTextarea>
            {columns.length !== 0 && tableQueryKey !== 0 && (
              <Table key={tableQueryKey} showNumericalOrderColumn columns={columns} data={listDataQuery} />
            )}

            <KanbanButton
              disabled={!(values.connectionId && values.sqlCommand && values.createdDateField)}
              w={'30%'}
              onClick={() =>
                getSampleDataMutate({ connectionId: values.connectionId, sqlCommand: values.sqlCommand, createdDateField: values.createdDateField })
              }>
              Get Sample Data
            </KanbanButton>
            <KanbanInput
              required
              disabled={isViewMode}
              label='Create Date Field'
              description={getMaxLengthMessage(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)}
              placeholder='Create Date Field'
              {...getInputProps('createdDateField')}
              maxLength={DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH}
            />
            <KanbanInput
              required
              disabled={isViewMode}
              label='Alert Id Field'
              description={getMaxLengthMessage(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)}
              placeholder='Alert Id Field'
              {...getInputProps('alertIdField')}
              maxLength={DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH}
            />
            <KanbanSelect
              required
              disabled={isViewMode}
              label='Interval'
              data={INTERVAL_VALUE}
              value={`${values.interval}`}
              onChange={(value) => {
                setValues((prev) => ({ ...prev, interval: Number(value || 0) }));
              }}
            />
          </Card>

          <Card shadow='sm' m={5} radius='md' mt={10} withBorder>
            <KanbanTitle pb={10} order={4}>
              Config Input Collect
            </KanbanTitle>
            <SimpleGrid cols={2}>
              <KanbanSelect
                description={MESSAGE_TYPE}
                disabled={isViewMode}
                required
                allowDeselect={false}
                data={comboboxFieldType}
                label='Service Name Type:'
                value={values.serviceNameType}
                onChange={(value) => {
                  if (value) {
                    const data: WEBHOOK_FIELD_TYPE = value === WEBHOOK_FIELD_TYPE.CUSTOM ? WEBHOOK_FIELD_TYPE.CUSTOM : WEBHOOK_FIELD_TYPE.FROM_SOURCE;
                    setFieldValue('serviceNameType', data);
                    setFieldValue('applicationType', data);
                    if (WEBHOOK_FIELD_TYPE.FROM_SOURCE === data) {
                      setServiceSelected(undefined);
                      setApplicationSelected(undefined);
                      setFieldValue('serviceId', undefined);
                      setFieldValue('applicationId', undefined);
                    }
                    if (WEBHOOK_FIELD_TYPE.CUSTOM === data) {
                      setFieldValue('applicationMapValue', undefined);
                      setFieldValue('serviceMapValue', undefined);
                    }
                  }
                }}
              />
              {values.serviceNameType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
                <SelectWithPage
                  key={'serviceName'}
                  description={'Choose from list'}
                  disabled={isViewMode}
                  label='Service Name'
                  required={true}
                  value={serviceSelected}
                  options={serviceComboxOptions}
                  handleScrollToBottom={fetchNextPageService}
                  onSearch={(val) => {
                    setServiceSearchParams((prev) => ({ ...prev, page: 0, search: val }));
                  }}
                  onBlur={() => {
                    setServiceSearchParams(DEFAULT_PAGINATION_CUSTOM_REQUEST);
                  }}
                  isLoading={isServiceFetching}
                  onChange={(_, data) => {
                    setApplicationSelected(undefined);
                    setServiceSelected(data);
                    const id = data?.value.toString() || undefined;
                    setValues((prev) => ({ ...prev, serviceId: id, applicationId: undefined }));
                  }}
                />
              ) : (
                <KanbanInput
                  required
                  disabled={isViewMode}
                  label='Service Name'
                  description={getMaxLengthMessage(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)}
                  placeholder='Input column mapping service name'
                  {...getInputProps('serviceMapValue')}
                  maxLength={DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH}
                />
              )}
            </SimpleGrid>

            <SimpleGrid cols={2}>
              <KanbanSelect
                allowDeselect={false}
                description={MESSAGE_TYPE}
                required
                defaultValue={WEBHOOK_FIELD_TYPE.FROM_SOURCE}
                data={comboboxFieldType}
                disabled={true}
                label='Application Name Type:'
                {...getInputProps('applicationType')}
                onChange={(data) => {
                  getInputProps('applicationType').onChange(data);
                  if (WEBHOOK_FIELD_TYPE.FROM_SOURCE === data) {
                    setServiceSelected(undefined);
                    setApplicationSelected(undefined);
                    setFieldValue('serviceId', undefined);
                    setFieldValue('applicationId', undefined);
                  }
                  if (WEBHOOK_FIELD_TYPE.CUSTOM === data) {
                    setFieldValue('applicationMapValue', undefined);
                  }
                }}
              />
              {values.applicationType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
                <SelectWithPage
                  key={'appName'}
                  description={'Choose from list'}
                  value={applicationSelected}
                  disabled={isViewMode || !values.serviceId}
                  label='Application Name'
                  required={true}
                  options={applicationComboxOptions}
                  handleScrollToBottom={fetchNextPageApplication}
                  onSearch={(val) => {
                    setApplicationSearchParams((prev) => ({ ...prev, page: 0, search: val }));
                  }}
                  onBlur={() => {
                    setApplicationSearchParams(DEFAULT_PAGINATION_CUSTOM_REQUEST);
                  }}
                  isLoading={isApplicationFetching}
                  onChange={(_, data) => {
                    const id = data?.value.toString() || undefined;
                    setValues((prev) => ({ ...prev, applicationId: id }));
                    setApplicationSelected(data);
                  }}
                />
              ) : (
                <KanbanInput
                  description={getMaxLengthMessage(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)}
                  disabled={isViewMode}
                  required
                  maxLength={DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH}
                  label='Application Name:'
                  placeholder='Input column mapping application name'
                  {...getInputProps('applicationMapValue')}
                />
              )}
            </SimpleGrid>
            <KanbanInput
              description={getMaxLengthMessage(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)}
              disabled={isViewMode}
              required
              maxLength={DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH}
              label='Alert Content:'
              {...getInputProps('alertMapValue')}
            />
            <SimpleGrid cols={2}>
              <KanbanSelect
                allowDeselect={false}
                disabled={isViewMode}
                description={MESSAGE_TYPE}
                required
                defaultValue={WEBHOOK_FIELD_TYPE.FROM_SOURCE}
                data={comboboxFieldType}
                label='PriorityType:'
                {...getInputProps('priorityType')}
              />
              {values.priorityType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
                <KanbanSelect
                  description={'Choose from list'}
                  disabled={isViewMode}
                  required
                  searchable
                  data={priorityConfigOptions}
                  label='Priority:'
                  value={`${values.priorityId}`}
                  autoChangeValueByOptions={false}
                  onChange={(value) => {
                    setValues((prev) => ({ ...prev, priorityId: Number(value || 0) }));
                  }}
                />
              ) : (
                <KanbanInput
                  maxLength={DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH}
                  disabled={isViewMode}
                  description={getMaxLengthMessage(DATABASE_COLLECT_COLUMN_MAP_MAX_LENGTH)}
                  required
                  placeholder='Input column mapping priority name'
                  label='Priority:'
                  {...getInputProps('priorityMapValue')}
                />
              )}
            </SimpleGrid>

            <SimpleGrid cols={2}>
              <KanbanSelect
                allowDeselect={false}
                disabled={isViewMode}
                description={MESSAGE_TYPE}
                required
                data={comboboxFieldType}
                label='Contact:'
                {...getInputProps('contactType')}
                onChange={(value) => {
                  if (value === WEBHOOK_FIELD_TYPE.CUSTOM) {
                    setValues({ ...values, contactType: WEBHOOK_FIELD_TYPE.CUSTOM, contactMapValue: undefined });
                  } else {
                    setValues({ ...values, contactType: WEBHOOK_FIELD_TYPE.FROM_SOURCE, contactCustomValue: undefined });
                  }
                }}
              />
              {values.contactType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
                <KanbanInput
                  key={'contactCustomValue'}
                  disabled={isViewMode}
                  maxLength={MAX_RECIPIENT_LENGTH}
                  description={getMaxLengthMessage(MAX_RECIPIENT_LENGTH)}
                  required
                  label='Contact:'
                  placeholder='Input value custom priority name'
                  {...getInputProps('contactCustomValue')}
                />
              ) : (
                <KanbanInput
                  key={'contactMapValue'}
                  disabled={isViewMode}
                  maxLength={DATABASE_COLLECT_CONTACT_CUSTOM_MAX_LENGTH}
                  description={getMaxLengthMessage(DATABASE_COLLECT_CONTACT_CUSTOM_MAX_LENGTH)}
                  required
                  placeholder='Input column mapping contact name'
                  label='Contact:'
                  {...getInputProps('contactMapValue')}
                />
              )}
            </SimpleGrid>
          </Card>
        </form>
      </Modal>
    </>
  );
};

export default DatabaseCollectModal;
