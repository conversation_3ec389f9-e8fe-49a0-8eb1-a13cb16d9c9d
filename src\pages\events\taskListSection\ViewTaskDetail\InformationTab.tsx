import { TaskTimeTypeEnum, TaskTimeTypeLabel } from '@common/constants/TaskConstants';
import { Task } from '@core/schema/Task';
import { ComboboxData, Flex, Stack } from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { TaskModel } from '@models/TaskModel';
import dayjs from 'dayjs';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import React, { useCallback, useContext } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import AssignUser from '../../AssignUser';
import { EventPageContext } from '@pages/events/EventPageContext';
import { refetchRequest } from '@common/utils/QueryUtils';
import { TaskApi } from '@api/TaskApi';
import { refetchEventPageData } from '../Utilts';
import { MAX_DESCRIPTION_LENGTH, MAX_NAME_LENGTH } from '@common/constants/ValidationConstant';

interface Props {
  task?: Task;
  form: UseFormReturn<TaskModel>;
  editable: boolean;
}

const TaskTypeOptions: ComboboxData = Object.keys(TaskTimeTypeEnum).map((key) => ({ value: key, label: TaskTimeTypeLabel[key as TaskTimeTypeEnum] }));
const InformationTab = ({ editable, form, task }: Props) => {
  const { control, register, watch } = form;
  const startTime = watch('startTime');
  const timeType = watch('timeType');
  const { calendarMode, filterValue } = useContext(EventPageContext);
  const onAssignUserSuccess = useCallback(() => {
    if (task) {
      refetchRequest(TaskApi.findByTaskId(task.id));
      refetchRequest(TaskApi.getAssigneeHistory(task.id));
      refetchEventPageData(calendarMode, filterValue);
    }
  }, [calendarMode, filterValue, task]);
  return (
    <Flex
      align='center'
      gap='xl'
      mt='sm'
      bg='var(--mantine-primary-color-0)'
      p='sm'
      style={{ borderRadius: 'var(--mantine-radius-sm)', border: '1px solid var(--mantine-primary-color-2)' }}>
      <Stack flex={1} gap={0}>
        <KanbanInput label='Name' placeholder='Enter name' required {...register('name')} disabled={!editable} maxLength={MAX_NAME_LENGTH} />
        <KanbanInput
          label='Description'
          placeholder='Enter description'
          {...register('description')}
          disabled={!editable}
          maxLength={MAX_DESCRIPTION_LENGTH}
        />
        <Controller
          name='timeType'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanSelect
              label='Time type'
              data={TaskTypeOptions}
              allowDeselect={false}
              {...field}
              error={fieldState?.error?.message}
              disabled={!editable}
            />
          )}
        />
        <Flex align='center' gap='sm'>
          <Controller
            name='startTime'
            control={control}
            render={({ field, fieldState }) => {
              const day = dayjs(field.value);
              return (
                <DateTimePicker
                  value={day.isValid() ? day.toDate() : new Date()}
                  onChange={(value) => {
                    const dateValue = dayjs(value);
                    field.onChange(dateValue.isValid() ? dateValue.format() : '');
                  }}
                  placeholder='Pick date and time'
                  style={{ flex: 1 }}
                  label='Start time'
                  error={fieldState?.error?.message}
                  minDate={new Date()}
                  disabled={!editable}
                />
              );
            }}
          />
          {TaskTimeTypeEnum.FROM_TIME_TO_TIME === timeType && (
            <Controller
              name='endTime'
              control={control}
              render={({ field, fieldState }) => {
                const day = dayjs(field.value);
                return (
                  <DateTimePicker
                    value={day.isValid() ? day.toDate() : new Date()}
                    onChange={(value) => {
                      const dateValue = dayjs(value);
                      field.onChange(dateValue.isValid() ? dateValue.format() : '');
                    }}
                    placeholder='Pick date and time'
                    style={{ flex: 1 }}
                    label='End time'
                    error={fieldState?.error?.message}
                    minDate={startTime ? dayjs(startTime).toDate() : new Date()}
                    disabled={!editable}
                  />
                );
              }}
            />
          )}
        </Flex>
      </Stack>
      {task && <AssignUser task={task} onAssignSuccess={onAssignUserSuccess} />}
    </Flex>
  );
};

export default InformationTab;
