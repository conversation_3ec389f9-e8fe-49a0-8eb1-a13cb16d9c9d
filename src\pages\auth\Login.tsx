import { LegacyRef, useRef, useState } from 'react';
import { KanbanInput, KanbanSelect, KanbanIconButton, KanbanText } from 'kanban-design-system';
import { IconEye, IconEyeOff } from '@tabler/icons-react';
import classes from './Login.module.css';
import React from 'react';
import { Box, Button, ComboboxData } from '@mantine/core';
import { LoginRequestSchema } from '@models/LoginModel';
import useMutate from '@core/hooks/useMutate';
import { AuthApi } from '@api/Auth';
import { useNavigate } from 'react-router-dom';
import { currentUserSlice } from '@slices/CurrentUserSlice';
import { useAppDispatch } from '@store';
import { clearRefreshInterval, setAuthTokens, startRefreshTokenInterval } from '@common/utils/AuthenticateUtils';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

const DEFAULT_VALUE = { userName: '', password: '', userLocal: false };
enum USER_LOGIN_TYPE_ENUM {
  KEYCLOCK = 'KEYCLOCK',
  LOCAL = 'LOCAL',
}

const USER_LOGIN_TYPE_OPTIONS: ComboboxData = [
  {
    label: 'Active Directory Authentication',
    value: USER_LOGIN_TYPE_ENUM.KEYCLOCK,
  },
  {
    label: 'Local Authentication',
    value: USER_LOGIN_TYPE_ENUM.LOCAL,
  },
];

export const LoginPage = () => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const navigate = useNavigate();
  const [error, setError] = useState('');
  const dispatch = useAppDispatch();

  const togglePasswordVisibility = () => setIsPasswordVisible(!isPasswordVisible);
  const { control, handleSubmit } = useForm({
    defaultValues: DEFAULT_VALUE,
    resolver: zodResolver(LoginRequestSchema),
  });

  const { mutate: handleLogin } = useMutate(AuthApi.login, {
    successNotification: { enable: false },
    onSuccess: (res) => {
      if (res?.data?.refreshToken && res?.data?.accessToken && res?.data?.expiresAt) {
        setAuthTokens(res.data.refreshToken, res.data.accessToken, res.data.expiresAt);
        dispatch(currentUserSlice.actions.fetchData());
        startRefreshTokenInterval();
        navigate('/');
      } else {
        setError('Incorrect username or password, please try again.');
      }
    },
    errorNotification: { enable: false },
    onError: () => {
      setError('Incorrect username or password, please try again.');
      clearRefreshInterval();
    },
  });

  const ref: LegacyRef<HTMLInputElement> | undefined = useRef(null);
  return (
    <>
      <Box className={classes.container}>
        {error && (
          <Box className={classes.box}>
            <KanbanText style={{ color: 'red' }}>{error}</KanbanText>
          </Box>
        )}
        <Box className={classes.backgroundImage} />
        <Box className={classes.content}>
          <Box className={classes.leftImage}>
            <Box className={classes.overlayText}>MB CENTRALIZED MONITORING SYSTEM</Box>
          </Box>
          <Box className={classes.rightSide}>
            <form onSubmit={handleSubmit((value) => handleLogin(value))}>
              <Box className={classes.logoContainer}>
                <Box className={classes.logoImg} />
                <KanbanText className={classes.systemName}>MBMONITOR</KanbanText>
              </Box>
              <Controller
                control={control}
                name='userName'
                render={({ field }) => (
                  <KanbanInput
                    label='Username'
                    id='username'
                    autoComplete='username'
                    required
                    maxLength={100}
                    {...field}
                    ref={ref}
                    style={{ width: '100%' }}
                  />
                )}
              />
              <Controller
                control={control}
                name='password'
                render={({ field }) => (
                  <KanbanInput
                    label='Password'
                    required
                    type={isPasswordVisible ? 'text' : 'password'}
                    id='password'
                    autoComplete='current-password'
                    style={{ width: '100%' }}
                    {...field}
                    rightSection={
                      <KanbanIconButton onClick={togglePasswordVisibility} variant='transparent'>
                        {isPasswordVisible ? <IconEye style={{ width: '65%' }} /> : <IconEyeOff style={{ width: '65%' }} />}
                      </KanbanIconButton>
                    }
                  />
                )}
              />
              <Controller
                control={control}
                name='userLocal'
                render={({ field }) => (
                  <KanbanSelect
                    label='Authentication Type'
                    style={{ width: '100%' }}
                    required
                    data={USER_LOGIN_TYPE_OPTIONS}
                    value={field.value ? USER_LOGIN_TYPE_ENUM.LOCAL : USER_LOGIN_TYPE_ENUM.KEYCLOCK}
                    onChange={(value) => {
                      field.onChange(value === USER_LOGIN_TYPE_ENUM.LOCAL);
                      if (ref.current) {
                        ref.current.focus();
                      }
                    }}
                  />
                )}
              />
              <Button type='submit' style={{ width: '100%', marginBottom: '35px' }}>
                Login
              </Button>
            </form>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default LoginPage;
