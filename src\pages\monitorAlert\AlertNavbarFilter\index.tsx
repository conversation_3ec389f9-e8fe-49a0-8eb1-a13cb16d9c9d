import { Flex, FloatingIndicator, Indicator, Stack, Tabs, UnstyledButton } from '@mantine/core';
import React, { useCallback, useContext, useMemo, useState } from 'react';
import classes from './AlertNavbarFilter.module.css';
import { IconAdjustments, IconSchema, IconSettings } from '@tabler/icons-react';
import { KanbanTooltip } from 'kanban-design-system';
import TreeServiceFilter from './TreeServiceFilter';
import Filter from './Filter';
import { NavbarTabTypeEnum } from '../Types';
import Setting from './Setting';
import { isEmpty } from 'lodash';
import { MonitorAlertPageContext } from '../MonitorAlertPageContext';

type NavFilter = {
  id: NavbarTabTypeEnum;
  icon: React.ReactNode;
  title: string;
  navContent: React.ReactNode;
  showIndicator: boolean;
};

const AlertNavbarFilter = () => {
  const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
  const [controlsRefs, setControlsRefs] = useState<Partial<Record<NavFilter['id'], HTMLButtonElement | null>>>({});
  const { filter, setTab, tab, tree } = useContext(MonitorAlertPageContext);

  const onClickHandle = useCallback(
    (id: NavFilter['id']) => {
      if (tab === id) {
        setTab(undefined);
      } else {
        setTab(id);
      }
    },
    [setTab, tab],
  );

  const setControlRef = (id: NavFilter['id']) => (node: HTMLButtonElement) => {
    controlsRefs[id] = node;
    setControlsRefs(controlsRefs);
  };

  const navFilters = useMemo<NavFilter[]>(() => {
    return [
      {
        id: NavbarTabTypeEnum.TREE,
        icon: <IconSchema className={classes.controlLabel} size={20} />,
        title: 'Tree',
        navContent: <TreeServiceFilter />,
        showIndicator: !!tree.serviceId,
      },
      {
        id: NavbarTabTypeEnum.FITLER,
        icon: <IconAdjustments className={classes.controlLabel} size={20} />,
        title: 'Filter',
        navContent: <Filter />,
        showIndicator:
          !isEmpty(filter.services) ||
          !isEmpty(filter.applications) ||
          !isEmpty(filter.recipient) ||
          !isEmpty(filter.content) ||
          !isEmpty(filter.alertPriorityConfigIds),
      },
      {
        id: NavbarTabTypeEnum.SETTING,
        icon: <IconSettings className={classes.controlLabel} size={20} />,
        title: 'Setting',
        navContent: <Setting />,
        showIndicator: false,
      },
    ];
  }, [tree.serviceId, filter.services, filter.applications, filter.recipient, filter.content, filter.alertPriorityConfigIds]);

  return (
    <Flex>
      <Stack className={classes.root} ref={setRootRef}>
        {navFilters.map(({ icon, id, showIndicator, title }) => (
          <Indicator disabled={!showIndicator} color='gray' size={10} key={id}>
            <UnstyledButton className={classes.control} ref={setControlRef(id)} onClick={() => onClickHandle(id)} mod={{ active: tab === id }}>
              <KanbanTooltip label={title}>{icon}</KanbanTooltip>
            </UnstyledButton>
          </Indicator>
        ))}
        <FloatingIndicator target={tab ? controlsRefs[tab] : undefined} parent={rootRef} className={classes.indicator} />
      </Stack>
      <Tabs value={tab} display='flex' keepMounted={false}>
        {navFilters.map(({ id, navContent }) => (
          <Tabs.Panel key={id} value={id} className={classes.navContent}>
            {navContent}
          </Tabs.Panel>
        ))}
      </Tabs>
    </Flex>
  );
};

export default AlertNavbarFilter;
