import { ExportFileTypeEnum } from '@common/constants/ExportFileTypeConstants';
import { LIMIT_EXPORT_ROWS_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const QuerySqlExportModelSchema = z.object({
  id: z.number(),
  params: z.record(z.string(), z.any()),
  type: z.nativeEnum(ExportFileTypeEnum),
  nameFile: z.string().trim().optional(),
  numberOfResults: z.number().min(1).max(LIMIT_EXPORT_ROWS_LENGTH).optional(),
});

export type QuerySqlExportModel = z.infer<typeof QuerySqlExportModelSchema>;
