import { z } from 'zod';
import { createDateTimeSchema } from './Common';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { AlertStatusEnum } from '@common/constants/AlertStatusConstant';
import { NoteSchema } from './Note';

export const AlertSchema = z.object({
  id: z.number(),
  serviceName: z.string().optional(),
  applicationName: z.string().optional(),
  recipient: z.string().nullish(),
  content: z.string().nullish(),
  priorityName: z.string().optional(),
  priorityColor: z.string().optional(),
  status: z.nativeEnum(AlertStatusEnum),
  createdDate: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
  alertGroupId: z.number().optional(),
  closedDate: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS).optional(),
  closedDuration: z.string().optional(),
  closedBy: z.string().optional(),
  notes: z.array(NoteSchema).optional(),
});

export type Alert = z.infer<typeof AlertSchema>;
