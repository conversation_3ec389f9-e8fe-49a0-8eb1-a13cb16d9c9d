import { z } from 'zod';
import { TaskStatusEnum, TaskTypeEnum } from '@common/constants/TaskConstants';
import { DateRangeModel } from './DateRangeModel';

export const FilterTaskModelSchema = z.object({
  types: z.array(z.nativeEnum(TaskTypeEnum)).optional(),
  statuses: z.array(z.nativeEnum(TaskStatusEnum)).optional(),
  creatorUsers: z.array(z.string()),
  assigneeUsers: z.array(z.string()),
  dateRanges: z.array(DateRangeModel),
  search: z.string(),
});

export type FilterTaskModel = z.infer<typeof FilterTaskModelSchema>;
