import React from 'react';
import { KanbanButton, KanbanText } from 'kanban-design-system';
import { EmailConfigApi } from '@api/EmailConfigApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import WarningAlertComponent from '@components/WarningAlertComponent';
import { EmailConfig } from '@core/schema/EmailConfig';

type ConfirmActiveModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  emailConfig?: EmailConfig;
};

const ConfirmActiveModal: React.FC<ConfirmActiveModalProps> = ({ emailConfig, onClose, opened, refetchList }) => {
  const { data: listCollectEmailConfig, refetch: refetch } = useFetch(EmailConfigApi.findAllByEmailConfigId(emailConfig?.id ?? 0), {
    enabled: !!emailConfig?.id && opened,
  });
  const { mutate: activeOrInactive } = useMutate(EmailConfigApi.activeOrInactive, {
    successNotification: 'Update status of config successfully!',
    onSuccess: () => {
      onClose();
      refetch();
      refetchList();
    },
  });
  return (
    <Modal
      size='xl'
      opened={opened}
      onClose={() => {
        onClose();
      }}
      title={`Confirm ${emailConfig?.active ? 'deactivate' : 'activate'} email connection`}
      actions={<KanbanButton onClick={() => activeOrInactive(emailConfig?.id ?? 0)}>Confirm</KanbanButton>}>
      {listCollectEmailConfig?.data && (
        <WarningAlertComponent
          mainEntity={`Email connection ${emailConfig?.protocolType} ${emailConfig?.email}`}
          dependencyEntity='collect email configs'
          dependencies={listCollectEmailConfig?.data.map((e) => e.name)}
        />
      )}
      <KanbanText>Are you sure you want to {emailConfig?.active ? 'deactivate' : 'activate'} the email connection?</KanbanText>
    </Modal>
  );
};

export default ConfirmActiveModal;
