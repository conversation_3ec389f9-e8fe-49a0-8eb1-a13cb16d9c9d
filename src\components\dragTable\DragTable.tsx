import { ActionIcon, Box, Table as MantineTable } from '@mantine/core';
import { getDataKey, getDefaultBgColor, getDisableDraggable, getItemIndex } from './Utils';
import React, { useCallback, useEffect, useState } from 'react';
import { DragTableProps, DragTableRowProps } from './Types';
import { IconArrowsMoveVertical } from '@tabler/icons-react';
import { closestCorners, DndContext, DragEndEvent, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { CSS } from '@dnd-kit/utilities';
import TData from './TData';
import THead from './THead';
import classes from '../table/Table.module.css';
import { ZIndexDraging } from '@common/constants/ZIndexConstants';

function Row<T extends object>({ columns, customProps, disableDraggable, index, itemKey, record, showIndexColumn }: DragTableRowProps<T>) {
  const disabledDrag = getDisableDraggable(record, disableDraggable);
  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({ id: itemKey, data: record, disabled: disabledDrag });
  return (
    <MantineTable.Tr
      style={{
        borderBottom: 'none',
        transform: CSS.Translate.toString(transform),
        transition,
        backgroundColor: isDragging ? 'var(--mantine-primary-color-1)' : getDefaultBgColor(index),
        zIndex: isDragging ? ZIndexDraging : 'initial',
        position: isDragging ? 'relative' : 'initial',
      }}
      ref={setNodeRef}
      {...attributes}>
      <MantineTable.Td p='xs'>
        <ActionIcon
          variant='light'
          style={{
            cursor: 'grab',
          }}
          {...listeners}
          disabled={disabledDrag}>
          <IconArrowsMoveVertical style={{ width: '70%', height: '70%' }} />
        </ActionIcon>
      </MantineTable.Td>
      {showIndexColumn && (
        <MantineTable.Td p='xs' {...customProps?.indexColumnTd}>
          {getItemIndex(0, 0, index)}
        </MantineTable.Td>
      )}
      {columns.map((column, index) => (
        <TData key={column.id} column={column} record={record} customProps={customProps} index={index} />
      ))}
    </MantineTable.Tr>
  );
}

const RowMeno = React.memo(Row) as <T extends object>(props: DragTableRowProps<T>) => JSX.Element;

/**
 * Move all static row to first table.
 * @param data data
 * @param staticDataIds staticDataIds
 * @param position top | buttom
 * @returns data
 */
function sortDataByStaticIds<T extends { id: number | string }>(data: T[], staticDataIds: (number | string)[], position: 'top' | 'bottom'): T[] {
  return data.sort((a, b) => {
    const aIndex = staticDataIds.indexOf(a.id);
    const bIndex = staticDataIds.indexOf(b.id);

    if (position === 'top') {
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      if (aIndex !== -1) {
        return -1;
      }
      if (bIndex !== -1) {
        return 1;
      }
    } else if (position === 'bottom') {
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      if (aIndex !== -1) {
        return 1;
      }
      if (bIndex !== -1) {
        return -1;
      }
    }
    return 0;
  });
}

function DragTable<T extends object & { id: number | string }>({
  columns,
  customProps,
  data,
  dataKey,
  disableDraggable = false,
  onDragHandler,
  position = 'top',
  showIndexColumn = true,
  staticIds = [],
}: DragTableProps<T>) {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { delay: 50, tolerance: 2 },
    }),
    useSensor(KeyboardSensor),
  );
  const [sortableData, setSortableData] = useState<T[]>(sortDataByStaticIds(data, staticIds, position));
  useEffect(() => {
    setSortableData(data);
  }, [data]);

  const onDragEnd = useCallback(
    ({ active, over }: DragEndEvent) => {
      if (!active || !over) {
        return;
      }
      const activeData = active.data.current as T;
      const overData = over.data.current as T;
      if (!activeData || !overData) {
        return;
      }
      const oldIndex = sortableData?.findIndex((ele) => getDataKey(ele, dataKey) === getDataKey(activeData, dataKey)) || 0;
      const newIndex = sortableData?.findIndex((ele) => getDataKey(ele, dataKey) === getDataKey(overData, dataKey)) || 0;
      setSortableData((prev) => arrayMove(prev, oldIndex, newIndex));
      onDragHandler(activeData, overData, oldIndex, newIndex);
    },
    [dataKey, onDragHandler, sortableData],
  );

  return (
    <Box className={classes.customTableWrapper}>
      <DndContext onDragEnd={onDragEnd} sensors={sensors} collisionDetection={closestCorners} modifiers={[restrictToVerticalAxis]}>
        <SortableContext
          strategy={verticalListSortingStrategy}
          items={sortableData.filter((obj) => !staticIds.includes(obj.id))?.map((record) => getDataKey(record, dataKey)) || []}>
          <MantineTable {...customProps?.table}>
            <MantineTable.Thead {...customProps?.thead}>
              <MantineTable.Tr {...customProps?.theadTr}>
                <MantineTable.Th></MantineTable.Th>
                {showIndexColumn && (
                  <MantineTable.Th p='xs' {...customProps?.indexColumnTh}>
                    No.
                  </MantineTable.Th>
                )}
                {columns.map((column) => (
                  <THead key={column.id} column={column} customProps={customProps} />
                ))}
              </MantineTable.Tr>
            </MantineTable.Thead>
            <MantineTable.Tbody {...customProps?.tbody} style={{ ...customProps?.tbody?.style, position: 'relative' }}>
              {sortableData.map((record, index) => {
                const itemKey = getDataKey(record, dataKey);
                return (
                  <RowMeno
                    key={itemKey}
                    columns={columns}
                    index={index}
                    itemKey={itemKey}
                    record={record}
                    showIndexColumn={showIndexColumn}
                    customProps={customProps}
                    disableDraggable={disableDraggable || staticIds.includes(record.id)}
                  />
                );
              })}
            </MantineTable.Tbody>
          </MantineTable>
        </SortableContext>
      </DndContext>
    </Box>
  );
}

export default DragTable;
