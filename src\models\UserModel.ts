import { USER_NAME_REGEX } from '@common/constants/RegexConstant';
import { REGEX_USER_NAME_ERROR } from '@core/message/MesageConstant';
import { z } from 'zod';
import { isEmpty } from 'lodash';
import { MAX_PASSWORD_LENGTH, MIN_PASSWORD_LENGTH } from '@common/constants/ValidationConstant';

export const UserModelSchema = z
  .object({
    id: z.number().optional(),
    userName: z.string().trim().min(1).regex(USER_NAME_REGEX, { message: REGEX_USER_NAME_ERROR }),
    roleIds: z.array(z.number()).optional(),
    password: z.string().optional().nullable(),
    isUserLocal: z.boolean().default(false),
  })
  .superRefine((value, ctx) => {
    if (value.isUserLocal === true) {
      if (!value.password || isEmpty(value.password)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['password'],
          message: 'Password can not be blank',
        });
      } else if (value.password.length < MIN_PASSWORD_LENGTH || value.password.length > MAX_PASSWORD_LENGTH) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['password'],
          message: 'Password must be between 8 and 100 characters',
        });
      }
    }
  });

export type UserModel = z.infer<typeof UserModelSchema>;
