.alertTable{
  user-select: none; /* CSS3 (little to no support) */
  -ms-user-select: none; /* IE 10+ */
  -moz-user-select: none; /* Gecko (Firefox) */
  -webkit-user-select: none; /* Webkit (Safari, Chrome) */
}
.tableCell{
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #041557;
}
.alertContent{
  font-weight: 600;
  font-size: 16px;
  line-height: 26px;
  word-wrap: break-word;
  word-break: break-word;
}
.alertOtherInfo{
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #9AA1BC;
}
.tabList{
  justify-content: space-between;
  background-color: #F6F8FF;
  border: none;
  &:before{
    border: none;
  }
}
.tabItem{
  flex: 1;
  border: 1px solid transparent;
  &:where([data-active]){
    background-color: white;
    border: 1px solid #DADDFA;
  }
}
.selectedAlertTitle{
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
}
.alertHandleWrapper{
  max-width: 450px;
}
.alertAmount{
  font-weight: 600;
  font-size: 10px;
  line-height: 14px;
  border-radius: 100px;
  color: white;
  padding: calc(var(--mantine-spacing-xs) / 2);
  min-width: 25px;
  text-align: center;
  text-wrap: nowrap;
}