import React from 'react';
import classes from '../ExecutionGroups.module.scss';
import { Box, Stack, Title } from '@mantine/core';
import { useSearchParams } from 'react-router-dom';
import { EXECUTION_ID_SEARCH_PARAM_KEY } from '../Constants';
import useFetch from '@core/hooks/useFetch';
import { ExecutionApi } from '@api/ExecutionApi';
import EmptyBox from '@components/EmptyBox';
import ExecutionEditor from './ExecutionEditor';

const ExecutionDetail = () => {
  const [searchParams] = useSearchParams({ [EXECUTION_ID_SEARCH_PARAM_KEY]: '' });
  const executionId = searchParams.get(EXECUTION_ID_SEARCH_PARAM_KEY);
  const { data: executionDetailData } = useFetch(ExecutionApi.findByIdWithVariable(executionId || ''), {
    enabled: !!executionId,
    showLoading: false,
    placeholderData: (prev) => prev,
  });

  if (!executionId || !executionDetailData?.data) {
    return (
      <Box className={classes.executionDetail}>
        <Stack gap='xs' flex={1} align='center' justify='center' h='100%'>
          <EmptyBox />
          <Title order={4} c='var(--mantine-color-gray-5)'>
            Select a execution
          </Title>
        </Stack>
      </Box>
    );
  }
  return (
    <Box className={classes.executionDetail}>
      <ExecutionEditor execution={executionDetailData.data} />
    </Box>
  );
};

export default ExecutionDetail;
