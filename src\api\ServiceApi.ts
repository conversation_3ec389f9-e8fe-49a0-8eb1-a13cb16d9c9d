import { createPageSchema, createResponseSchema, ServiceWithPrioritySchema, Service, ServiceSchema, ApplicationSchema } from '@core/schema';
import { z } from 'zod';
import { BaseURL } from '@common/constants/BaseUrl';
import { ServicePaginationRequest } from '@models/ServiceModel';
import { ExportFileRequest } from '@models/ExportFileModel';
import { WebHookSchema } from '@core/schema/WebHook';
import { AlertGroupStatusEnum } from '@common/constants/AlertGroupStatusConstant';
import { createRequest } from './Utils';
import { ServiceDependenciesSchema } from '@core/schema/ServiceDependencies';
import { ExportData } from '@core/schema/ExportData';

export class ServiceApi {
  static findServiceWithPriorityByAlertGroupStatus(alertGroupStatus: AlertGroupStatusEnum) {
    return createRequest({
      url: `${BaseURL.service}/with-priority`,
      method: 'GET',
      schema: createResponseSchema(z.array(ServiceWithPrioritySchema)),
      params: {
        alertGroupStatus,
      },
    });
  }
  static findAll(pagination: ServicePaginationRequest) {
    return createRequest({
      url: BaseURL.service,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(ServiceSchema)),
    });
  }

  static findById(id: string) {
    return createRequest({
      url: `${BaseURL.service}/:id`,
      method: 'GET',
      schema: createResponseSchema(ServiceSchema),
      pathVariable: {
        id,
      },
    });
  }
  static findAllApplicationById(id: string) {
    return createRequest({
      url: `${BaseURL.service}/:id/applications`,
      method: 'GET',
      schema: createResponseSchema(z.array(ApplicationSchema)),
      pathVariable: {
        id,
      },
    });
  }
  static findAllDependenciesById(id: string) {
    return createRequest({
      url: `${BaseURL.service}/:id/dependencies`,
      method: 'GET',
      schema: createResponseSchema(ServiceDependenciesSchema),
      pathVariable: {
        id,
      },
    });
  }
  static findAllWebhookById(id: string) {
    return createRequest({
      url: `${BaseURL.service}/:id/webhooks`,
      method: 'GET',
      schema: createResponseSchema(z.array(WebHookSchema)),
      pathVariable: {
        id,
      },
    });
  }
  static save(body: Service) {
    return createRequest({
      url: BaseURL.service,
      method: 'POST',
      data: body,
    });
  }

  static deleteById(id: string) {
    return createRequest<string>({
      url: `${BaseURL.service}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    });
  }

  static export(request: ExportFileRequest) {
    return createRequest<ExportData>({
      url: `${BaseURL.service}/export`,
      method: 'POST',
      data: request,
    });
  }
}
