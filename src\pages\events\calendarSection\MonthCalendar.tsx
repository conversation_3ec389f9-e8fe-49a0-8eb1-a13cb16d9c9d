import { Box, SimpleGrid, Stack } from '@mantine/core';
import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useContext, useMemo } from 'react';
import { getAllDatesOfMonth, getDateRange } from './Utils';
import { ViewModeEnum } from '../Contants';
import { TaskApi } from '@api/TaskApi';
import useFetch from '@core/hooks/useFetch';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { useIntersection } from '@mantine/hooks';
import { EventPageContext } from '../EventPageContext';
import DayOfWeeks from './DayOfWeeks';
import Date from './Date';
import clasess from './CalendarSection.module.css';

interface Props {
  scrollRef: React.RefObject<HTMLDivElement>;
  month: number;
}

const MonthCalendar = ({ month, scrollRef }: Props) => {
  const { calendarMode, filterValue, setCalendarMode } = useContext(EventPageContext);
  const { day, selectedDates, viewMode } = calendarMode;
  const dayMonth = useMemo(() => day.set('month', month), [day, month]);
  const datesOfMonth = useMemo(() => getAllDatesOfMonth(dayMonth), [dayMonth]);
  const isSmallView = viewMode === ViewModeEnum.YEAR;
  const onClickToDate = useCallback(
    (event: React.MouseEvent<HTMLDivElement, MouseEvent>, date: Dayjs) => {
      const index = selectedDates.findIndex((ele) => ele.isSame(date, 'day'));
      const isSelected = index !== -1;
      if (!event.shiftKey && !event.ctrlKey) {
        setCalendarMode({ ...calendarMode, selectedDates: isSelected ? [] : [date] });
        return;
      }
      if (event.shiftKey) {
        if (selectedDates.length) {
          const lastSelectedDate = selectedDates[selectedDates.length - 1];
          const dateRange = getDateRange(date, lastSelectedDate);
          setCalendarMode({
            ...calendarMode,
            selectedDates: [...dateRange],
          });
          return;
        } else {
          setCalendarMode({ ...calendarMode, selectedDates: [...selectedDates, date] });
          return;
        }
      }
      if (event.ctrlKey) {
        if (isSelected) {
          selectedDates.splice(index, 1);
          setCalendarMode({ ...calendarMode, selectedDates: [...selectedDates] });
          return;
        }
        setCalendarMode({ ...calendarMode, selectedDates: [...selectedDates, date] });
        return;
      }
    },
    [selectedDates, setCalendarMode, calendarMode],
  );
  const { entry, ref } = useIntersection({
    root: scrollRef.current,
    threshold: 1,
  });
  const { data: taskCountData } = useFetch(
    TaskApi.countTask({
      ...filterValue,
      dateRanges: [
        {
          fromDate: dayMonth.startOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
          toDate: dayMonth.endOf('month').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
        },
      ],
    }),
    { enabled: entry?.isIntersecting === true, showLoading: false },
  );

  return (
    <Stack>
      <Box>
        <DayOfWeeks isSmallView={isSmallView} ref={ref} />
        {datesOfMonth.map((datesOfWeeks, weekIndex) => (
          <SimpleGrid key={weekIndex} cols={7} spacing={0}>
            {datesOfWeeks.map((date, index) => {
              const taskCount = taskCountData?.data?.find((ele) => dayjs(ele.date).isSame(date, 'date'));
              const isOutOfMonth = date.month() !== month;
              const isSelectedDate = selectedDates.some((ele) => ele.isSame(date, 'day'));
              const bgColor = isOutOfMonth ? 'var(--mantine-color-gray-1)' : isSelectedDate ? 'var(--mantine-color-primary-1)' : undefined;
              return (
                <Box
                  key={index}
                  className={`${clasess.day} ${clasess.dayOfWeek}`}
                  onClick={(event) => !isOutOfMonth && onClickToDate(event, date)}
                  style={{
                    cursor: isOutOfMonth ? undefined : 'pointer',
                    padding: isSmallView ? 'calc(var(--mantine-spacing-xs) / 2)' : 'var(--mantine-spacing-sm)',
                    color: isOutOfMonth ? 'var(--mantine-color-gray-4)' : 'var(--mantine-primary-color-7)',
                    backgroundColor: bgColor,
                    borderRight: date.day() === 6 ? '1px solid var(--mantine-color-default-border)' : undefined,
                    borderBottom: weekIndex === datesOfMonth.length - 1 ? '1px solid var(--mantine-color-default-border)' : undefined,
                    minHeight: isSmallView ? 70 : 125,
                  }}>
                  <Date key={index} taskCount={isOutOfMonth ? undefined : taskCount} date={date} isSmallView={isSmallView} />
                </Box>
              );
            })}
          </SimpleGrid>
        ))}
      </Box>
    </Stack>
  );
};

export default MonthCalendar;
