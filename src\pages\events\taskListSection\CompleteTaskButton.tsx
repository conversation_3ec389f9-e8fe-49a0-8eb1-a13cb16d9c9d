import React, { useMemo } from 'react';
import { KanbanButton } from 'kanban-design-system';
import { Task } from '@core/schema/Task';
import { useAppSelector } from '@store';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { TaskStatusEnum, TaskTypeEnum } from '@common/constants/TaskConstants';

interface Props {
  task: Task;
  onClick: () => void;
  disabled?: boolean;
}

const CompleteTaskButton = ({ disabled, onClick, task }: Props) => {
  const currentUser = useAppSelector(getCurrentUser).userInfo;
  const hasEditPermission = useMemo(() => isAnyPermissions([AclPermission.taskEdit]), []);
  const isTaskDoneable =
    (TaskStatusEnum.NEW === task.status && TaskTypeEnum.SHIFT_HANDOVER_TASK === task.type) ||
    (TaskStatusEnum.INPROGRESS === task.status && TaskTypeEnum.TASK === task.type);
  const assigneeUserIsMe = currentUser?.userName === task.currentAssigneeUserName;
  const createdTaskUserIsMe = currentUser?.userName === task.createdBy;
  // Assignee user or user have EDIT permission
  const completeable = isTaskDoneable && (assigneeUserIsMe || hasEditPermission || createdTaskUserIsMe);
  if (!completeable) {
    return null;
  }

  return (
    <KanbanButton onClick={onClick} variant='outline' disabled={disabled}>
      Complete
    </KanbanButton>
  );
};

export default CompleteTaskButton;
