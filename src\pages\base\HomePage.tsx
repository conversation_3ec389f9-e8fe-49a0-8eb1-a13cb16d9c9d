import { getConfigs } from '@core/configs/Configs';
import React from 'react';
import styled from 'styled-components';

const Container = styled.div`
  text-align: center;
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
`;
const Title = styled.div`
  font-size: 2.5rem;
  div {
    display: inline-block;
    span {
      margin-right: 1px;
      font-weight: 900;
      color: var(--mantine-color-primary-5);
    }
  }
`;
const Description = styled.div``;

const config = getConfigs();

export default function HomePage() {
  return (
    <>
      <Container>
        <Title>
          Welcome to <div>{config.name}</div>
        </Title>
        <Description>Click to menu to navigate</Description>
      </Container>
    </>
  );
}
