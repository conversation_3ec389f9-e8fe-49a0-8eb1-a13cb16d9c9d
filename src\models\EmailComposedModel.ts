import { HTML_CONTENT_VALIDATION_REGEX } from '@common/constants/RegexConstant';
import { MAX_EMAIL_ADDRESSES, MAX_EMAIL_SUBJECT_CHARACTER_LENGTH } from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { FileStorageModelSchema } from './FileStorageModel';
import { EmailModel } from './Common';
import { EmailPartnerSchema } from '@core/schema/EmailPartner';

export const EmailComposedModelSchema = z.object({
  id: z.number().optional(),
  partners: z.array(EmailPartnerSchema).optional(),
  subject: z.string().min(1).max(MAX_EMAIL_SUBJECT_CHARACTER_LENGTH, `Subject should be at most ${MAX_EMAIL_SUBJECT_CHARACTER_LENGTH} characters`),
  files: z.array(z.instanceof(File)).optional(),
  isOneEmail: z.boolean(),
  emailSender: z.string().email(),
  fileStorages: z.array(FileStorageModelSchema).optional(),
  to: z
    .array(EmailModel)
    .max(MAX_EMAIL_ADDRESSES, { message: `The number of email addresses cannot exceed ${MAX_EMAIL_ADDRESSES}.` })
    .optional(),
  cc: z
    .array(EmailModel)
    .max(MAX_EMAIL_ADDRESSES, { message: `The number of email addresses cannot exceed ${MAX_EMAIL_ADDRESSES}.` })
    .optional(),
  content: z.string().refine(
    (value) => {
      const plainText = value.replace(HTML_CONTENT_VALIDATION_REGEX, '').trim();
      return plainText.length > 0;
    },
    {
      message: 'Content cannot be empty.',
    },
  ),
});

export type EmailComposedModel = z.infer<typeof EmailComposedModelSchema>;
