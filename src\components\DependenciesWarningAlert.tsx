import React from 'react';
import WarningAlertComponent from './WarningAlertComponent';

export type DependencyItem = {
  dependencyEntity: string;
  dependencies: string[];
  action?: string;
};
type Props = {
  dependencyConfigs: DependencyItem[];
  mainEntity: string;
  isDeleted: boolean;
};

export const DependenciesWarningAlert: React.FC<Props> = ({ dependencyConfigs, isDeleted, mainEntity }) => {
  if (dependencyConfigs.length === 0) {
    return null;
  }

  return (
    <>
      {dependencyConfigs.map((config, index) => (
        <WarningAlertComponent
          key={index}
          mainEntity={mainEntity}
          dependencyEntity={config.dependencyEntity}
          dependencies={config.dependencies}
          color={isDeleted ? 'red' : undefined}
          isDeleted={isDeleted}
          action={config.action}
        />
      ))}
    </>
  );
};

export default DependenciesWarningAlert;
