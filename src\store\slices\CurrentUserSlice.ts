import { UserDetail } from '@core/schema/UserDetails';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { type RootStoreType } from '@store';

interface UserState {
  isFetching: boolean;
  userInfo?: UserDetail;
}

const initialState: UserState = {
  isFetching: false,
};

export const currentUserSlice = createSlice({
  name: 'currentUser',
  initialState,
  reducers: {
    fetchData() {},
    setValue(_state, action: PayloadAction<UserState>) {
      return action.payload;
    },
  },
});

export const getCurrentUser = (store: RootStoreType) => store.currentUser;
