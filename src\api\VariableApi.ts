import { createPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { PaginationModel } from '@models/PaginationModel';
import { VariableSchema } from '@core/schema/Variable';
import { z } from 'zod';
import { VariableModel } from '@models/VariableModel';

export class VariableApi {
  static findAllWithPaging(pagination: PaginationModel) {
    return createRequest({
      url: `${BaseURL.variable}/with-paging`,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(VariableSchema)),
    });
  }
  static findAll() {
    return createRequest({
      url: BaseURL.variable,
      method: 'GET',
      schema: createResponseSchema(z.array(VariableSchema)),
    });
  }
  static findById(id: string) {
    return createRequest({
      url: `${BaseURL.variable}/:id`,
      method: 'GET',
      pathVariable: { id },
      schema: createResponseSchema(VariableSchema),
    });
  }
  static createOrUpdate(variableModel: VariableModel) {
    return createRequest({
      url: BaseURL.variable,
      method: 'POST',
      data: variableModel,
      schema: createResponseSchema(VariableSchema),
    });
  }
  static deleteById(id: string) {
    return createRequest({
      url: `${BaseURL.variable}/:id`,
      method: 'DELETE',
      pathVariable: { id },
    });
  }
}
