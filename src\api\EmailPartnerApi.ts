import { createPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { PaginationRequest } from './Type';
import { EmailPartnerSchema } from '@core/schema/EmailPartner';
import { EmailPartnerModel } from '@models/EmailPartnerModel';
import { createRequest } from './Utils';

export type EmailPartnerPaginationRequest = PaginationRequest & {
  search?: string;
};

export class EmailPartnerApi {
  static findAll(tableAffected: EmailPartnerPaginationRequest) {
    return createRequest({
      url: BaseURL.partnerManagement,
      method: 'GET',
      params: tableAffected,
      schema: createResponseSchema(createPageSchema(EmailPartnerSchema)),
    });
  }
  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.partnerManagement}/:id`,
      method: 'GET',
      schema: createResponseSchema(EmailPartnerSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(body: EmailPartnerModel) {
    return createRequest({
      url: BaseURL.partnerManagement,
      method: 'POST',
      data: body,
    });
  }

  static deleteById(id: number) {
    return createRequest<string>({
      url: `${BaseURL.partnerManagement}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
    });
  }
}
