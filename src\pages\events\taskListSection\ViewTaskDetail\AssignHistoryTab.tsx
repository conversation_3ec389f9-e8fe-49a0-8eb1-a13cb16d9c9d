import { TaskApi } from '@api/TaskApi';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import useFetch from '@core/hooks/useFetch';
import { Task } from '@core/schema/Task';
import { Grid, Stack } from '@mantine/core';
import FieldTitle from '@pages/events/FieldTitle';
import dayjs from 'dayjs';
import React from 'react';

interface Props {
  task?: Task;
}

const AssignHistoryTab = ({ task }: Props) => {
  const { data } = useFetch(TaskApi.getAssigneeHistory(task?.id || 0), { enabled: !!task?.id });
  return (
    <Stack mt='sm' mah={400} style={{ overflow: 'auto' }}>
      {data?.data?.map((taskUser) => (
        <Stack
          key={taskUser.id}
          p='sm'
          gap='xl'
          style={{ borderRadius: 'var(--mantine-radius-sm)', border: '1px solid var(--mantine-primary-color-2)' }}
          bg='var(--mantine-primary-color-0)'>
          <Grid gutter='sm'>
            <Grid.Col span={2}>
              <FieldTitle title='Time' />
            </Grid.Col>
            <Grid.Col span={10}>{dayjs(taskUser.createdDate).format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}</Grid.Col>
            <Grid.Col span={2}>
              <FieldTitle title='Assignee' />
            </Grid.Col>
            <Grid.Col span={10}>{taskUser.userName}</Grid.Col>
            <Grid.Col span={2}>
              <FieldTitle title='Assign by' />
            </Grid.Col>
            <Grid.Col span={10}>{taskUser.createdBy}</Grid.Col>
          </Grid>
        </Stack>
      ))}
    </Stack>
  );
};

export default AssignHistoryTab;
