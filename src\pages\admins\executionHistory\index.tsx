import { ExecutionHistoryApi } from '@api/ExecutionHistoryApi';
import React from 'react';
import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import InfiniteScrollTable from '@components/dragTable/InfiniteScrollTable';
import { Column } from '@components/dragTable/Types';
import { ExecutionHistory } from '@core/schema/ExecutionHistory';
import { ExecutionStatusEnum, ExecutionTypeLabel } from '@common/constants/ExecutionConstants';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { Box, Button, Flex, Stack, Text, Title } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import ExecutionHistoryStatus from './ExecutionHistoryStatus';

const DEFAULT_EXECUTION_HISTORY_PAGE_SIZE = 20;

const ViewDetailButton = ({ executionHistory }: { executionHistory: ExecutionHistory }) => {
  const navigate = useNavigate();
  return (
    <Button variant='outline' onClick={() => navigate({ pathname: `${ROUTE_PATH.EXECUTION_HISTORY}/${executionHistory.id}` })}>
      View Result
    </Button>
  );
};

export const COLUMNS: Column<ExecutionHistory>[] = [
  {
    id: 'name',
    title: 'Name',
    render: 'executionName',
    width: '25%',
  },
  {
    id: 'type',
    title: 'Type',
    render: (data) => (
      <Text fw={500} fz={15}>
        {ExecutionTypeLabel[data.executionType]}
      </Text>
    ),
    width: '10%',
  },
  {
    id: 'executionBy',
    title: 'Username',
    render: 'executionBy',
    width: '10%',
  },
  {
    id: 'startTime',
    title: 'Start time',
    render: (data) => data.startTime.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS),
  },
  {
    id: 'endTime',
    title: 'End time',
    render: (data) => data?.endTime?.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS) || '',
  },
  {
    id: 'status',
    title: 'Status',
    render: (data) => <ExecutionHistoryStatus status={data.status} />,
    width: '20%',
  },
  {
    id: 'action',
    title: 'Action',
    render: (data) =>
      data.status === ExecutionStatusEnum.COMPLETED || data.status === ExecutionStatusEnum.FAILED ? (
        <ViewDetailButton executionHistory={data} />
      ) : null,
  },
];

const ExecutionHistoryPage = () => {
  const { errorUpdateCount, fetchNextPage, flatData, isFetching } = useInfiniteCursorFetch(
    ExecutionHistoryApi.findAll({ pageSize: DEFAULT_EXECUTION_HISTORY_PAGE_SIZE }),
  );

  return (
    <Stack flex={1} h='100%'>
      <Flex justify='space-between'>
        <Title order={3} c='primary'>
          Execution history
        </Title>
      </Flex>
      <Box flex={1} style={{ overflow: 'auto' }}>
        <InfiniteScrollTable
          columns={COLUMNS}
          data={flatData || []}
          showIndexColumn={false}
          onScrollToBottom={() => {
            if (errorUpdateCount < 1) {
              fetchNextPage();
            }
          }}
          loading={isFetching}
        />
      </Box>
    </Stack>
  );
};

export default ExecutionHistoryPage;
