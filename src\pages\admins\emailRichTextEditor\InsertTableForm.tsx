import classes from './Template.module.scss';

import React, { useState } from 'react';
import { KanbanButton, KanbanNumberInput } from 'kanban-design-system';
import { Tooltip, Button } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconTable } from '@tabler/icons-react';
import Modal from '@components/Modal';

type InsertTableProps = {
  onInsert: (columns: number, rows: number) => void;
};

const InsertTableForm: React.FC<InsertTableProps> = ({ onInsert }) => {
  const [columns, setColumns] = useState<number>(2);
  const [rows, setRows] = useState<number>(2);
  const [openedModalCreateTemplate, { close: closeModalCreateTemplate, open: openModalCreateTemplate }] = useDisclosure(false);

  const handleInsert = () => {
    onInsert(columns, rows);
    closeModalCreateTemplate();
  };

  return (
    <>
      <Tooltip label='Insert Table' withArrow position='bottom'>
        <Button className={classes.mantineButton} variant='subtle' size='xs' onClick={openModalCreateTemplate}>
          <IconTable size={16} />
        </Button>
      </Tooltip>
      <Modal
        opened={openedModalCreateTemplate}
        onClose={closeModalCreateTemplate}
        title='Insert Table'
        actions={<KanbanButton onClick={handleInsert}>Insert</KanbanButton>}>
        <KanbanNumberInput label='Number of columns' value={columns} required={true} onChange={(e) => setColumns(Number(e))} min={1} max={10} />
        <KanbanNumberInput label='Number of rows' value={rows} required={true} onChange={(e) => setRows(Number(e))} min={1} max={20} />
      </Modal>
    </>
  );
};

export default InsertTableForm;
