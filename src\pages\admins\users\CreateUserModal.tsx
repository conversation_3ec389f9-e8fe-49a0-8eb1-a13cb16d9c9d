import { useDisclosure } from '@mantine/hooks';
import { KanbanCheckbox, KanbanButton, KanbanInput, KanbanMultiSelect, KanbanIconButton } from 'kanban-design-system';
import React, { useMemo, useState } from 'react';
import { IconPlus } from '@tabler/icons-react';
import Modal from '@components/Modal';
import useMutate from '@core/hooks/useMutate';
import { UserApi } from '@api/UserApi';
import { UserModel, UserModelSchema } from '@models/UserModel';
import { useForm, zodResolver } from '@mantine/form';
import useFetch from '@core/hooks/useFetch';
import { RoleApi } from '@api/RoleApi';
import { IconEye, IconEyeOff } from '@tabler/icons-react';
interface CreateUserProps {
  fetchListUsers?: () => void;
}

export const CreateUserModal = (props: CreateUserProps) => {
  const [openedModalCreateUser, { close: closeModalCreateUser, open: openModalCreateUser }] = useDisclosure(false);
  const [rolesSelected, setRolesSelected] = useState<string[]>([]);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [userLocal, setUserLocal] = useState(false);

  const togglePasswordVisibility = () => setIsPasswordVisible(!isPasswordVisible);

  const { data: roles } = useFetch(RoleApi.findAll());

  const roleComboxOptions = useMemo(() => {
    if (roles?.data) {
      return roles.data
        .filter((obj) => obj.active)
        .sort((a, b) => a.name.localeCompare(b.name))
        .map((obj) => ({ value: `${obj.id}`, label: obj.name }));
    }
    return [];
  }, [roles?.data]);

  const { getInputProps, isValid, reset, setFieldValue, values } = useForm<UserModel>({
    initialValues: { userName: '', roleIds: [], password: undefined, isUserLocal: false },
    validate: zodResolver(UserModelSchema),
    validateInputOnChange: true,
  });

  const { mutate: createUserMutate } = useMutate(UserApi.save, {
    successNotification: (res) => {
      return {
        title: 'Create User',
        message: `Create user ${res.data?.userName} Success`,
      };
    },

    onSuccess: () => {
      if (props.fetchListUsers) {
        props.fetchListUsers();
        resetValues();
      }
    },
  });

  const resetValues = () => {
    reset();
    setRolesSelected([]);
    setIsPasswordVisible(false);
    setUserLocal(false);
    closeModalCreateUser();
  };

  return (
    <>
      <KanbanButton
        onClick={() => {
          openModalCreateUser();
        }}
        leftSection={<IconPlus />}>
        Create User
      </KanbanButton>
      <Modal
        size={'xl'}
        withinPortal={true}
        opened={openedModalCreateUser}
        onClose={resetValues}
        title={'Create new'}
        actions={
          <KanbanButton
            disabled={!isValid()}
            onClick={() => {
              if (isValid()) {
                createUserMutate(UserModelSchema.parse(values));
                resetValues();
              }
            }}>
            Save
          </KanbanButton>
        }>
        <>
          <KanbanInput label='UserName' maxLength={100} required {...getInputProps('userName')} />
          <KanbanCheckbox
            pt={10}
            label='User local'
            onClick={(e) => {
              e.stopPropagation();
            }}
            onChange={(e) => {
              setUserLocal(e.target.checked);
              setFieldValue('isUserLocal', e.target.checked);
            }}
          />

          {userLocal && (
            <KanbanInput
              required
              label='Password'
              type={isPasswordVisible ? 'text' : 'password'}
              {...getInputProps('password')}
              rightSection={
                <KanbanIconButton onClick={togglePasswordVisibility} variant='transparent'>
                  {isPasswordVisible ? <IconEye /> : <IconEyeOff />}
                </KanbanIconButton>
              }
            />
          )}

          <KanbanMultiSelect
            label='Roles'
            searchable
            clearable
            data={roleComboxOptions}
            value={rolesSelected}
            onChange={(values) => {
              setFieldValue(
                'roleIds',
                values.map((obj) => Number(obj)),
              );
              setRolesSelected(values);
            }}
          />
        </>
      </Modal>
    </>
  );
};
