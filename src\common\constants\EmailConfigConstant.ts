import { EmailConfigPaginationRequest } from '@models/EmailConfigModel';
import { SortType } from './SortType';
import { EmailProtocolTypeEnum } from './EmailProtocolTypeConstant';

export const DEFAULT_EMAIL_CONFIG_PAGINATION_REQUEST: EmailConfigPaginationRequest = {
  page: 0,
  size: 10,
  sortBy: 'createdDate',
  sortOrder: SortType.DESC,
  email: '',
  search: '',
  protocolTypes: [EmailProtocolTypeEnum.EXCHANGE, EmailProtocolTypeEnum.IMAP],
  withInactived: false,
};
