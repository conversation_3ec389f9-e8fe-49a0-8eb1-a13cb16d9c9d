import React, { useState, useRef, useCallback, useMemo } from 'react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { ActionIcon, Box, Flex, Switch } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { IconEye, IconPlus, IconTrash } from '@tabler/icons-react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { IconEdit } from '@tabler/icons-react';
import { SortType } from '@common/constants/SortType';
import { PaginationRequest } from '@api/Type';
import useMutate from '@core/hooks/useMutate';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { createSearchParams, useNavigate } from 'react-router-dom';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { IconCopyPlus } from '@tabler/icons-react';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { DatabaseThresholdConfigApi } from '@api/DatabaseThresholdConfigApi';
import { BaseDatabaseThresholdConfig } from '@core/schema/DatabaseThresholdConfig';
import { DatabaseThresholdConfigAction } from '@common/constants/DatabaseThresholdConfigConstant';
import TruncatedText from '../modifyAlertConfig/TruncatedText';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';

export const DatabaseThresholdConfigPage = () => {
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST);

  const columns = useMemo<ColumnType<BaseDatabaseThresholdConfig>[]>(
    () => [
      {
        title: 'Config name',
        name: 'name',
      },
      {
        title: 'Description',
        name: 'description',
      },
      {
        title: 'Database connection',
        name: 'databaseConnectionName',
      },
      {
        title: 'Interval',
        name: 'cronTime',
      },
      {
        title: 'Service name',
        name: 'serviceName',
      },
      {
        title: 'Application name',
        name: 'applicationName',
      },
      {
        title: 'Priority',
        name: 'priorityConfigName',
      },
      {
        title: 'Contact',
        name: 'recipient',
      },
      {
        title: 'Alert content',
        name: 'contentJson',
        customRender: (record) => {
          return <TruncatedText text={record} isJson={true} />;
        },
      },
    ],
    [],
  );

  const navigate = useNavigate();
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  // API
  const { data: listConfig, refetch: refetchList } = useFetch(DatabaseThresholdConfigApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const { mutate: activeOrInactive } = useMutate(DatabaseThresholdConfigApi.activeOrInactive, {
    successNotification: 'Update status of config successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
  });
  const { mutate: deleteByIdMutate } = useMutate(DatabaseThresholdConfigApi.deleteById, {
    successNotification: 'Deleted successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });
  //Function update table affected
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<BaseDatabaseThresholdConfig>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const handleChangeURL = useCallback(
    (param: string, value: string | null) => {
      navigate({
        pathname: `${ROUTE_PATH.DATABASE_THRESHOLD}/${param}`,
        search: createSearchParams({
          action: value || DatabaseThresholdConfigAction.CREATE,
        }).toString(),
      });
    },
    [navigate],
  );

  const tableViewListRolesProps: KanbanTableProps<BaseDatabaseThresholdConfig> = useMemo(() => {
    return {
      columns: columns,
      data: listConfig?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listConfig?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <Switch
                checked={data.active}
                onClick={() => {
                  if (!isAnyPermissions([AclPermission.databaseThresholdConfigEdit])) {
                    return;
                  }
                  activeOrInactive(data.id);
                }}
              />
              <GuardComponent requirePermissions={[AclPermission.databaseThresholdConfigView]}>
                <KanbanTooltip label='View'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      handleChangeURL(data.id, DatabaseThresholdConfigAction.VIEW);
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.databaseThresholdConfigEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      handleChangeURL(data.id, DatabaseThresholdConfigAction.UPDATE);
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.databaseThresholdConfigCreate]}>
                <KanbanTooltip label='Clone'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      handleChangeURL(data.id, DatabaseThresholdConfigAction.COPY);
                    }}>
                    <IconCopyPlus />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.databaseThresholdConfigDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    activeOrInactive,
    columns,
    deleteByIdMutate,
    handleChangeURL,
    handleUpdateTablePagination,
    listConfig?.data?.content,
    listConfig?.data?.totalElements,
    tableAffected,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of database threshold config'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.databaseThresholdConfigCreate]}>
            <Flex direction='row' gap='xs' align='center'>
              <KanbanButton
                size='xs'
                onClick={() => {
                  handleChangeURL(DatabaseThresholdConfigAction.CREATE, DatabaseThresholdConfigAction.CREATE);
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} columns={columns} />
    </Box>
  );
};
export default DatabaseThresholdConfigPage;
