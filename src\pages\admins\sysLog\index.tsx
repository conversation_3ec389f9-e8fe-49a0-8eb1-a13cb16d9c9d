import { Sys<PERSON><PERSON><PERSON><PERSON> } from '@api/SysLogApi';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { dateToString } from '@common/utils/DateUtils';
import InfiniteScrollTable from '@components/dragTable/InfiniteScrollTable';
import { Column } from '@components/dragTable/Types';
import GuardComponent from '@components/GuardComponent';
import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import { SysLog } from '@core/schema/SysLog';
import { Box, Flex, Stack, Title } from '@mantine/core';
import { SysLogFilterModel } from '@models/SysLogFilterModel';
import { IconFileExport } from '@tabler/icons-react';
import dayjs from 'dayjs';
import { KanbanButton, KanbanText } from 'kanban-design-system';
import React, { useState } from 'react';
import SysLogFilterForm from './SysLogFilterForm';
import { formatActionLogLabel } from './Utils';
import { SysLogFunctionLabel } from '@common/constants/SysLogConstants';
import LogMessage from './LogMessage';
import { AclPermission } from '@models/AclPermission';
import { DateRangeTypeEnum } from '@components/dateRange/Constants';
import { omit } from 'lodash';

export const COLUMNS: Column<SysLog>[] = [
  {
    id: 'function',
    title: 'Function',
    render: (record) => <KanbanText fw={500}>{SysLogFunctionLabel[record.function]}</KanbanText>,
    width: '8%',
  },
  {
    id: 'logBy',
    title: 'Log by',
    render: 'logBy',
    width: '8%',
  },
  {
    id: 'logDate',
    title: 'Log Date',
    render: (record) => dateToString(record.logDate, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS),
    width: '150px',
  },
  {
    id: 'action',
    title: 'Action',
    render: (record) => <KanbanText fw={500}>{formatActionLogLabel(record.action)}</KanbanText>,
    width: '8%',
  },
  {
    id: 'message',
    title: 'Message',
    render: (record) => <LogMessage message={record.message} />,
  },
];

const DEFAULT_FILTER_VALUE: SysLogFilterModel = {
  fromDate: dayjs().startOf('date').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
  toDate: dayjs().endOf('date').format(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
  userNames: [],
  actions: [],
  functions: [],
  message: '',
  pageSize: 30,
  rangeType: DateRangeTypeEnum.TODAY,
};

const SysLogPage = () => {
  const [firstRender, setFirstRender] = useState(true);
  const [logFilter, setLogFilter] = useState<SysLogFilterModel>(DEFAULT_FILTER_VALUE);
  const { errorUpdateCount, fetchNextPage, flatData, isFetching, refetch } = useInfiniteCursorFetch(SysLogApi.findAll(omit(logFilter, 'rangeType')), {
    enabled: !firstRender,
  });
  return (
    <Box pos='relative' h='100%'>
      <SysLogFilterForm
        onSearch={(filterValue) => {
          setLogFilter(filterValue);
          refetch();
          setFirstRender(false);
        }}
        sysLogFilter={logFilter}
      />
      <Stack flex={1} h='100%'>
        <Flex justify='space-between'>
          <Title order={3} c='primary'>
            System Log
          </Title>
          <Flex align='center' gap='sm'>
            <GuardComponent requirePermissions={[AclPermission.syslogExport]}>
              <KanbanButton disabled={true} ml='sm' size='xs' variant='outline' dis leftSection={<IconFileExport />}>
                Export
              </KanbanButton>
            </GuardComponent>
          </Flex>
        </Flex>
        <Box flex={1} style={{ overflow: 'auto' }}>
          <InfiniteScrollTable
            columns={COLUMNS}
            data={flatData || []}
            onScrollToBottom={() => {
              if (errorUpdateCount < 1) {
                fetchNextPage();
              }
            }}
            loading={isFetching}
          />
        </Box>
      </Stack>
    </Box>
  );
};

export default SysLogPage;
