import { TaskUserTypeEnum } from '@common/constants/TaskConstants';
import { z } from 'zod';
import { createDateTimeSchema } from './Common';

export const TaskUserSchema = z.object({
  id: z.number(),
  taskId: z.number(),
  userName: z.string(),
  type: z.nativeEnum(TaskUserTypeEnum),
  createdDate: createDateTimeSchema(),
  createdBy: z.string(),
});

export type TaskUser = z.infer<typeof TaskUserSchema>;
