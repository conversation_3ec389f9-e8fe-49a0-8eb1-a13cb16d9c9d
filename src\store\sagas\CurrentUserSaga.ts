import { callRequest } from '@core/api';
import { ResponseData } from '@core/schema';
import { UserDetail } from '@core/schema/UserDetails';
import { currentUserSlice } from '@slices/CurrentUserSlice';
import { UserApi } from 'api';
import { call, put, takeEvery } from 'redux-saga/effects';

function* fetchData() {
  yield put(
    currentUserSlice.actions.setValue({
      isFetching: true,
    }),
  );
  try {
    const response: ResponseData<UserDetail> = yield call(() => callRequest(UserApi.me()));
    yield put(
      currentUserSlice.actions.setValue({
        isFetching: false,
        userInfo: response.data,
      }),
    );
  } catch (ex) {
    yield put(
      currentUserSlice.actions.setValue({
        isFetching: false,
      }),
    );
  }
}

export function* currentUserSaga() {
  yield takeEvery(currentUserSlice.actions.fetchData, fetchData);
}
