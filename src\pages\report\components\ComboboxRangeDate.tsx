import React, { useCallback, useState } from 'react';
import { Combobox, Grid, InputBase, useCombobox, Box, SimpleGrid, Stack } from '@mantine/core';
import { KanbanText } from 'kanban-design-system';
import { DateFilterOptionEnum, dateToString, getRangeDate } from '@common/utils/DateUtils';
import { AlertFormFilterModel } from '@models/AlertModel';
import dayjs, { Dayjs } from 'dayjs';
import DateTimePickerComponent from '@components/DateTimePickerComponent';
import { DATE_FORMAT } from '@common/constants/DateConstants';

type DateRangeFilterProps = {
  setFormData: React.Dispatch<React.SetStateAction<AlertFormFilterModel>>;
  toDate: Dayjs | undefined;
  fromDate: Dayjs | undefined;
  setDisableSearchButton: React.Dispatch<React.SetStateAction<boolean>>;
};

export const dateRangeOptions = [
  DateFilterOptionEnum.SINCE_SPECIFIC_DATE,
  DateFilterOptionEnum.BEFORE_SPECIFIC_DATE,
  DateFilterOptionEnum.BETWEEN_DATES,
];

const ERROR_VALIDATE_RANGE_DATE = 'From date cannot be greater than or equal to To date.';
const renderDatePickers = (
  selectedDateFilter: DateFilterOptionEnum,
  fromDate: Dayjs,
  toDate: Dayjs,
  updateFormDate: (field: keyof Pick<AlertFormFilterModel, 'fromDate' | 'toDate'>, value: Dayjs) => void,
) => {
  if (selectedDateFilter === DateFilterOptionEnum.BETWEEN_DATES) {
    return (
      <>
        <Box>
          <DateTimePickerComponent label='From date' onChange={(val) => updateFormDate('fromDate', val)} value={fromDate} />
        </Box>
        <Box>
          <DateTimePickerComponent label='To date' onChange={(val) => updateFormDate('toDate', val)} value={toDate} />
        </Box>
      </>
    );
  } else if (selectedDateFilter === DateFilterOptionEnum.SINCE_SPECIFIC_DATE) {
    return (
      <>
        <Stack justify='center'>
          <KanbanText>{selectedDateFilter}:</KanbanText>
        </Stack>
        <Box mt='sm'>
          <DateTimePickerComponent onChange={(val) => updateFormDate('fromDate', val)} value={fromDate} />
        </Box>
      </>
    );
  } else if (selectedDateFilter === DateFilterOptionEnum.BEFORE_SPECIFIC_DATE) {
    return (
      <>
        <Stack justify='center'>
          <KanbanText>{selectedDateFilter}:</KanbanText>
        </Stack>
        <Box mt='sm'>
          <DateTimePickerComponent onChange={(val) => updateFormDate('toDate', val)} value={toDate} />
        </Box>
      </>
    );
  }

  return null;
};

const RangeDateCombobox: React.FC<DateRangeFilterProps> = ({ fromDate, setDisableSearchButton: setDisabledSearchButton, setFormData, toDate }) => {
  const [selectedDateFilter, setSelectedDateFilter] = useState<DateFilterOptionEnum>(DateFilterOptionEnum.ALL_TIME);
  const [error, setError] = useState<string>('');

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });
  const handleValidateRangeDate = useCallback(
    (fromDate: Dayjs | undefined, toDate: Dayjs | undefined) => {
      if (fromDate?.isSame(toDate) || fromDate?.isAfter(toDate)) {
        setError(ERROR_VALIDATE_RANGE_DATE);
        setDisabledSearchButton(true);
      } else {
        setError('');
        setDisabledSearchButton(false);
      }
    },
    [setDisabledSearchButton],
  );
  const handleOptionSubmit = (val: string | null) => {
    const selectedFilter = val as DateFilterOptionEnum;
    const { fromDate, toDate } = getRangeDate(selectedFilter);
    handleValidateRangeDate(fromDate, toDate);
    setSelectedDateFilter(selectedFilter);
    setFormData((prev) => ({
      ...prev,
      fromDate: dateToString(fromDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
      toDate: dateToString(toDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
      rangeDate: selectedFilter,
    }));
    combobox.closeDropdown();
  };
  const updateFormDate = useCallback<(field: keyof Pick<AlertFormFilterModel, 'fromDate' | 'toDate'>, value: Dayjs) => void>(
    (field, value) => {
      setFormData((prev) => {
        const newData = { ...prev, [field]: dateToString(value, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS) };
        const { fromDate, toDate } = newData;
        handleValidateRangeDate(dayjs(fromDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS), dayjs(toDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS));
        return newData;
      });
    },
    [handleValidateRangeDate, setFormData],
  );

  return (
    <Box>
      <Combobox store={combobox} onOptionSubmit={handleOptionSubmit}>
        <Combobox.Target>
          <InputBase rightSection={<Combobox.Chevron />} component='button' label='Range date' onClick={() => combobox.toggleDropdown()}>
            {selectedDateFilter}
          </InputBase>
        </Combobox.Target>
        <Combobox.Dropdown>
          <Combobox.Options>
            {/* Presets Group */}
            <Combobox.Group label='Presets'>
              <Grid gutter='xs'>
                {Object.values(DateFilterOptionEnum)
                  .filter((option) => !dateRangeOptions.includes(option))
                  .map((option) => (
                    <Grid.Col span={6} key={option}>
                      <Combobox.Option value={option}>{option}</Combobox.Option>
                    </Grid.Col>
                  ))}
              </Grid>
            </Combobox.Group>

            {/* Date & Time Range Group */}
            <Combobox.Group label='Date & Time Range'>
              {dateRangeOptions.map((option) => (
                <Combobox.Option key={option} value={option}>
                  {option}
                </Combobox.Option>
              ))}
            </Combobox.Group>
          </Combobox.Options>
        </Combobox.Dropdown>
      </Combobox>
      {fromDate && toDate && (
        <SimpleGrid cols={2} spacing={0}>
          {renderDatePickers(selectedDateFilter, fromDate, toDate, updateFormDate)}
          {error && <KanbanText c='red'>{error}</KanbanText>}
        </SimpleGrid>
      )}
    </Box>
  );
};

export default RangeDateCombobox;
