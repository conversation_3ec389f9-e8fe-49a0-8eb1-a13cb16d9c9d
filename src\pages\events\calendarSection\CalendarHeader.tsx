import { ActionIcon, Button, Flex, Tabs, Title } from '@mantine/core';
import React, { useCallback, useContext } from 'react';
import { getCalendarTitle } from './Utils';
import { IconChevronLeft, IconChevronRight } from '@tabler/icons-react';
import { ViewModeEnum } from '../Contants';
import dayjs from 'dayjs';
import classes from './CalendarSection.module.css';
import { EventPageContext } from '../EventPageContext';

const CalendarHeader = () => {
  const { calendarMode, setCalendarMode } = useContext(EventPageContext);
  const onNext = useCallback(() => {
    const viewMode = calendarMode.viewMode;
    setCalendarMode({
      viewMode: viewMode,
      day: calendarMode.day.add(1, viewMode === ViewModeEnum.MONTH ? 'month' : 'year'),
      selectedDates: [],
    });
  }, [calendarMode.day, calendarMode.viewMode, setCalendarMode]);
  const onPrevious = useCallback(() => {
    const viewMode = calendarMode.viewMode;
    setCalendarMode({
      viewMode: viewMode,
      day: calendarMode.day.add(-1, viewMode === ViewModeEnum.MONTH ? 'month' : 'year'),
      selectedDates: [],
    });
  }, [calendarMode.day, calendarMode.viewMode, setCalendarMode]);
  const onTodayClick = useCallback(() => {
    const viewMode = calendarMode.viewMode;
    setCalendarMode({ viewMode: viewMode, day: dayjs(), selectedDates: [dayjs()] });
  }, [calendarMode.viewMode, setCalendarMode]);
  return (
    <Flex align='center' justify='space-between'>
      <Flex justify='center' gap='xs'>
        <ActionIcon size={30} variant='default' onClick={onPrevious}>
          <IconChevronLeft />
        </ActionIcon>
        <Title order={3} w={180} ta='center'>
          {getCalendarTitle(calendarMode)}
        </Title>
        <ActionIcon size={30} variant='default' onClick={onNext}>
          <IconChevronRight />
        </ActionIcon>
        <Button size='compact-md' variant='outline' onClick={onTodayClick}>
          Today
        </Button>
      </Flex>
      <Tabs value={calendarMode.viewMode} onChange={(value) => setCalendarMode({ viewMode: value as ViewModeEnum, day: dayjs(), selectedDates: [] })}>
        <Tabs.List>
          <Tabs.Tab value={ViewModeEnum.YEAR} className={classes.viewModeTitle}>
            Year
          </Tabs.Tab>
          <Tabs.Tab value={ViewModeEnum.MONTH} className={classes.viewModeTitle}>
            Month
          </Tabs.Tab>
        </Tabs.List>
      </Tabs>
    </Flex>
  );
};

export default CalendarHeader;
