import React, { useContext } from 'react';
import AlertTable from './AlertTable';
import { isEmpty } from 'lodash';
import AlertNavbarFilter from './AlertNavbarFilter';
import { Box, Center, Flex } from '@mantine/core';
import AlertHandle from './AlertHandle';
import { MonitorAlertPageContext, MonitorAlertPageProvider } from './MonitorAlertPageContext';

const RightSection = () => {
  const { selectedAlertGroups } = useContext(MonitorAlertPageContext);
  return (
    <Box bg='white' p='xs' h='100%' w={450}>
      {isEmpty(selectedAlertGroups) ? (
        <Center bg='var(--mantine-color-gray-light)' h='100%'>
          <Box bg='var(--mantine-color-blue-light)' p='xs' style={{ borderRadius: 'var(--mantine-radius-xs)' }}>
            No alert selected
          </Box>
        </Center>
      ) : (
        <AlertHandle />
      )}
    </Box>
  );
};

const MonitorAlertPage = () => {
  return (
    <MonitorAlertPageProvider>
      <Flex gap='xs' h='var(--kanban-appshell-maxheight-content)'>
        <AlertNavbarFilter />
        <Flex gap='xs' flex={1}>
          <Box bg='white' flex={1} style={{ overflow: 'auto' }}>
            <AlertTable />
          </Box>
          <RightSection />
        </Flex>
      </Flex>
    </MonitorAlertPageProvider>
  );
};

export default MonitorAlertPage;
