import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ActionIcon, Box, Flex, Tooltip } from '@mantine/core';
import React, { useCallback, useMemo, useState } from 'react';
import DragTable from '@components/dragTable/DragTable';
import useFetch from '@core/hooks/useFetch';
import { AlertGroupConfigApi } from '@api/AlertGroupConfigApi';
import useMutate from '@core/hooks/useMutate';
import { AlertGroupConfig } from '@core/schema/AlertGroupConfig';
import { Column, OnDragHandler } from '@components/dragTable/Types';
import { KanbanButton, KanbanIconButton, KanbanInput, KanbanSwitch } from 'kanban-design-system';
import { IconEdit, IconEye } from '@tabler/icons-react';
import { IconPlus } from '@tabler/icons-react';
import { createSearchParams, useNavigate } from 'react-router-dom';
import classes from './GroupConfigStyle.module.css';
import { ALERT_GROUP_CONFIG_TYPE_LABEL, ALERT_GROUP_OUTPUT_LABEL } from '@common/constants/AlertGroupConfigConstants';
import { sortBy } from 'lodash';
import { TABLE_INPUT_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { IconSearch, IconTrash } from '@tabler/icons-react';
import { useDebouncedState } from '@mantine/hooks';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { AclPermission } from '@models/AclPermission';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { AlertGroupConfigAction } from './Constants';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';

const ActionColumn = ({
  groupConfig,
  handleChangeURL,
  refetch,
}: {
  groupConfig: AlertGroupConfig;
  refetch: () => void;
  handleChangeURL: (param: number, value: string | null) => void;
}) => {
  const { mutate: deleteMutate } = useMutate(AlertGroupConfigApi.delete, {
    successNotification: 'Delete Alert Group Config success',
    errorNotification: 'Delete Alert Group Config failed!',
    confirm: getDefaultDeleteConfirmMessage(),
    onSuccess: () => refetch(),
  });
  const { mutate: updateActiveMutate } = useMutate(AlertGroupConfigApi.updateActive, {
    successNotification: `${groupConfig.active ? 'Inactive' : 'Active'} Alert Group Config success`,
    errorNotification: `${groupConfig.active ? 'Inactive' : 'Active'} Alert Group Config failed!`,
    onSuccess: () => refetch(),
  });
  return (
    <Flex gap='xs' align='center'>
      <GuardComponent requirePermissions={[AclPermission.alertGroupConfigEdit]}>
        <Tooltip label={groupConfig.active ? 'Active' : 'Inactive'}>
          <KanbanSwitch checked={groupConfig.active} onChange={(event) => updateActiveMutate({ id: groupConfig.id, active: event.target.checked })} />
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.alertGroupConfigView]}>
        <Tooltip label='View Config'>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              handleChangeURL(groupConfig.id, AlertGroupConfigAction.VIEW);
            }}>
            <IconEye />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.alertGroupConfigEdit]}>
        <Tooltip label='Edit Config'>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              handleChangeURL(groupConfig.id, AlertGroupConfigAction.UPDATE);
            }}>
            <IconEdit />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.alertGroupConfigDelete]}>
        <ActionIcon
          variant='transparent'
          color='red'
          onClick={() =>
            deleteMutate(groupConfig.id, {
              confirm: getDefaultDeleteConfirmMessage(groupConfig.name),
            })
          }>
          <IconTrash width={20} height={24} />
        </ActionIcon>
      </GuardComponent>
    </Flex>
  );
};

const GroupConfigPage = () => {
  const [searchInput, setSearchInput] = useState('');
  const [search, setSearch] = useDebouncedState(searchInput, DEFAULT_DEBOUNCE_TIME);
  const { data, isFetching, refetch } = useFetch(AlertGroupConfigApi.findAll({ withDeleted: false, search }));

  const navigate = useNavigate();
  const { mutate: updatePositionMutate } = useMutate(AlertGroupConfigApi.updatePosition, {
    successNotification: 'Update position success.',
    errorNotification: 'Update position failed!',
    onSuccess: () => refetch(),
    onError: () => refetch(),
  });

  const handleChangeURL = useCallback(
    (param: number, value: string | null) => {
      navigate({
        pathname: `${ROUTE_PATH.GROUP_CONFIG}/${param}`,
        search: createSearchParams({
          action: value || AlertGroupConfigAction.CREATE,
        }).toString(),
      });
    },
    [navigate],
  );

  const columns = useMemo<Column<AlertGroupConfig>[]>(
    () => [
      {
        id: 'name',
        title: 'Name',
        render: 'name',
        width: '15%',
      },
      {
        id: 'description',
        title: 'Description',
        render: (record) => record.description,
        width: '30%',
      },
      {
        id: 'type',
        title: 'Condition Type',
        render: (record) => ALERT_GROUP_CONFIG_TYPE_LABEL[record.type],
        width: '20%',
      },
      {
        id: 'alertOutput',
        title: 'Alert output',
        render: (record) => ALERT_GROUP_OUTPUT_LABEL[record.alertOutput],
        width: '20%',
      },
      {
        id: 'action',
        title: 'Action',
        render: (record) => <ActionColumn handleChangeURL={handleChangeURL} groupConfig={record} refetch={refetch} />,
      },
    ],
    [handleChangeURL, refetch],
  );

  const alertGroupConfigs = useMemo(() => sortBy(data?.data || [], 'position'), [data?.data]);
  const updatePositionHandler = useCallback<OnDragHandler<AlertGroupConfig>>(
    (activeConfig, overConfig) => {
      if (activeConfig.position !== overConfig.position) {
        updatePositionMutate({
          alertGroupConfigFromId: activeConfig.id,
          alertGroupConfigToId: overConfig.id,
          fromPosition: activeConfig.position,
          toPosition: overConfig.position,
        });
      }
    },
    [updatePositionMutate],
  );

  return (
    <Box className={classes.groupConfigWrapper}>
      <HeaderTitleComponent
        title='Alert Group Config'
        rightSection={
          <Flex gap='md'>
            <KanbanInput
              placeholder='Search'
              maxLength={TABLE_INPUT_MAX_LENGTH}
              leftSection={<IconSearch />}
              size='sm'
              value={searchInput}
              onChange={(event) => {
                setSearchInput(event.target.value);
                setSearch(event.target.value);
              }}
            />
            <GuardComponent requirePermissions={[AclPermission.alertGroupConfigCreate]}>
              <KanbanButton
                onClick={() => {
                  handleChangeURL(0, AlertGroupConfigAction.CREATE);
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <DragTable
        disableDraggable={!!search || isFetching || !isAnyPermissions([AclPermission.alertGroupConfigEdit])}
        columns={columns}
        data={alertGroupConfigs}
        onDragHandler={updatePositionHandler}
        showIndexColumn={false}
        dataKey={(record) => record.id}
      />
    </Box>
  );
};

export default GroupConfigPage;
