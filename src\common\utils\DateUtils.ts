import { RangeDateModel } from '@models/RangeDateModel';
import dayjs, { Dayjs } from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

// Constants
export const ISO_DATE_FORMAT = 'YYYY-MM-DDTHH:mm:ss';
export const DD_MM_YYYY_FORMAT = 'DD/MM/YYYY HH:mm';
export const maxDate = new Date(8640000000000000);
export const minDate = new Date(-8640000000000000);

export enum DateFilterOptionEnum {
  ALL_TIME = 'All time',
  TODAY = 'Today',
  YESTERDAY = 'Yesterday',
  PREVIOUS_WEEK = 'Previous week',
  PREVIOUS_MONTH = 'Previous month',
  LAST_15_MINUTES = 'Last 15 minutes',
  LAST_60_MINUTES = 'Last 60 minutes',
  LAST_4_HOURS = 'Last 4 hours',
  LAST_24_HOURS = 'Last 24 hours',
  LAST_7_DAYS = 'Last 7 days',
  LAST_30_DAYS = 'Last 30 days',
  SINCE_SPECIFIC_DATE = 'Since a specific date',
  BEFORE_SPECIFIC_DATE = 'Before a specific date',
  BETWEEN_DATES = 'Between two dates',
}

// Helper functions
export const getNowDate = (): Dayjs => dayjs();
export const getStartOfDate = (date: Dayjs): Dayjs => date.startOf('day');
export const getEndOfDate = (date: Dayjs): Dayjs => date.endOf('day');
export const getRangeDate = (dateFilterOption: DateFilterOptionEnum): RangeDateModel => {
  const now = getNowDate();
  const getStartOfPreviousWeek = (): Dayjs => getStartOfDate(dayjs()).subtract(dayjs().day() + 7, 'day');
  const getRelativeDate = (daysOffset: number): Dayjs => dayjs().add(daysOffset, 'day');
  switch (dateFilterOption) {
    case DateFilterOptionEnum.TODAY:
      return {
        fromDate: getStartOfDate(now),
        toDate: getEndOfDate(now),
      };
    case DateFilterOptionEnum.YESTERDAY:
      return {
        fromDate: getStartOfDate(getRelativeDate(-1)),
        toDate: getEndOfDate(getRelativeDate(-1)),
      };
    case DateFilterOptionEnum.PREVIOUS_WEEK:
      return {
        fromDate: getStartOfPreviousWeek(),
        toDate: getEndOfDate(getRelativeDate(-now.day() - 1)),
      };
    case DateFilterOptionEnum.PREVIOUS_MONTH:
      return {
        fromDate: now.subtract(1, 'month').startOf('month'),
        toDate: getEndOfDate(now.subtract(1, 'month').endOf('month')),
      };
    case DateFilterOptionEnum.LAST_15_MINUTES:
      return {
        fromDate: now.subtract(15, 'minute'),
        toDate: now,
      };
    case DateFilterOptionEnum.LAST_60_MINUTES:
      return {
        fromDate: now.subtract(60, 'minute'),
        toDate: now,
      };
    case DateFilterOptionEnum.LAST_4_HOURS:
      return {
        fromDate: now.subtract(4, 'hour'),
        toDate: now,
      };
    case DateFilterOptionEnum.LAST_24_HOURS:
      return {
        fromDate: now.subtract(24, 'hour'),
        toDate: now,
      };
    case DateFilterOptionEnum.LAST_7_DAYS:
      return {
        fromDate: now.subtract(7, 'day'),
        toDate: now,
      };
    case DateFilterOptionEnum.LAST_30_DAYS:
      return {
        fromDate: now.subtract(30, 'day'),
        toDate: now,
      };
    case DateFilterOptionEnum.SINCE_SPECIFIC_DATE:
    case DateFilterOptionEnum.BETWEEN_DATES:
      return {
        fromDate: getStartOfDate(now),
        toDate: getEndOfDate(now),
      };
    case DateFilterOptionEnum.ALL_TIME:
    case DateFilterOptionEnum.BEFORE_SPECIFIC_DATE:
      return {
        fromDate: dayjs(0),
        toDate: getEndOfDate(now),
      };
    default:
      return {
        fromDate: now,
        toDate: now,
      };
  }
};

// Utility functions
export function dateToString(value: any, format: string = DD_MM_YYYY_FORMAT) {
  return dayjs(value).format(format);
}

export function stringToDate(value: string) {
  try {
    const result = new Date(value);
    if (result instanceof Date && !isNaN(result.valueOf())) {
      return result;
    }
    return undefined;
  } catch (e) {
    return undefined;
  }
}

export function stringToDateWithFormat(value: string, format: string) {
  dayjs.extend(customParseFormat);
  try {
    const result = dayjs(value, format).toDate();
    if (result instanceof Date && !isNaN(result.valueOf())) {
      return result;
    }
    return undefined;
  } catch (e) {
    return undefined;
  }
}

export function getAllFormatDateAtExcel() {
  const dateFormats: string[] = [
    'yyyy/MM/dd',
    'yyyy.MM.dd',
    'yyyy-MM-dd',
    'dd-MM-yyyy',
    'dd/MM/yyyy',
    'dd.MM.yyyy',
    'MM-dd-yyyy',
    'MM/dd/yyyy',
    'MM.dd.yyyy',
  ];

  return dateFormats;
}
