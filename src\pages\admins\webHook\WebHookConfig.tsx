import { <PERSON><PERSON><PERSON> } from '@api/ApplicationApi';
import { ServiceApi } from '@api/ServiceApi';
import { PaginationRequest } from '@api/Type';
import { WEBHOOK_DATA_TYPE, WEBHOOK_FIELD_TYPE } from '@common/constants/WebHookConstant';
import useFetch from '@core/hooks/useFetch';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { ComboboxItem, SimpleGrid } from '@mantine/core';
import { WebHookModel } from '@models/WebHookModel';
import { WebHookApi } from 'api/WebHookApi';
import { KanbanInput, KanbanSelect, KanbanText } from 'kanban-design-system';
import React, { useEffect, useMemo, useState } from 'react';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import { sortBy } from 'lodash';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationContant';
import { SelectWithPage } from '@components/SelectWithPage';
import { CHARACTER_CONTENT_ALERT_MAX_LENGTH, MAX_RECIPIENT_LENGTH } from '@common/constants/ValidationConstant';

type WebHookConfigProps = {
  id?: number;
  webHook: WebHookModel;
  setWebHook: React.Dispatch<React.SetStateAction<WebHookModel>>;
  isViewMode?: boolean;
};

const DEFAULT_PAGINATION_WEBHOOK_REQUEST: PaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortBy: 'name',
};

const WebHookConfig = ({ id, isViewMode, setWebHook, webHook }: WebHookConfigProps) => {
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_PAGINATION_WEBHOOK_REQUEST);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_PAGINATION_WEBHOOK_REQUEST);
  const [serviceIds, setServiceIds] = useState<string[]>([]);
  const [serviceSelected, setServiceSelected] = useState<ComboboxItem | undefined>(undefined);
  const [applicationSelected, setApplicationSelected] = useState<ComboboxItem | undefined>(undefined);

  // find by id
  const { data: dataDetail } = useFetch(WebHookApi.findById(id || 0), {
    enabled: !!id,
  });

  //load when edit webhook and webhook setting custom service
  const { data: service } = useFetch(ServiceApi.findById(dataDetail?.data?.serviceId || ''), {
    enabled: !!dataDetail?.data?.serviceId,
  });
  const { data: application } = useFetch(ApplicationApi.findById(dataDetail?.data?.applicationId || ''), {
    enabled: !!dataDetail?.data?.applicationId,
  });
  const { data: priorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });
  const {
    fetchNextPage: fetchNextPageService,
    flatData: services,
    isFetching: isServiceFetching,
  } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
  });
  const {
    fetchNextPage: fetchNextPageApplication,
    flatData: applications,
    isFetching: isApplicationFetching,
  } = useInfiniteFetch(ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds }), {
    showLoading: false,
    enabled: serviceIds.length > 0,
  });

  const serviceComboxOptions = useMemo(() => {
    return services.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [services]);

  const applicationComboxOptions = useMemo(() => {
    return applications.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [applications]);

  const priorityConfigOptions = useMemo(() => {
    return sortBy(
      priorityConfigs?.data?.map((config) => ({ value: `${config.id}`, label: config.name + (config.deleted ? ' (DELETED)' : '') })) || [],
      (option) => option.label.toLowerCase(),
    );
  }, [priorityConfigs?.data]);

  useEffect(() => {
    if (service?.data) {
      setServiceSelected({ value: `${service.data.id}`, label: service.data.name });
      setServiceIds([service.data.id]);
    }
    if (application?.data) {
      setApplicationSelected({ value: `${application.data.id}`, label: application.data.name });
    }
  }, [service?.data, application?.data]);

  useEffect(() => {
    if (dataDetail?.data) {
      setWebHook({ ...dataDetail?.data, alertPriorityConfigId: `${dataDetail?.data.alertPriorityConfigId || ''}` });
    }
  }, [dataDetail?.data, setWebHook]);

  const comboboxFieldType: ComboboxItem[] = useMemo(() => {
    return Object.keys(WEBHOOK_FIELD_TYPE).map((key) => ({
      value: key,
      label: key,
    }));
  }, []);

  const comboboxDataType: ComboboxItem[] = useMemo(() => {
    return Object.keys(WEBHOOK_DATA_TYPE).map((key) => ({
      value: key,
      label: key,
    }));
  }, []);

  return (
    <>
      <KanbanText>Source Alert Information</KanbanText>
      <SimpleGrid cols={2}>
        <KanbanInput
          required
          maxLength={40}
          label='Name:'
          disabled={isViewMode || !!id}
          value={webHook.name}
          onChange={(data) =>
            setWebHook((prev) => ({
              ...prev,
              name: data.target.value,
            }))
          }
          onBlur={() => {
            setWebHook((prev) => ({
              ...prev,
              name: prev.name?.trim(),
            }));
          }}
        />
        <KanbanSelect
          disabled={isViewMode}
          required
          data={comboboxDataType}
          label='Data Type:'
          value={webHook.dataType}
          onChange={(data) => {
            if (data) {
              setWebHook((prev) => ({
                ...prev,
                dataType: data,
              }));
            }
          }}
        />
      </SimpleGrid>
      <KanbanText>Config Alert</KanbanText>
      <SimpleGrid cols={2}>
        <KanbanSelect
          disabled={isViewMode}
          required
          maxLength={40}
          data={comboboxFieldType}
          value={webHook.serviceNameType}
          onChange={(value) => {
            if (value) {
              setWebHook((prev) => ({
                ...prev,
                serviceNameType: value,
                applicationType: value,
                serviceId: undefined,
                applicationId: undefined,
                applicationMapValue: undefined,
              }));

              setServiceSelected(undefined);
              setApplicationSelected(undefined);
            }
          }}
          label='Service Name:'
        />

        {webHook.serviceNameType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
          <SelectWithPage
            disabled={isViewMode}
            label='Service Name'
            required={true}
            options={serviceComboxOptions}
            handleScrollToBottom={fetchNextPageService}
            onChange={(_, data) => {
              const valueId = data?.value.toString() || undefined;
              setApplicationSelected(undefined);
              setServiceSelected(data);
              setWebHook((prev) => ({
                ...prev,
                serviceId: valueId,
                serviceMapValue: undefined,
                applicationMapValue: undefined,
                applicationId: undefined,
              }));
              setServiceIds(valueId ? [valueId] : []);
            }}
            onSearch={(val) => {
              setServiceSearchParams((prev) => ({ ...prev, page: 0, search: val }));
            }}
            onBlur={() => {
              setServiceSearchParams(DEFAULT_PAGINATION_WEBHOOK_REQUEST);
            }}
            value={serviceSelected}
            isLoading={isServiceFetching}
          />
        ) : (
          <KanbanInput
            required
            maxLength={40}
            disabled={isViewMode}
            label='Service Name:'
            placeholder='$.serviceName'
            value={webHook.serviceMapValue}
            onChange={(data) => {
              setWebHook((prev) => ({
                ...prev,
                serviceMapValue: data.target.value.trim(),
                serviceId: undefined,
                applicationId: undefined,
              }));
            }}
          />
        )}
      </SimpleGrid>
      <SimpleGrid cols={2}>
        <KanbanSelect
          required
          defaultValue={WEBHOOK_FIELD_TYPE.FROM_SOURCE}
          data={comboboxFieldType}
          disabled={true}
          value={webHook.applicationType}
          label='Application Name:'
        />
        {webHook.applicationType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
          <SelectWithPage
            disabled={isViewMode || serviceIds.length === 0}
            label='Application Name'
            required={true}
            options={applicationComboxOptions}
            handleScrollToBottom={fetchNextPageApplication}
            onChange={(_, data) => {
              const id = data?.value.toString() || undefined;
              setApplicationSelected(data);
              setWebHook((prev) => ({
                ...prev,
                applicationId: id,
                applicationMapValue: undefined,
              }));
            }}
            onSearch={(val) => {
              setApplicationSearchParams((prev) => ({ ...prev, page: 0, search: val }));
            }}
            onBlur={() => {
              setApplicationSearchParams(DEFAULT_PAGINATION_WEBHOOK_REQUEST);
            }}
            value={applicationSelected}
            isLoading={isApplicationFetching}
          />
        ) : (
          <KanbanInput
            disabled={isViewMode}
            required
            maxLength={40}
            label='Application Name:'
            placeholder='$.application'
            value={webHook.applicationMapValue || ''}
            onChange={(data) =>
              setWebHook((prev) => ({
                ...prev,
                applicationMapValue: data.target.value.trim(),
                applicationId: undefined,
              }))
            }
          />
        )}
      </SimpleGrid>
      <SimpleGrid cols={2}>
        <KanbanSelect
          disabled={isViewMode}
          required
          value={webHook.alertContentType}
          onChange={(value) => {
            if (value) {
              setWebHook((prev) => ({
                ...prev,
                alertContentType: value,
                alertContentCustomValue: undefined,
                alertContentMapValue: undefined,
              }));
            }
          }}
          data={comboboxFieldType}
          label='Alert Content:'
        />
        {webHook.alertContentType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
          <KanbanInput
            disabled={isViewMode}
            required
            maxLength={CHARACTER_CONTENT_ALERT_MAX_LENGTH}
            label='Alert Content:'
            value={webHook.alertContentCustomValue || ''}
            onChange={(data) =>
              setWebHook((prev) => ({
                ...prev,
                alertContentCustomValue: data.target.value,
                alertContentMapValue: undefined,
              }))
            }
            onBlur={() => {
              setWebHook((prev) => ({
                ...prev,
                alertContentCustomValue: prev.alertContentCustomValue?.trim(),
              }));
            }}
          />
        ) : (
          <KanbanInput
            disabled={isViewMode}
            required
            label='Alert Content:'
            placeholder='$.alert'
            maxLength={40}
            value={webHook.alertContentMapValue || ''}
            onChange={(data) =>
              setWebHook((prev) => ({
                ...prev,
                alertContentMapValue: data.target.value.trim(),
                alertContentCustomValue: undefined,
              }))
            }
          />
        )}
      </SimpleGrid>
      <SimpleGrid cols={2}>
        <KanbanSelect
          disabled={isViewMode}
          required
          data={comboboxFieldType}
          value={webHook.priorityType}
          onChange={(value) => {
            if (value) {
              setWebHook((prev) => ({
                ...prev,
                priorityType: value,
                priorityCustomValue: undefined,
                priorityMapValue: undefined,
              }));
            }
          }}
          label='Priority:'
        />
        {webHook.priorityType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
          <KanbanSelect
            required
            disabled={isViewMode}
            data={priorityConfigOptions}
            label='Priority:'
            value={webHook.alertPriorityConfigId}
            autoChangeValueByOptions={false}
            onChange={(data) => {
              setWebHook((prev) => ({
                ...prev,
                alertPriorityConfigId: data || undefined,
                priorityMapValue: undefined,
              }));
            }}
          />
        ) : (
          <KanbanInput
            disabled={isViewMode}
            required
            maxLength={40}
            label='Priority:'
            placeholder='$.priority'
            value={webHook.priorityMapValue || ''}
            onChange={(data) =>
              setWebHook((prev) => ({
                ...prev,
                priorityMapValue: data.target.value.trim(),
                priorityCustomValue: undefined,
              }))
            }
          />
        )}
      </SimpleGrid>
      <SimpleGrid cols={2}>
        <KanbanSelect
          required
          disabled={isViewMode}
          data={comboboxFieldType}
          value={webHook.contactType}
          onChange={(value) => {
            if (value) {
              setWebHook((prev) => ({
                ...prev,
                contactType: value,
                contactCustomValue: undefined,
                contactMapValue: undefined,
              }));
            }
          }}
          label='Contact:'
        />
        {webHook.contactType === WEBHOOK_FIELD_TYPE.CUSTOM ? (
          <KanbanInput
            disabled={isViewMode}
            required
            maxLength={MAX_RECIPIENT_LENGTH}
            label='Contact:'
            value={webHook.contactCustomValue || ''}
            onChange={(data) =>
              setWebHook((prev) => ({
                ...prev,
                contactCustomValue: data.target.value,
                contactMapValue: undefined,
              }))
            }
          />
        ) : (
          <KanbanInput
            disabled={isViewMode}
            required
            maxLength={40}
            label='Contact:'
            placeholder='$.contact'
            value={webHook.contactMapValue || ''}
            onChange={(data) =>
              setWebHook((prev) => ({
                ...prev,
                contactMapValue: data.target.value.trim(),
                contactCustomValue: undefined,
              }))
            }
          />
        )}
      </SimpleGrid>
    </>
  );
};
export default WebHookConfig;
