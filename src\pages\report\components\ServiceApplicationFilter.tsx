import React, { useMemo, useState } from 'react';
import ComboboxLoadMore from '../../../components/ComboboxLoadMore';
import { ApplicationApi, ServiceApi } from 'api';
import { Application, Service } from '@core/schema';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { ApplicationPaginationRequest } from '@models/ApplicationModel';
import { ServicePaginationRequest } from '@models/ServiceModel';
import { formatSelectCountMessage } from '@common/utils/MessageUtils';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationContant';
import { AlertFormFilterModel } from '@models/AlertModel';
import { SetFieldValueFn } from '@common/utils/Type';
import { uniqBy } from 'lodash';

const DEFAULT_APPLICATION_PAGINATION_REQUEST: ApplicationPaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortBy: 'name',
  name: '',
  withDeleted: true,
};

const DEFAULT_SERVICE_PAGINATION_REQUEST: ServicePaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortBy: 'name',
  withDeleted: true,
};

interface Props {
  alertFormFilter: AlertFormFilterModel;
  updateAlertFormFilter: SetFieldValueFn<AlertFormFilterModel>;
}

const ServiceApplicationFilter = ({ alertFormFilter, updateAlertFormFilter }: Props) => {
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_SERVICE_PAGINATION_REQUEST);
  const [applicationSearchParams, setApplicationSearchParams] = useState(DEFAULT_APPLICATION_PAGINATION_REQUEST);
  const [selectedServices, setSelectedServices] = useState<Service[]>([]);
  const [selectedApplications, setSelectedApplications] = useState<Application[]>([]);
  const { fetchNextPage: fetchNextPageService, flatData: services } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
  });
  const { fetchNextPage: fetchNextPageApplication, flatData: applications } = useInfiniteFetch(
    ApplicationApi.findAllByServiceIdIn({ ...applicationSearchParams, serviceIds: alertFormFilter.services?.map((ele) => ele.id) }),
    {
      showLoading: false,
    },
  );

  const handleChangeServiceValue = (values: Service[]) => {
    setSelectedServices(values);
    updateAlertFormFilter('services', values || []);
    if (!values?.length) {
      setApplicationSearchParams(DEFAULT_APPLICATION_PAGINATION_REQUEST);
    }
  };

  const handleChangeApplicationValue = (values: Application[]) => {
    setSelectedApplications(values);
    updateAlertFormFilter('applications', values || []);
  };

  const serviceOptions = useMemo(() => uniqBy([...services, ...selectedServices], (ele) => ele.id), [selectedServices, services]);
  const applicationOptions = useMemo(() => uniqBy([...applications, ...selectedApplications], (ele) => ele.id), [applications, selectedApplications]);

  return (
    <>
      <ComboboxLoadMore
        options={serviceOptions}
        onChange={handleChangeServiceValue}
        label='Service Name'
        placeholder='Search service name'
        onSearch={(val) => setServiceSearchParams((prev) => ({ ...prev, name: val }))}
        onScroll={fetchNextPageService}
        onClickedOption={() => setServiceSearchParams(DEFAULT_SERVICE_PAGINATION_REQUEST)}
        renderPillLabel={(data) => `(${data.id}) ${data.name}`}
        renderOptionLabel={(data) => `(${data.id}) ${data.name}`}
        values={alertFormFilter.services || []}
        scrollableForValue={true}
        description={formatSelectCountMessage(alertFormFilter?.services?.length || 0)}
      />
      <ComboboxLoadMore
        options={applicationOptions}
        onChange={handleChangeApplicationValue}
        label='Application Name'
        placeholder='Search application name'
        onSearch={(val) => setApplicationSearchParams((prev) => ({ ...prev, name: val }))}
        onClickedOption={() => setApplicationSearchParams(DEFAULT_APPLICATION_PAGINATION_REQUEST)}
        onScroll={fetchNextPageApplication}
        renderPillLabel={(data) => `(${data.serviceId}) ${data.serviceName}: (${data.id}) ${data.name}`}
        renderOptionLabel={(data) => `(${data.id}) ${data.name}`}
        values={alertFormFilter?.applications || []}
        groupByKeys={['serviceName']}
        scrollableForValue={true}
        description={formatSelectCountMessage(alertFormFilter?.applications?.length || 0)}
      />
    </>
  );
};

export default ServiceApplicationFilter;
