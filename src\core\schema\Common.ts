import { DATE_FORMAT, DateFormat } from '@common/constants/DateConstants';
import dayjs from 'dayjs';
import { z } from 'zod';

export function createDateTimeSchema(dateTimeFormat: DateFormat = DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS) {
  return z
    .custom<dayjs.Dayjs>((value) => {
      if (value === undefined || value === null) {
        return false;
      }
      return dayjs(value, dateTimeFormat).isValid();
    }, `The date is not in the correct format ${dateTimeFormat}`)
    .transform((value) => dayjs(value, dateTimeFormat, true));
}

export function createPageSchema<T>(schema: z.ZodType<T>) {
  return z.object({
    totalPages: z.number(),
    totalElements: z.number(),
    size: z.number(),
    content: z.array(schema),
    number: z.number(),
    sort: z.object({
      empty: z.boolean(),
      sorted: z.boolean(),
      unsorted: z.boolean(),
    }),
    first: z.boolean(),
    last: z.boolean(),
    numberOfElements: z.number(),
    pageable: z.object({
      pageNumber: z.number(),
      pageSize: z.number(),
      sort: z.object({
        empty: z.boolean(),
        sorted: z.boolean(),
        unsorted: z.boolean(),
      }),
      offset: z.number(),
      unpaged: z.boolean(),
      paged: z.boolean(),
    }),
    empty: z.boolean(),
  });
}

export function createResponseSchema<T>(schema: z.ZodType<T>) {
  return z.object({
    status: z.number().nullable().optional(),
    path: z.string().nullable().optional(),
    clientMessageId: z.string().nullable().optional(),
    errorCode: z.string().nullable().optional(),
    errorDescription: z.string().nullable().optional(),
    data: schema.optional(),
  });
}

export function createCursorPageSchema<T, C>(schema: z.ZodType<T>, cursor: z.ZodType<C>) {
  return z.object({
    data: z.array(schema).optional(),
    nextCursor: cursor.optional(),
  });
}

export type ResponseData<T> = z.infer<ReturnType<typeof createResponseSchema<T>>>;

export type Page<T> = z.infer<ReturnType<typeof createPageSchema<T>>>;

export type CursorPage<T, C> = z.infer<ReturnType<typeof createCursorPageSchema<T, C>>>;
