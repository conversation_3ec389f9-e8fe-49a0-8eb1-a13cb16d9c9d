import React, { use<PERSON>emo } from 'react';
import { Flex, ComboboxData, ActionIcon, Box, Stack, Title } from '@mantine/core';
import { Controller, useFieldArray, UseFormReturn, useWatch } from 'react-hook-form';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { ModifyAlertConfigModel } from '@models/ModifyAlertConfigModel';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import useFetch from '@core/hooks/useFetch';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import { FIELD_LABEL_MAP } from './Constants';
import { CustomContentAlert } from '../collectEmail/customContentAlert/CustomContentAlert';
import { CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH } from '@common/constants/ValidationConstant';

interface Props {
  form: UseFormReturn<ModifyAlertConfigModel>;
  isViewMode: boolean;
}

const ModifyField = ({ form, isViewMode }: Props) => {
  const { control, getValues, setValue } = form;
  const { fields, insert, remove } = useFieldArray({ control, name: 'modifies' });

  const modifiesWatch = useWatch({
    control,
    name: 'modifies',
  });

  const { data: priorityData } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });

  const priorityOptions = useMemo<ComboboxData>(
    () =>
      priorityData?.data?.map((priority) => ({
        value: String(priority.id),
        label: priority.name,
      })) || [],
    [priorityData?.data],
  );

  const availableFieldOptions = useMemo(() => {
    return Object.entries(FIELD_LABEL_MAP).map(([value, label]) => ({
      value,
      label,
      disabled: modifiesWatch.some((field) => field.fieldName === value),
    }));
  }, [modifiesWatch]);

  return (
    <Flex direction='column' gap='sm'>
      <Stack>
        {fields.map((group, index) => {
          const fieldName = modifiesWatch?.[index]?.fieldName;
          return (
            <Box key={group.id} style={{ border: '1px solid var(--mantine-color-default-border)' }} p='md'>
              <Flex justify='space-between' mb='xs'>
                <Title order={6}>{`Modify Field ${index + 1}`}</Title>
                <Flex align='center' gap='md'>
                  {fields.length < availableFieldOptions.length && !isViewMode && (
                    <ActionIcon onClick={() => insert(index + 1, { fieldName: '', fieldValue: '' })} variant='outline'>
                      <IconPlus />
                    </ActionIcon>
                  )}
                  {fields.length !== 1 && !isViewMode && (
                    <ActionIcon disabled={isViewMode} onClick={() => remove(index)}>
                      <IconTrash />
                    </ActionIcon>
                  )}
                </Flex>
              </Flex>
              <Controller
                name={`modifies.${index}.fieldName`}
                control={control}
                render={({ field: { onChange, value } }) => (
                  <KanbanSelect
                    label='Field Name'
                    placeholder='Select field'
                    data={availableFieldOptions}
                    value={value}
                    onChange={(selectedValue) => {
                      onChange(selectedValue);
                      setValue(`modifies.${index}.fieldValue`, '');
                      setValue(`modifies.${index}.contentHtml`, '');
                    }}
                    disabled={isViewMode}
                  />
                )}
              />
              <Controller
                name={`modifies.${index}.fieldValue`}
                control={control}
                render={({ field: { onChange, value } }) =>
                  fieldName === 'priority' ? (
                    <KanbanSelect
                      label='Priority'
                      placeholder='Select priority'
                      data={priorityOptions}
                      value={value}
                      onChange={onChange}
                      disabled={isViewMode}
                    />
                  ) : fieldName === 'content' ? (
                    <CustomContentAlert
                      label='Alert content'
                      value={getValues(`modifies.${index}.contentHtml`) || '{}'}
                      disabled={isViewMode}
                      onChange={(content, val) => {
                        onChange(val);
                        setValue(`modifies.${index}.contentHtml`, content);
                      }}
                    />
                  ) : (
                    <KanbanInput
                      label='Alert contact'
                      value={value}
                      onChange={(event) => onChange(event.currentTarget.value)}
                      maxLength={CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH}
                      placeholder='Enter field value'
                      disabled={isViewMode}
                    />
                  )
                }
              />
            </Box>
          );
        })}
      </Stack>
    </Flex>
  );
};

export default ModifyField;
