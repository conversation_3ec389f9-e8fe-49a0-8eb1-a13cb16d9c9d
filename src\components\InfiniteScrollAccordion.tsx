import React, { useCallback, useRef, useState } from 'react';
import { Accordion, ScrollArea, Stack, Loader, Center } from '@mantine/core';
import { useDebounceCallback } from 'kanban-design-system';

interface InfiniteScrollAccordionProps<T> {
  data: T[];
  renderAccordionLabel: (item: T, index: number) => React.ReactNode;
  renderAccordionContent: (item: T, index: number) => React.ReactNode;
  loading?: boolean;
  onScrollToBottom: () => void;
  onChange?: (item: T, index: number) => void;
}
export const DEFAULT_DROPDOWN_SCROLL_THRESHOLD = 20;
export const DEFAULT_DEBOUNCE_TIME = 100;

function InfiniteScrollAccordion<T>({
  data,
  loading = false,
  onChange,
  onScrollToBottom,
  renderAccordionContent,
  renderAccordionLabel,
}: InfiniteScrollAccordionProps<T>) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

  const handleAccordionChange = useCallback(
    (value: string | null) => {
      if (value) {
        const index = parseInt(value);
        setSelectedIndex(index);
        if (onChange) {
          const item = data[index];
          onChange(item, index);
        }
      } else {
        setSelectedIndex(null);
      }
    },
    [data, onChange],
  );

  const debouncedScroll = useDebounceCallback(onScrollToBottom, DEFAULT_DEBOUNCE_TIME);

  const handleScrollPositionChange = (position: { x: number; y: number }) => {
    if (scrollAreaRef?.current) {
      const { clientHeight, scrollHeight } = scrollAreaRef.current;
      const maxScrollPosition = scrollHeight - clientHeight;
      if (position.y >= maxScrollPosition - DEFAULT_DROPDOWN_SCROLL_THRESHOLD) {
        debouncedScroll();
      }
    }
  };

  return (
    <Stack mih='var(--kanban-appshell-maxheight-content)' bg='white' w='15vw' miw='15vw'>
      <ScrollArea.Autosize
        mah='var(--kanban-appshell-maxheight-content)'
        type='scroll'
        viewportRef={scrollAreaRef}
        onScrollPositionChange={handleScrollPositionChange}>
        <Accordion value={selectedIndex?.toString() || null} onChange={handleAccordionChange}>
          {data.map((item, index) => {
            return (
              <Accordion.Item key={index} value={index.toString()}>
                <Accordion.Control
                  w='15vw'
                  miw='15vw'
                  style={selectedIndex === index ? { backgroundColor: 'var(--mantine-primary-color-1)' } : undefined}>
                  {renderAccordionLabel(item, index)}
                </Accordion.Control>
                <Accordion.Panel style={selectedIndex === index ? { backgroundColor: 'var(--mantine-primary-color-1)' } : undefined}>
                  {renderAccordionContent(item, index)}
                </Accordion.Panel>
              </Accordion.Item>
            );
          })}
        </Accordion>
        {loading && (
          <Center mt='md'>
            <Loader size='sm' />
          </Center>
        )}
      </ScrollArea.Autosize>
    </Stack>
  );
}

export default InfiniteScrollAccordion;
