import { <PERSON><PERSON><PERSON> } from '@api/RoleApi';
import { PERMISSION_ACTION_LABEL } from '@common/constants/PermissionAction';
import { PERMISSION_MODULE_LABEL, PermissionModuleEnum } from '@common/constants/PermissionModule';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import Table from '@components/table';
import useMutate from '@core/hooks/useMutate';
import { Permission } from '@core/schema/Permission';
import { Role } from '@core/schema/Role';
import { UserDetail } from '@core/schema/UserDetails';
import { ActionIcon, Pill } from '@mantine/core';
import { IconTrash } from '@tabler/icons-react';
import { KanbanTableProps, KanbanText } from 'kanban-design-system';
import { groupBy } from 'lodash';
import React, { useMemo } from 'react';

type RoleOfUserProps = {
  user?: UserDetail;
  fetchUserDetail: () => void;
};

const columns = [
  {
    title: 'Role Name',
    name: 'name',
    customRender: (data: string) => {
      return (
        <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
          {data}
        </KanbanText>
      );
    },
  },
  {
    title: 'Permissions',
    name: 'permissions',
    customRender: (permissions: Permission[]) => {
      const groupedByModule = groupBy(permissions, 'module');
      return (
        <Pill.Group>
          {Object.entries(groupedByModule).map(([module, actions], index) => (
            <Pill key={index}>
              {PERMISSION_MODULE_LABEL[module as PermissionModuleEnum]} ({actions.map((action) => PERMISSION_ACTION_LABEL[action.action]).join(', ')})
            </Pill>
          ))}
        </Pill.Group>
      );
    },
  },
];

export const RoleOfUser = (props: RoleOfUserProps) => {
  const { fetchUserDetail, user } = props;
  const { mutate: deleteRoleFromUserMutate } = useMutate(RoleApi.deleteRoleFromUser, {
    successNotification: () => {
      return {
        title: 'Delete',
        message: `Delete role from user Success`,
      };
    },
    onSuccess: () => {
      fetchUserDetail();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const tableProps: KanbanTableProps<Role> = useMemo(() => {
    return {
      searchable: {
        enable: true,
      },
      sortable: {
        enable: true,
      },

      showNumericalOrderColumn: true,
      columns: columns,
      data: user?.roles || [],
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <ActionIcon
              variant='transparent'
              color='red'
              onClick={() =>
                deleteRoleFromUserMutate(
                  { roleId: data.id, userId: user?.id || 0 },
                  {
                    confirm: getDefaultDeleteConfirmMessage(data.name),
                  },
                )
              }>
              <IconTrash width={20} height={24} />
            </ActionIcon>
          );
        },
      },
    };
  }, [deleteRoleFromUserMutate, user?.id, user?.roles]);

  return <Table {...tableProps} />;
};
