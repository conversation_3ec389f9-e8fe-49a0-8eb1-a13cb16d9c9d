import { z } from 'zod';
import { ExportFileStatusEnum, ExportFileTypeEnum } from '@common/constants/ExportFileTypeConstants';
import { createDateTimeSchema } from '@core/schema';
import { DATE_FORMAT } from '@common/constants/DateConstants';

export const ExportDataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().trim(),
  status: z.nativeEnum(ExportFileStatusEnum),
  extension: z.nativeEnum(ExportFileTypeEnum),
  exportedBy: z.string(),
  createdDate: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS),
});

export type ExportData = z.infer<typeof ExportDataSchema>;
