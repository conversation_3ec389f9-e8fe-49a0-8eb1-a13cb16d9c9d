import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { ActionIcon, Box, ComboboxData, Flex, Paper, Stack, Transition } from '@mantine/core';
import { KanbanButton, KanbanInput, KanbanMultiSelect, KanbanNumberInput, KanbanTooltip } from 'kanban-design-system';
import { IconChevronLeft, IconSearch, IconSelectAll, IconX } from '@tabler/icons-react';
import type { AlertFormFilterModel } from '@models/AlertModel';
import { SetFieldValueFn } from '@common/utils/Type';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import useFetch from '@core/hooks/useFetch';
import { sortBy } from 'lodash';
import { useDisclosure } from '@mantine/hooks';
import { IconChevronRight } from '@tabler/icons-react';
import {
  AlertDurationCompareOperatorEnum,
  AlertDurationCompareUnitEnum,
  MAX_ALERT_GROUP_ID,
  MAX_LENGTH_CHARACTER,
  MAX_LENGTH_DESCRIPTION,
} from '../Constants';
import RangeDateCombobox from './ComboboxRangeDate';
import ServiceApplicationFilter from './ServiceApplicationFilter';
import { AlertStatusEnum } from '@common/constants/AlertStatusConstant';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import UserMultipleSelect from '@components/UserMultipleSelect';
import { formatSelectCountMessage } from '@common/utils/MessageUtils';
import ACKDuration from './ACKDurationFilter';

type AlertFilterFormProps = {
  handleSearchAlert: (value: AlertFormFilterModel) => void;
  searchParams: AlertFormFilterModel;
};

const AlertFilterForm = ({ handleSearchAlert, searchParams }: AlertFilterFormProps) => {
  const [alertFormFilter, setAlertFormFilter] = useState<AlertFormFilterModel>(searchParams);
  const [disabledSearchButton, setDisableSearchButton] = useState<boolean>(false);
  const updateAlertFormFilter = useCallback<SetFieldValueFn<AlertFormFilterModel>>((field, value) => {
    setAlertFormFilter((prev) => ({ ...prev, [field]: value }));
  }, []);
  const onChangeDurationFilter = useCallback(
    (operator?: AlertDurationCompareOperatorEnum, duration?: number, unit?: AlertDurationCompareUnitEnum) => {
      setAlertFormFilter((prev) => ({ ...prev, closeDurationOperator: operator, closedDuration: duration, closeDurationUnit: unit }));
    },
    [],
  );
  const [opended, { toggle }] = useDisclosure(true);
  const { data: alertPriorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: true }), { showLoading: false });
  const priorityConfigOptions: ComboboxData = useMemo(
    () =>
      sortBy(
        alertPriorityConfigs?.data?.map((config) => ({ value: `${config.id || ''}`, label: config.name + (config.deleted ? ' (DELETED)' : '') })) ||
          [],
        (option) => option.label.toLowerCase(),
      ),
    [alertPriorityConfigs?.data],
  );

  return (
    <Box bg='white'>
      <Transition mounted={opended} transition='slide-right' duration={150} timingFunction='ease'>
        {(transitionStyle) => (
          <Paper shadow='lg' h='100%' w='355px' pos='absolute' top={0} left={0} right={0} style={{ ...transitionStyle, zIndex: 10 }}>
            <Stack h='100%' gap='xs' p='md'>
              <Box flex={1} style={{ overflowY: 'auto' }}>
                <Box style={{ maxHeight: '100%', overflowY: 'auto' }}>
                  <RangeDateCombobox
                    setFormData={setAlertFormFilter}
                    fromDate={alertFormFilter?.fromDate ? dayjs(alertFormFilter?.fromDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS) : undefined}
                    toDate={alertFormFilter?.toDate ? dayjs(alertFormFilter?.toDate, DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS) : undefined}
                    setDisableSearchButton={setDisableSearchButton}
                  />
                  <ServiceApplicationFilter alertFormFilter={alertFormFilter} updateAlertFormFilter={updateAlertFormFilter} />
                  <KanbanMultiSelect
                    data={priorityConfigOptions}
                    searchable
                    label='Priority'
                    placeholder={alertFormFilter.alertPriorityConfigIds.length ? 'Search priority' : 'All'}
                    value={alertFormFilter.alertPriorityConfigIds}
                    rightSectionPointerEvents='all'
                    rightSection={
                      <>
                        {alertFormFilter.alertPriorityConfigIds.length < priorityConfigOptions.length && (
                          <KanbanTooltip label='Select All'>
                            <IconSelectAll
                              onClick={() =>
                                updateAlertFormFilter(
                                  'alertPriorityConfigIds',
                                  alertPriorityConfigs?.data?.map((config) => `${config.id || ''}`) || [],
                                )
                              }
                            />
                          </KanbanTooltip>
                        )}
                        {alertFormFilter.alertPriorityConfigIds.length > 0 && (
                          <KanbanTooltip label='Clear All'>
                            <IconX onClick={() => updateAlertFormFilter('alertPriorityConfigIds', [])} />
                          </KanbanTooltip>
                        )}
                      </>
                    }
                    onChange={(value) => updateAlertFormFilter('alertPriorityConfigIds', value as string[])}
                  />
                  <KanbanMultiSelect
                    data={Object.values(AlertStatusEnum)}
                    searchable
                    label='Status'
                    value={alertFormFilter.statuses}
                    placeholder={alertFormFilter.statuses.length ? 'Search status' : 'All'}
                    rightSectionPointerEvents='all'
                    rightSection={
                      <>
                        {alertFormFilter.statuses.length < Object.values(AlertStatusEnum).length && (
                          <KanbanTooltip label='Select All'>
                            <IconSelectAll onClick={() => updateAlertFormFilter('statuses', Object.values(AlertStatusEnum))} />
                          </KanbanTooltip>
                        )}
                        {alertFormFilter.statuses.length > 0 && (
                          <KanbanTooltip label='Clear All'>
                            <IconX onClick={() => updateAlertFormFilter('statuses', [])} />
                          </KanbanTooltip>
                        )}
                      </>
                    }
                    onChange={(value) => updateAlertFormFilter('statuses', value as AlertStatusEnum[])}
                  />
                  <KanbanInput
                    label='Alert Content'
                    description={MAX_LENGTH_DESCRIPTION}
                    value={alertFormFilter.content}
                    onChange={(event) => updateAlertFormFilter('content', event.currentTarget.value)}
                    maxLength={MAX_LENGTH_CHARACTER}
                  />
                  <KanbanNumberInput
                    label='Group ID'
                    description='Search match Group Id'
                    value={alertFormFilter.alertGroupId}
                    onChange={(value) => {
                      const number = parseInt(`${value}`);
                      updateAlertFormFilter('alertGroupId', isNaN(number) ? undefined : number);
                    }}
                    max={MAX_ALERT_GROUP_ID}
                    min={0}
                    clampBehavior='strict'
                    allowNegative={false}
                    allowDecimal={false}
                  />
                  <KanbanInput
                    label='Contact'
                    description={MAX_LENGTH_DESCRIPTION}
                    value={alertFormFilter.recipient}
                    onChange={(event) => updateAlertFormFilter('recipient', event.currentTarget.value)}
                    maxLength={MAX_LENGTH_CHARACTER}
                  />
                  <UserMultipleSelect
                    label='ACK User'
                    description={formatSelectCountMessage(alertFormFilter?.closedBy?.length || 0)}
                    value={alertFormFilter.closedBy}
                    onChange={(userNames) => updateAlertFormFilter('closedBy', userNames)}
                    placeholder='Search user name'
                    justShowActiveUser={false}
                  />
                  <ACKDuration
                    onChangeDuration={onChangeDurationFilter}
                    duration={alertFormFilter.closedDuration}
                    operator={alertFormFilter.closeDurationOperator}
                    unit={alertFormFilter.closeDurationUnit}
                  />
                </Box>
              </Box>

              <Flex justify='flex-end'>
                <KanbanButton leftSection={<IconSearch />} onClick={() => handleSearchAlert(alertFormFilter)} disabled={disabledSearchButton} mt='xs'>
                  Search
                </KanbanButton>
              </Flex>
            </Stack>

            <KanbanTooltip label='Close filter'>
              <ActionIcon onClick={toggle} pos='absolute' style={{ right: -20, top: '50%', transform: 'translateY(-50%)' }}>
                <IconChevronLeft />
              </ActionIcon>
            </KanbanTooltip>
          </Paper>
        )}
      </Transition>
      <Transition mounted={!opended} transition='slide-left' duration={150} timingFunction='ease'>
        {(transitionStyle) => (
          <KanbanTooltip label='Open filter'>
            <ActionIcon
              onClick={toggle}
              pos='absolute'
              style={{ ...transitionStyle, zIndex: 10, left: -10, top: '50%', transform: 'translateY(-50%)' }}>
              <IconChevronRight />
            </ActionIcon>
          </KanbanTooltip>
        )}
      </Transition>
    </Box>
  );
};

export default AlertFilterForm;
