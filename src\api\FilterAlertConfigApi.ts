import { BaseURL } from '@common/constants/BaseUrl';
import { createPageSchema, createResponseSchema } from '@core/schema';
import { z } from 'zod';
import { PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { BaseFilterAlertConfigSchema, FilterAlertConfigSchema } from '@core/schema/FilterAlertConfig';
import { FilterAlertConfigModel } from '@models/FilterAlertConfigModel';

export type FilterAlertPaginationRequest = PaginationRequest & {
  search?: string;
};
export class FilterAlertConfigApi {
  static findAll(pagination: FilterAlertPaginationRequest) {
    return createRequest({
      url: BaseURL.filterAlert,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(BaseFilterAlertConfigSchema)),
      params: pagination,
    });
  }
  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.filterAlert}/:id`,
      method: 'GET',
      schema: createResponseSchema(FilterAlertConfigSchema),
      pathVariable: {
        id,
      },
    });
  }
  static save(data: FilterAlertConfigModel) {
    return createRequest({
      url: BaseURL.filterAlert,
      method: 'POST',
      schema: createResponseSchema(FilterAlertConfigSchema),
      data,
    });
  }
  static delete(id: number) {
    return createRequest({
      url: `${BaseURL.filterAlert}/:id`,
      method: 'DELETE',
      schema: createResponseSchema(z.string()),
      pathVariable: {
        id,
      },
    });
  }

  static updateActive({ active, id }: { id: number; active: boolean }) {
    return createRequest({
      url: `${BaseURL.filterAlert}/:id`,
      method: 'PUT',
      schema: createResponseSchema(FilterAlertConfigSchema),
      pathVariable: {
        id,
      },
      params: {
        active,
      },
    });
  }
}
