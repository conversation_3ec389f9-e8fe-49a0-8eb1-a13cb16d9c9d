import { Box, SimpleGrid } from '@mantine/core';
import React from 'react';
import clasess from './CalendarSection.module.css';

const DAY_OF_WEEKS = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

interface Props {
  isSmallView: boolean;
  ref: React.LegacyRef<HTMLDivElement> | undefined;
}

const DayOfWeeks = ({ isSmallView }: Props, ref: React.ForwardedRef<HTMLDivElement>) => {
  return (
    <SimpleGrid cols={7} spacing={0} ref={ref}>
      {DAY_OF_WEEKS.map((ele, index) => (
        <Box
          key={index}
          className={`${clasess.day} ${clasess.dayOfWeekTitle} ${index > 4 ? clasess.weekend : ''}`}
          style={{
            textAlign: isSmallView ? 'center' : 'left',
            borderRight: index === 6 ? '1px solid var(--mantine-color-default-border)' : undefined,
            padding: isSmallView ? 'calc(var(--mantine-spacing-xs) / 2)' : 'var(--mantine-spacing-md)',
          }}>
          {ele}
        </Box>
      ))}
    </SimpleGrid>
  );
};

DayOfWeeks.displayName = 'DayOfWeeks';

export default React.memo(React.forwardRef(DayOfWeeks as any)) as typeof DayOfWeeks;
