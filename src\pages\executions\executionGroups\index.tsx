import React, { useEffect, useRef } from 'react';
import { Box, Collapse, Flex, Text, Tooltip } from '@mantine/core';
import { useDebouncedValue, useIntersection } from '@mantine/hooks';
import { useState } from 'react';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import { PaginationModel } from '@models/PaginationModel';
import { ExecutionGroup } from '@core/schema/ExecutionGroup';
import classes from '../ExecutionGroups.module.scss';
import ExecutionList from './ExecutionList';
import { IconFolderFilled, IconSearch } from '@tabler/icons-react';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { SortType } from '@common/constants/SortType';
import { KanbanInput } from 'kanban-design-system';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';

export const DEFAULT_PAGINATION_REQUEST: PaginationModel = {
  page: 0,
  size: 25,
  sortBy: 'createdDate',
  sortOrder: SortType.DESC,
  search: '',
};

const ExecutionGroups = () => {
  const [selectedExecutionGroup, setSelectedExecutionGroup] = useState<ExecutionGroup | undefined>(undefined);
  const [search, setSearch] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  const { entry, ref } = useIntersection({
    threshold: 1,
  });
  const [searchValue] = useDebouncedValue(search, DEFAULT_DEBOUNCE_TIME);
  const { fetchNextPage, flatData, hasNextPage, isLoading } = useInfiniteFetch(
    ExecutionGroupApi.findAllWithPermission({ ...DEFAULT_PAGINATION_REQUEST, search: searchValue }),
  );

  useEffect(() => {
    if (entry?.isIntersecting && hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [entry?.isIntersecting, fetchNextPage, hasNextPage, isLoading, ref]);

  return (
    <Box ref={containerRef} className={classes.container}>
      <KanbanInput
        placeholder='Search'
        leftSection={<IconSearch />}
        value={search}
        onChange={(event) => setSearch(event.target.value || '')}
        px='xs'
        pt='xs'
      />
      {flatData?.map((group, index) => {
        const isSelectedGroup = selectedExecutionGroup?.id === group.id;
        return (
          <Box key={group.id} style={{ backgroundColor: isSelectedGroup ? 'var(--mantine-color-gray-1)' : 'transparent' }}>
            <Flex
              className={classes.group}
              align='center'
              justify='flex-start'
              onClick={() => setSelectedExecutionGroup(isSelectedGroup ? undefined : group)}
              ref={index === flatData.length - 1 ? ref : undefined}>
              <Box w={20} h={20}>
                <IconFolderFilled size={20} color='var(--mantine-color-yellow-4)' />
              </Box>
              <Tooltip label={group.name} multiline withArrow transitionProps={{ duration: 200 }}>
                <Text fw={500} truncate mr='xs' size='15px'>
                  {group.name}
                </Text>
              </Tooltip>
            </Flex>
            <Collapse in={isSelectedGroup} transitionDuration={150}>
              <ExecutionList executionGroup={group} isSelected={isSelectedGroup} />
            </Collapse>
          </Box>
        );
      })}
    </Box>
  );
};

export default ExecutionGroups;
