import React from 'react';
import { ExecutionGroup } from '@core/schema/ExecutionGroup';
import { KanbanIconButton, KanbanTooltip } from 'kanban-design-system';
import { IconTrash } from '@tabler/icons-react';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import useMutate from '@core/hooks/useMutate';
import { Box } from '@mantine/core';
import WarningAlertComponent from '@components/WarningAlertComponent';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import useFetch from '@core/hooks/useFetch';
import { ExecutionApi } from '@api/ExecutionApi';

interface Props {
  executionGroup: ExecutionGroup;
  onDeleteSuccess: () => void;
}

const DependenciesModalContent = ({ executionGroup }: { executionGroup: ExecutionGroup }) => {
  const { data: dependenciesData } = useFetch(ExecutionApi.findAllByExecutionGroupId(executionGroup.id));
  return (
    <Box>
      {dependenciesData?.data && (
        <WarningAlertComponent mainEntity='Execution Group' dependencyEntity='Execution' dependencies={dependenciesData?.data.map((e) => e.name)} />
      )}
      <DeleteConfirmMessage name={executionGroup.name} />
    </Box>
  );
};

const DeleteExecutionGroupButton = ({ executionGroup, onDeleteSuccess }: Props) => {
  const { mutate: deleteMutate } = useMutate(ExecutionGroupApi.deleteById, {
    confirm: {
      children: <DependenciesModalContent executionGroup={executionGroup} />,
      title: 'Confirm delete',
    },
    onSuccess: onDeleteSuccess,
  });
  return (
    <KanbanTooltip label='Delete'>
      <KanbanIconButton variant='transparent' size={'sm'} onClick={() => deleteMutate(executionGroup.id)}>
        <IconTrash color='red' />
      </KanbanIconButton>
    </KanbanTooltip>
  );
};

export default DeleteExecutionGroupButton;
