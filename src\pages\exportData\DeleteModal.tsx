import React from 'react';
import { KanbanButton } from 'kanban-design-system';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { ExportDataApi } from '@api/ExportDataApi';

type DeleteModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  exportDataId?: string;
  exportDataName?: string;
};

const DeleteModal: React.FC<DeleteModalProps> = ({ exportDataId, exportDataName, onClose, opened, refetchList }) => {
  const { mutate: deleteByIdMutate } = useMutate(ExportDataApi.deleteById, {
    successNotification: { message: 'Deleted successfully!' },
    onSuccess: () => {
      refetchList();
      onClose();
    },
  });
  return (
    <Modal
      size='xl'
      opened={opened}
      onClose={() => {
        onClose();
      }}
      title={'Delete export file'}
      actions={<KanbanButton onClick={() => deleteByIdMutate(exportDataId ?? '')}>Confirm</KanbanButton>}>
      <DeleteConfirmMessage name={exportDataName} />
    </Modal>
  );
};

export default DeleteModal;
