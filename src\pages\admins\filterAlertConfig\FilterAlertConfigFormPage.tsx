import { isNaN, parseInt } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';
import { KanbanButton, KanbanInput } from 'kanban-design-system';
import classes from './GroupConfigStyle.module.css';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { useForm, UseFormReturn, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import ConditionSession from './CoditionSession';
import { DEFAULT_FORM_VALUE, FilterAlertConfigAction } from './Constants';
import { DESCRIPTION_MAX_LENGTH, MAX_NAME_LENGTH } from '@common/constants/ValidationConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { FilterAlertConfigModel, FilterAlertConfigModelSchema } from '@models/FilterAlertConfigModel';
import { FilterAlertConfigApi } from '@api/FilterAlertConfigApi';
import { validateQuery } from '@components/queryBuilder';
import { RuleGroupType } from 'react-querybuilder';

const SaveButton = ({ form }: { form: UseFormReturn<FilterAlertConfigModel> }) => {
  const navigate = useNavigate();
  const { control, formState } = form;
  const ruleGroup = useWatch({ control, name: 'ruleGroup' });
  const isRuleValid = useMemo(() => validateQuery(ruleGroup as RuleGroupType), [ruleGroup]);

  const { mutate: saveFilterAlertConfigMutate } = useMutate(FilterAlertConfigApi.save, {
    onSuccess: () => {
      navigate('../');
    },
  });
  const onSubmit = useCallback(() => {
    const parsedData = FilterAlertConfigModelSchema.safeParse(form.getValues());

    if (parsedData.success) {
      const data = parsedData.data;
      saveFilterAlertConfigMutate({
        ...data,
      });
    }
  }, [form, saveFilterAlertConfigMutate]);
  return (
    <GuardComponent requirePermissions={[AclPermission.filterAlertConfigEdit, AclPermission.filterAlertConfigCreate]}>
      <KanbanButton onClick={onSubmit} disabled={!formState.isValid || !isRuleValid}>
        Save
      </KanbanButton>
    </GuardComponent>
  );
};

const FilterAlertConfigFormPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { filterAlertConfigId: filterAlertConfigStringId } = useParams();
  const filterAlertConfigId = !!filterAlertConfigStringId && !isNaN(filterAlertConfigStringId) ? parseInt(filterAlertConfigStringId) : 0;
  const isCreateMode = !filterAlertConfigId;
  const [currentTab, setCurrentTab] = useState<string>(FilterAlertConfigAction.CREATE);
  const isViewMode = currentTab === FilterAlertConfigAction.VIEW;
  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);

  const form = useForm<FilterAlertConfigModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(FilterAlertConfigModelSchema),
    mode: 'onChange',
  });

  const { data: filterAlertConfigData } = useFetch(FilterAlertConfigApi.findById(filterAlertConfigId), { enabled: !isCreateMode });

  useEffect(() => {
    if (!isCreateMode && filterAlertConfigData?.data) {
      const { data } = filterAlertConfigData;
      form.reset({
        ...data,
      });
    }
  }, [filterAlertConfigData, filterAlertConfigData?.data, form, isCreateMode]);
  return (
    <Box>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>
          {isViewMode ? 'View filter alert config' : isCreateMode ? 'Create filter alert config' : 'Update filter alert config'}
        </Title>
        <Flex>
          <Group>
            <KanbanButton variant='outline' onClick={() => navigate('../')}>
              Cancel
            </KanbanButton>
            {!isViewMode && <SaveButton form={form} />}
          </Group>
        </Flex>
      </Flex>
      <Stack gap='md'>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>General Information</Title>
          <KanbanInput disabled={isViewMode} label='Config name' required {...form.register('name')} maxLength={MAX_NAME_LENGTH} />
          <KanbanInput disabled={isViewMode} label='Description' {...form.register('description')} maxLength={DESCRIPTION_MAX_LENGTH} />
        </Stack>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Condition</Title>
          <ConditionSession isViewMode={isViewMode} form={form} />
        </Stack>
      </Stack>
    </Box>
  );
};

export default FilterAlertConfigFormPage;
