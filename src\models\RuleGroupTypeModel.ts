import { QueryRuleGroupTypeSchema, QueryRuleTypeSchema } from '@core/schema/RuleGroupCondition';
import { z } from 'zod';

export const QueryRuleTypeModelSchema = QueryRuleTypeSchema;

export type QueryRuleTypeModel = z.infer<typeof QueryRuleTypeModelSchema>;

export const QueryRuleGroupTypeModelSchema = QueryRuleGroupTypeSchema;

export type QueryRuleGroupTypeModel = z.infer<typeof QueryRuleGroupTypeModelSchema>;
