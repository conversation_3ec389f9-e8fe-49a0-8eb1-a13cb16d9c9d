import { Button, Popover, Text } from '@mantine/core';
import React, { useMemo, useState } from 'react';
import ReactJson from 'react-json-view';

interface SysLogMessage {
  template: string;
  params: (string | object)[];
}

interface Props {
  message: string;
}

const LogMessageParam = ({ param }: { param: string | object }) => {
  const [opened, setOpened] = useState(false);
  return (
    <Popover width={200} position='bottom' withArrow shadow='md' opened={opened} onChange={setOpened}>
      <Popover.Target>
        <Button variant='transparent' bg='primary.1' onClick={() => setOpened((o) => !o)} size='xs' maw={500}>
          <Text fw={500} truncate>
            {typeof param === 'string' || typeof param === 'number' ? param : 'Object'}
          </Text>
        </Button>
      </Popover.Target>
      <Popover.Dropdown styles={{ dropdown: { overflow: 'auto', width: 'fit-content', maxWidth: 400, maxHeight: 450 } }}>
        {typeof param === 'string' || typeof param === 'number' ? (
          param
        ) : (
          <ReactJson
            src={param}
            indentWidth={1}
            collapseStringsAfterLength={100}
            name={false}
            groupArraysAfterLength={100}
            style={{ fontSize: 13 }}
            enableClipboard={false}
            sortKeys
            displayObjectSize={false}
            displayDataTypes={false}
          />
        )}
      </Popover.Dropdown>
    </Popover>
  );
};

const LogMessage = ({ message }: Props) => {
  const parseMessage = useMemo<SysLogMessage>(() => JSON.parse(message), [message]);
  const templateElements = parseMessage.template.split(/{\d}/);
  return (
    <Text>
      {templateElements.map((ele, index) => {
        const param = parseMessage.params[index];
        return (
          <Text key={index} component='span' size='sm'>
            {ele} {param && <LogMessageParam param={param} />}
          </Text>
        );
      })}
    </Text>
  );
};

export default LogMessage;
