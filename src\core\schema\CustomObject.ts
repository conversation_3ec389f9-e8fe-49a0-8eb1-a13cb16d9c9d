import { CustomObjectTypeEnum } from '@common/constants/CustomObjectTypeConstant';
import { z } from 'zod';

export const CustomObjectSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().nullish(),
  type: z.nativeEnum(CustomObjectTypeEnum),
  regex: z.string().nullish(),
  fromIndex: z.number().nullish(),
  toIndex: z.number().nullish(),
  fromKeyword: z.string().nullish(),
  toKeyword: z.string().nullish(),
});

export type CustomObject = z.infer<typeof CustomObjectSchema>;
