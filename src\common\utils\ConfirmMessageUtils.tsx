import React from 'react';
import { Text } from '@mantine/core';
import { ConfirmConfig } from '@core/hooks/AppConfigTypes';

export function getDefaultDeleteConfirmMessage(name?: string): ConfirmConfig {
  return {
    title: 'Confirm delete',
    children: name ? <DeleteConfirmMessage name={name} /> : 'Are you sure to delete this item?',
  };
}

export const DeleteConfirmMessage: React.FC<{ name?: string }> = ({ name }) => (
  <Text>
    Are you sure to delete <strong>{name}</strong>?
  </Text>
);
