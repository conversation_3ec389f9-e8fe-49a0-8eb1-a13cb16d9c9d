import Modal from '@components/Modal';
import { Box } from '@mantine/core';
import { KanbanButton } from 'kanban-design-system';
import React from 'react';

interface Props {
  open: boolean;
  title: string;
  onClose: () => void;
  children: React.ReactNode;
  onSaveClick: () => void;
  disableSaveButton: boolean;
}
const PriorityConfigModal = ({ children, disableSaveButton, onClose, onSaveClick, open, title }: Props) => {
  return (
    <Modal
      size='xl'
      opened={open}
      onClose={onClose}
      title={title}
      actions={
        <KanbanButton onClick={onSaveClick} disabled={disableSaveButton}>
          Save
        </KanbanButton>
      }>
      <Box p='md'>{children}</Box>
    </Modal>
  );
};

export default PriorityConfigModal;
