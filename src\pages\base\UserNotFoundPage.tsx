import { logout } from '@common/utils/AuthenticateUtils';
import { KanbanButton } from 'kanban-design-system';
import React from 'react';
import styled from 'styled-components';

const Container = styled.div`
  text-align: center;
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  height: 100vh;
`;
const Title = styled.div`
  font-size: 1.5rem;
  div {
    display: inline-block;
    span {
      margin-right: 1px;
      font-weight: 900;
      color: var(--mantine-color-primary-5);
    }
  }
`;
const Description = styled.div``;
export default function UserNotFoundPage() {
  return (
    <>
      <Container>
        <Title>
          <div>
            <span>Your account has not been activated yet</span>
          </div>
        </Title>
        <Description>Please contact Administrator to support</Description>
        <br />
        <div>
          <KanbanButton variant='outline' onClick={() => logout()}>
            Logout
          </KanbanButton>
        </div>
      </Container>
    </>
  );
}
