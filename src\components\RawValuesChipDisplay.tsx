import { Chip, Flex } from '@mantine/core';
import React from 'react';

export const RawValuesChipDisplay = ({ values }: { values?: string[] }) => {
  return (
    <Flex wrap='wrap' gap='xs'>
      {values?.map((value) => (
        <Chip key={value} variant='outline' checked={false} styles={{ label: { wordBreak: 'break-all', cursor: 'initial' } }}>
          {value}
        </Chip>
      ))}
    </Flex>
  );
};
