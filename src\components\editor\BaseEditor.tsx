import { Box, Flex } from '@mantine/core';
import ReactCodeMirror, { Extension } from '@uiw/react-codemirror';
import { KanbanText } from 'kanban-design-system';
import React, { useMemo } from 'react';
import classes from './Editor.module.scss';
import { BaseEditorProps } from './Type';
import { MaxLengthExtension } from './Constants';

const BaseEditor = ({ error, label, maxLength, required = false, ...restProps }: BaseEditorProps) => {
  const extentions = useMemo<Extension[] | undefined>(() => {
    if (restProps.extensions === undefined || maxLength === undefined) {
      return undefined;
    }
    if (maxLength) {
      return [...(restProps.extensions || []), MaxLengthExtension(maxLength)];
    }
    return restProps.extensions;
  }, [maxLength, restProps.extensions]);
  return (
    <Box>
      {label && (
        <Flex direction='row' gap='xs'>
          <KanbanText fw='500' mb='xs'>
            {label}
          </KanbanText>
          {required && <KanbanText c='red'>*</KanbanText>}
        </Flex>
      )}
      <ReactCodeMirror {...restProps} extensions={extentions} className={classes.container} />
      {error && (
        <KanbanText c='red' size='sm' mt='xs'>
          {error}
        </KanbanText>
      )}
    </Box>
  );
};

export default BaseEditor;
