import { AlertSchema, createCursorPageSchema, createPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { AlertPaginationRequest } from '@models/AlertModel';
import { ExportFileRequest } from '@models/ExportFileModel';
import { createRequest } from './Utils';
import { AlertCursorSchema } from '@core/schema/AlertCursor';
import { z } from 'zod';
import { ExportData } from '@core/schema/ExportData';

export class AlertApi {
  static findAll(pagination: AlertPaginationRequest) {
    return createRequest({
      url: BaseURL.alert,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createCursorPageSchema(AlertSchema, AlertCursorSchema)),
    });
  }
  static countAllAlert(pagination: AlertPaginationRequest) {
    return createRequest({
      url: `${BaseURL.alert}/count`,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(z.number()),
    });
  }
  static findAllByPostMethod(pagination: AlertPaginationRequest) {
    return createRequest({
      url: `${BaseURL.alert}/search`,
      method: 'POST',
      data: pagination,
      schema: createResponseSchema(createPageSchema(AlertSchema)),
    });
  }
  static export(request: ExportFileRequest) {
    return createRequest<ExportData>({
      url: `${BaseURL.alert}/export`,
      method: 'POST',
      data: request,
    });
  }
}
