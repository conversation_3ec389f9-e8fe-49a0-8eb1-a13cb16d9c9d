import { SortType } from '@common/constants/SortType';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import Table from '@components/table';
import useFetch from '@core/hooks/useFetch';
import { Card } from '@mantine/core';
import { IconDeviceFloppy } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanInput,
  KanbanSwitch,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTextarea,
  KanbanTooltip,
  TableAffactedSafeType,
  useDebounceCallback,
} from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { TelegramAlertConfigApi } from '@api/TelegramAlertConfigApi';
import { PaginationRequest } from '@api/Type';
import { TelegramAlertConfig } from '@core/schema/Telegram';
import TelegramModal from './TelegramModal';
import { useForm, zodResolver } from '@mantine/form';
import { TelegramModelSchema } from '@models/TelegramModel';
import useMutate from '@core/hooks/useMutate';
import { TelegramAlertConfigTypeEnum } from '@common/constants/TelegramConstants';
import { MAX_DESCRIPTION_LENGTH, TELEGRAM_GROUP_ID_LENGTH } from '@common/constants/ValidationConstant';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { TELEGRAM_GROUP_ID_REGEX } from '@common/constants/RegexConstant';
import { TELEGRAM_GROUP_ID_REGEX_INPUT_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME } from '@common/constants/PaginationRequestConstant';

export const TelegramPage = () => {
  const tableServiceRef = useRef<KanbanTableSelectHandleMethods>(null);
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME);
  const [telegramAlertConfigs, setTelegramAlertConfigs] = useState<TelegramAlertConfig[]>([]);
  const [groupIdError, setGroupIdError] = useState<Record<string, boolean>>({});
  const { data: listService, refetch: refetchListService } = useFetch(TelegramAlertConfigApi.findAllServices(tableAffected), {
    placeholderData: (prev) => prev,
  });

  const { data: teleConfig, refetch: telegramConfigRefetchList } = useFetch(TelegramAlertConfigApi.findConfig(), {
    placeholderData: (prev) => prev,
  });

  const { getInputProps, isValid, setValues, values } = useForm({
    validate: zodResolver(TelegramModelSchema),
    validateInputOnChange: true,
    initialValues: { ...teleConfig?.data, isActive: false },
  });

  const { mutate: saveMutate } = useMutate(TelegramAlertConfigApi.saveConfig, {
    successNotification: { message: 'Save config successfully!' },
    onSuccess: () => {
      refetchListService();
      telegramConfigRefetchList();
    },
  });

  useEffect(() => {
    if (teleConfig?.data) {
      setValues(teleConfig?.data);
    }
  }, [teleConfig?.data, setValues]);

  const handleChangeAlertConfig = useCallback(async (config: TelegramAlertConfig, type: TelegramAlertConfigTypeEnum, isActiveChange: boolean) => {
    const configCopy = { ...config, isActive: config.isActive === undefined ? true : config.isActive };
    configCopy.type = type;
    setTelegramAlertConfigs((prev) => {
      const oldConfig = prev.find((obj) =>
        TelegramAlertConfigTypeEnum.SERVICE === type ? obj.serviceId === configCopy.serviceId : obj.applicationId === configCopy.applicationId,
      );
      if (oldConfig) {
        const newData = prev.filter((obj) =>
          TelegramAlertConfigTypeEnum.SERVICE === type ? obj.serviceId !== oldConfig.serviceId : obj.applicationId !== oldConfig.applicationId,
        );
        if (isActiveChange) {
          configCopy.groupChatId = oldConfig.groupChatId;
        } else {
          configCopy.isActive = oldConfig.isActive === undefined ? true : oldConfig.isActive;
        }
        return [...newData, { ...configCopy }];
      }
      return [...prev, { ...configCopy }];
    });
  }, []);

  const handleChangeAlertConfigDebounce = useDebounceCallback(handleChangeAlertConfig, 300);

  const getGroupChatId = useMemo(
    () =>
      (config: TelegramAlertConfig, type: TelegramAlertConfigTypeEnum): string => {
        const configEdit = telegramAlertConfigs.find((obj) =>
          TelegramAlertConfigTypeEnum.SERVICE === type ? obj.serviceId === config.serviceId : obj.applicationId === config.applicationId,
        );
        return configEdit?.groupChatId ?? config.groupChatId ?? '';
      },
    [telegramAlertConfigs],
  );

  const getCheckedEnable = useMemo(
    () =>
      (config: TelegramAlertConfig, type: TelegramAlertConfigTypeEnum): boolean => {
        const configEdit = telegramAlertConfigs.find((obj) =>
          TelegramAlertConfigTypeEnum.SERVICE === type ? obj.serviceId === config.serviceId : obj.applicationId === config.applicationId,
        );
        return configEdit?.isActive ?? config.isActive ?? true;
      },
    [telegramAlertConfigs],
  );

  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<TelegramAlertConfig>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME.sortBy,
        sortOrder: data.isReverse || !data.sortedBy ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );

  const hasAnyFalseGroupIdError = useMemo(() => {
    return Object.values(groupIdError).some((value) => value);
  }, [groupIdError]);

  const columnsService = useMemo((): ColumnType<TelegramAlertConfig>[] => {
    return [
      {
        title: 'Service Name',
        name: 'serviceName',
        width: '20%',
      },
      {
        title: 'Telegram Group ID ',
        name: 'groupChatId',
        customRender: (data, rowData) => {
          const rowDataCopy = { ...rowData };
          return (
            <KanbanInput
              pt={10}
              key={rowDataCopy.serviceId}
              maxLength={TELEGRAM_GROUP_ID_LENGTH}
              placeholder='Telegram group ID'
              error={groupIdError[rowDataCopy.serviceId || ''] ? TELEGRAM_GROUP_ID_REGEX_INPUT_MESSAGE_ERROR : undefined}
              defaultValue={getGroupChatId(rowDataCopy, TelegramAlertConfigTypeEnum.SERVICE)}
              onChange={(data) => {
                const isValid = !data.target.value || TELEGRAM_GROUP_ID_REGEX.test(data.target.value);
                setGroupIdError((prev) => ({
                  ...prev,
                  [rowDataCopy.serviceId || '']: !isValid,
                }));

                if (isValid) {
                  rowDataCopy.groupChatId = data.target.value;
                  handleChangeAlertConfigDebounce(rowDataCopy, TelegramAlertConfigTypeEnum.SERVICE, false);
                }
              }}
            />
          );
        },
      },
    ];
  }, [getGroupChatId, groupIdError, handleChangeAlertConfigDebounce]);

  const tableServiceProps: KanbanTableProps<TelegramAlertConfig> = useMemo(() => {
    return {
      columns: columnsService,
      data: listService?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listService?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          const dataCopy = { ...data };
          return (
            <>
              <KanbanTooltip label='Enable Telegram'>
                <KanbanSwitch
                  key={dataCopy.serviceId}
                  placeholder='En'
                  defaultChecked={getCheckedEnable(dataCopy, TelegramAlertConfigTypeEnum.SERVICE)}
                  onChange={(value) => {
                    dataCopy.isActive = value.target.checked;
                    handleChangeAlertConfigDebounce(dataCopy, TelegramAlertConfigTypeEnum.SERVICE, true);
                  }}
                />
              </KanbanTooltip>
              <KanbanTooltip label='Setting Application'>
                <TelegramModal
                  serviceId={data.serviceId ?? ''}
                  serviceName={data.serviceName ?? ''}
                  handleChangeAlertConfigDebounce={handleChangeAlertConfigDebounce}
                  getGroupChatId={getGroupChatId}
                  getCheckedEnable={getCheckedEnable}
                />
              </KanbanTooltip>
            </>
          );
        },
      },
    };
  }, [
    columnsService,
    getCheckedEnable,
    getGroupChatId,
    handleChangeAlertConfigDebounce,
    handleUpdateTablePagination,
    listService?.data?.content,
    listService?.data?.totalElements,
    tableAffected,
  ]);

  return (
    <>
      <HeaderTitleComponent
        title='Config Telegram'
        rightSection={
          <KanbanButton
            disabled={!isValid() || hasAnyFalseGroupIdError}
            size='xs'
            onClick={() => {
              saveMutate({ config: TelegramModelSchema.parse(values), alertsConfig: telegramAlertConfigs });
            }}
            leftSection={<IconDeviceFloppy />}>
            Save Config
          </KanbanButton>
        }
      />
      <Card shadow='sm' radius='md' m={5} withBorder>
        <KanbanInput
          {...getInputProps('botToken')}
          label='Bot token'
          required={!teleConfig?.data?.id}
          maxLength={100}
          placeholder={
            teleConfig?.data?.id ? (teleConfig?.data?.tokenPlaceHolder ? teleConfig?.data?.tokenPlaceHolder : 'Update bot token') : 'Input bot token'
          }
        />
        <KanbanInput
          {...getInputProps('defaultGroupChatId')}
          label='System default telegram group ID'
          maxLength={TELEGRAM_GROUP_ID_LENGTH}
          required
        />
        <KanbanTextarea
          {...getInputProps('description')}
          label='Description'
          maxLength={MAX_DESCRIPTION_LENGTH}
          description={getMaxLengthMessage(MAX_DESCRIPTION_LENGTH)}
        />
        <KanbanSwitch {...getInputProps('isActive')} checked={values['isActive']} label='Enable telegram' />
      </Card>
      <Card shadow='sm' radius='md' m={5} withBorder>
        <Table ref={tableServiceRef} {...tableServiceProps} />
      </Card>
    </>
  );
};
export default TelegramPage;
