import { SortType } from '@common/constants/SortType';
import { useCallback, useMemo, useState } from 'react';
import { SortEffect } from './Types';

interface Props {
  onSortChange?: (sortBy: string, direction: SortType) => void;
  defaultSortBy?: string;
  defaultDirection?: SortType;
}

export function useSortEffect({ defaultDirection, defaultSortBy, onSortChange }: Props): SortEffect {
  const [sortBy, setSortBy] = useState(defaultSortBy || '');
  const [direction, setDirection] = useState<SortType>(defaultDirection || SortType.ASC);
  const onChange = useCallback(
    (sortBy: string, direction: SortType) => {
      setSortBy(sortBy);
      setDirection(direction);
      onSortChange && onSortChange(sortBy, direction);
    },
    [onSortChange],
  );
  return useMemo(
    () => ({
      sortBy,
      direction,
      onChange,
    }),
    [direction, onChange, sortBy],
  );
}
