import Modal from '@components/Modal';
import { Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton } from 'kanban-design-system';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { IconPlus } from '@tabler/icons-react';
import useMutate from '@core/hooks/useMutate';
import { VariableApi } from '@api/VariableApi';
import ExeuctionVariableForm from './VariableForm';
import { zodResolver } from '@hookform/resolvers/zod';
import { VariableModel, VariableModelSchema } from '@models/VariableModel';

const DEFAULT_VARIABLE_VALUE: VariableModel = {
  name: '',
  description: '',
  value: '',
  hidden: false,
};

interface Props {
  onCreateSuccess: () => void;
}

const CreateVariableButton = ({ onCreateSuccess }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const form = useForm<VariableModel>({
    defaultValues: DEFAULT_VARIABLE_VALUE,
    resolver: zodResolver(VariableModelSchema),
  });
  const onCloseModal = useCallback(() => {
    close();
    form.reset();
  }, [close, form]);
  const { mutate: createMutate } = useMutate(VariableApi.createOrUpdate, {
    successNotification: 'Create Execution Variable successfully.',
    onSuccess: () => {
      onCreateSuccess();
      onCloseModal();
    },
  });
  const onSaveClick = useCallback(() => {
    createMutate(form.getValues());
  }, [createMutate, form]);
  const { formState } = form;
  return (
    <>
      <KanbanButton size='xs' onClick={open} leftSection={<IconPlus />}>
        Create Execution Variable
      </KanbanButton>
      <Modal
        size='xl'
        opened={opened}
        onClose={onCloseModal}
        title='Create Variable'
        actions={
          <KanbanButton onClick={onSaveClick} disabled={!formState.isValid}>
            Save
          </KanbanButton>
        }>
        <Box p='xs'>
          <ExeuctionVariableForm form={form} readonly={false} />
        </Box>
      </Modal>
    </>
  );
};

export default CreateVariableButton;
