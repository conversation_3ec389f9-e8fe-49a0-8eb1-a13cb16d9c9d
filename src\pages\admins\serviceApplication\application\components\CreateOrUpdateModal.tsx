import React, { useEffect, useMemo, useState } from 'react';
import { KanbanButton } from 'kanban-design-system';
import { ApplicationApi } from '@api/ApplicationApi';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import useFetch from '@core/hooks/useFetch';
import { Application } from '@core/schema';
import { useForm, zodResolver } from '@mantine/form';
import { KanbanInput } from 'kanban-design-system';
import useMutate from '@core/hooks/useMutate';
import { useDisclosure } from '@mantine/hooks';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { ServiceApi } from '@api/ServiceApi';
import { ComboboxItem } from '@mantine/core';
import { ServicePaginationRequest } from '@models/ServiceModel';
import { SortType } from '@common/constants/SortType';
import { ApplicationModel, ApplicationModelSchema } from '@models/ApplicationModel';
import { SelectWithPage } from '@components/SelectWithPage';
import Modal from '@components/Modal';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

type CreateOrUpdateModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  application?: Application;
};

const CHARACTER_NAME_APPLICATION_MAX_LENGTH = 100;
const CHARACTER_DESCRIPTION_APPLICATION_MAX_LENGTH = 300;
const DEFAULT_FORM_APPLICATION: ApplicationModel = { id: '', name: '', description: '', serviceId: '' };
const DEFAULT_PAGINATION_REQUEST: ServicePaginationRequest = {
  size: 10,
  page: 0,
  name: '',
  sortBy: 'name',
  sortOrder: SortType.ASC,
};
const CreateOrUpdateModal: React.FC<CreateOrUpdateModalProps> = ({ application, onClose, opened, refetchList }) => {
  const [serviceSelect, setServiceSelect] = useState<ComboboxItem | undefined>(undefined);
  const [serviceSearchParams, setServiceSearchParams] = useState(DEFAULT_PAGINATION_REQUEST);
  const isUpdateMode = !!application;
  const { data: applicationDetail } = useFetch(ApplicationApi.findById(application?.id || ''), {
    enabled: isUpdateMode && opened,
  });

  const { data: dependencies } = useFetch(ApplicationApi.findAllDependenciesById(application?.id || ''), {
    enabled: isUpdateMode && opened,
  });
  const dependencyConfig: DependencyItem[] = [
    {
      dependencyEntity: 'webhooks',
      dependencies: dependencies?.data?.webHooks ?? [],
    },
    {
      dependencyEntity: 'collect email configs',
      dependencies: dependencies?.data?.collectEmailConfigs ?? [],
    },
    {
      dependencyEntity: 'collect database configs',
      dependencies: dependencies?.data?.databaseCollects ?? [],
    },
    {
      dependencyEntity: 'alert group configs',
      dependencies: dependencies?.data?.alertGroupConfigs ?? [],
    },
    {
      dependencyEntity: 'maintenance time configs',
      dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
    },
    {
      dependencyEntity: 'database threshold configs',
      dependencies: dependencies?.data?.databaseThresholdConfigs ?? [],
    },
  ];
  const {
    fetchNextPage: fetchNextPageService,
    flatData: optionsService,
    isFetching: isServiceFetching,
  } = useInfiniteFetch(ServiceApi.findAll(serviceSearchParams), {
    showLoading: false,
    enabled: opened,
  });
  const { mutate: saveMutate } = useMutate(ApplicationApi.save, {
    successNotification: { message: application ? `Update Application Successfully` : 'Create Application Successfully' },
    onSuccess: () => {
      setValues(DEFAULT_FORM_APPLICATION);
      refetchList();
      onClose();
      if (openedModalConfirmUpdate) {
        closeModalConfirmUpdate();
      }
    },
    onError: () => {
      if (openedModalConfirmUpdate) {
        closeModalConfirmUpdate();
      }
    },
  });
  const [openedModalConfirmUpdate, { close: closeModalConfirmUpdate, open: openModalConfirmUpdate }] = useDisclosure(false);

  const { errors, getInputProps, isValid, setFieldValue, setValues, values } = useForm({
    validateInputOnChange: true,
    initialValues: DEFAULT_FORM_APPLICATION,
    validate: zodResolver(ApplicationModelSchema),
  });
  const isDisabledButtonSave = !isValid();
  const serviceComboxOptions = useMemo(() => {
    return optionsService.map((obj) => ({ value: `${obj.id}`, label: obj.name }));
  }, [optionsService]);

  const handleClickSaveButton = () => {
    if (isUpdateMode && isValid()) {
      openModalConfirmUpdate();
    } else if (isValid()) {
      saveMutate(ApplicationModelSchema.parse(values));
    }
  };

  const handleChangeServiceValue = (value?: string | undefined, data?: ComboboxItem | undefined) => {
    setServiceSelect(data);
    setFieldValue('serviceId', data?.value || '');
  };
  useEffect(() => {
    if (!isUpdateMode) {
      setValues(DEFAULT_FORM_APPLICATION);
      setServiceSelect(undefined);
      return;
    }
    if (applicationDetail?.data) {
      setValues({
        id: applicationDetail?.data?.id,
        name: applicationDetail?.data?.name,
        description: applicationDetail?.data?.description || '',
        serviceId: applicationDetail?.data?.serviceId || '',
      });
      setServiceSelect({ value: applicationDetail?.data?.serviceId || '', label: applicationDetail?.data?.serviceName || '' });
    }
  }, [application, applicationDetail?.data, isUpdateMode, opened, setValues]);

  return (
    <>
      <Modal
        size={'xl'}
        opened={opened}
        onClose={() => {
          onClose();
          setValues(DEFAULT_FORM_APPLICATION);
        }}
        title={isUpdateMode ? `Update Application ${application?.name ? application.name : ''}` : 'Create Application'}
        actions={
          <KanbanButton onClick={handleClickSaveButton} disabled={isDisabledButtonSave}>
            Save
          </KanbanButton>
        }>
        <DependenciesWarningAlert mainEntity='Application' dependencyConfigs={dependencyConfig} isDeleted={false} />
        <form>
          <KanbanInput
            required
            label='Application Name'
            description={getMaxLengthMessage(CHARACTER_NAME_APPLICATION_MAX_LENGTH)}
            value={values.name}
            {...getInputProps('name')}
            error={errors.name}
            maxLength={CHARACTER_NAME_APPLICATION_MAX_LENGTH}
          />
          <KanbanInput
            label='Description'
            description={getMaxLengthMessage(CHARACTER_DESCRIPTION_APPLICATION_MAX_LENGTH)}
            value={values.description}
            {...getInputProps('description')}
            error={errors.description}
            maxLength={CHARACTER_DESCRIPTION_APPLICATION_MAX_LENGTH}
          />
          <SelectWithPage
            label='Service Name'
            required={true}
            options={serviceComboxOptions}
            handleScrollToBottom={fetchNextPageService}
            onChange={handleChangeServiceValue}
            onSearch={(val) => setServiceSearchParams((prev) => ({ ...prev, page: 0, name: val }))}
            onBlur={() => setServiceSearchParams(DEFAULT_PAGINATION_REQUEST)}
            value={serviceSelect}
            isLoading={isServiceFetching}
            disabled={isUpdateMode}
          />
        </form>
      </Modal>
      <Modal
        size='xl'
        opened={openedModalConfirmUpdate}
        onClose={closeModalConfirmUpdate}
        title={'Update Application'}
        actions={<KanbanButton onClick={() => saveMutate(ApplicationModelSchema.parse(values))}>Confirm</KanbanButton>}>
        <DependenciesWarningAlert mainEntity='Application' dependencyConfigs={dependencyConfig} isDeleted={false} />
        Are you sure to update this item?
      </Modal>
    </>
  );
};

export default CreateOrUpdateModal;
