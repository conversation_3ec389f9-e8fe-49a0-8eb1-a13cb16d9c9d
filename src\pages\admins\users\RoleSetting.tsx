import { <PERSON><PERSON><PERSON> } from '@api/RoleApi';
import { User<PERSON><PERSON> } from '@api/UserApi';
import { PERMISSION_ACTION_LABEL, PermissionActionEnum } from '@common/constants/PermissionAction';
import {
  LIST_MODULE_BY_GROUP,
  LIST_PERMISSION_BY_MODULE,
  PERMISSION_MODULE_LABEL,
  PermissionGroupModuleEnum,
  PermissionModuleEnum,
} from '@common/constants/PermissionModule';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { refetchRequest } from '@common/utils/QueryUtils';
import { tableAffectedToPaginationRequestModel } from '@common/utils/TableAffectedUtils';
import Table from '@components/table';
import classes from './RoleSetting.module.scss';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { User } from '@core/schema';
import { ActionIcon, Card, Grid, NavLink, SimpleGrid, Space } from '@mantine/core';
import { RoleModel } from '@models/RoleModel';
import { IconLicense, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import {
  KanbanAccordion,
  KanbanCheckbox,
  KanbanInput,
  KanbanTableProps,
  KanbanText,
  KanbanTextarea,
  KanbanTitle,
  TableAffactedSafeType,
} from 'kanban-design-system';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { IconChevronRight } from '@tabler/icons-react';
import { PermissionTypeEnum } from '@common/constants/PermissionType';
import RunExecutionPermission from './RunExecutionPermission';

type SettingRoleProps = {
  id?: number;
  isCopy?: boolean;
  onChange?: (data: RoleModel) => void;
  closeModalRoleSetting?: () => void;
};

export type PermissionSettingModel = {
  module: PermissionModuleEnum;
  action: PermissionActionEnum;
  type: PermissionTypeEnum;
  isChecked?: boolean;
  moduleId?: string;
  moduleParentId?: string;
};
export interface SettingRoleHandle {
  saveRoleSetting: () => void;
}

const userColumns = [
  {
    title: 'User Name',
    name: 'userName',
    customRender: (data: string) => {
      return (
        <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
          {data}
        </KanbanText>
      );
    },
  },
];

const ROLE_DEFAULT_PAGE: TableAffactedSafeType<User> = { page: 0, search: '', isReverse: true };
export const SettingRole = forwardRef<SettingRoleHandle, SettingRoleProps>((props, ref) => {
  const { closeModalRoleSetting, id, isCopy, onChange } = props;
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType<User>>(ROLE_DEFAULT_PAGE);
  const [roleSetting, setRoleSetting] = useState<RoleModel>({ permissions: [] });
  const [viewUser, setViewUser] = useState<boolean>(false);
  const [groupModuleSelected, setGroupModuleSelected] = useState<PermissionGroupModuleEnum>(PermissionGroupModuleEnum.ALERT_CONFIG);

  const { data: usersByRoleId, refetch: fetchUsersByRoleId } = useFetch(
    UserApi.findAllByRoleId(id || 0, tableAffectedToPaginationRequestModel(tableAffected)),
    {
      enabled: !!tableAffected && !!id && viewUser,
      placeholderData: (prev) => prev,
    },
  );

  const { mutate: deleteUserIntoRoleMutate } = useMutate(RoleApi.deleteRoleFromUser, {
    successNotification: {
      title: 'Delete',
      message: `Delete user Success`,
    },
    onSuccess: () => {
      fetchUsersByRoleId();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const { mutate: saveRole } = useMutate(RoleApi.save, {
    successNotification: { enable: true, message: 'Save role successfully' },
    onSuccess: () => {
      refetchRequest(RoleApi.findAll());
      if (closeModalRoleSetting) {
        closeModalRoleSetting();
      }
    },
  });
  const { data: dataRoleDetail } = useFetch(RoleApi.findRoleWithPermissionById(id || 0), {
    enabled: !!id,
  });

  useEffect(() => {
    setRoleSetting({
      ...dataRoleDetail?.data,
      id: !isCopy ? dataRoleDetail?.data?.id : undefined,
      name: !isCopy ? dataRoleDetail?.data?.name : `${dataRoleDetail?.data?.name} copy`,
    });
  }, [dataRoleDetail, isCopy]);

  useEffect(() => {
    if (onChange) {
      onChange(roleSetting);
    }
  }, [onChange, roleSetting]);

  useImperativeHandle(
    ref,
    () => ({
      saveRoleSetting: () => {
        return saveRole(roleSetting);
      },
    }),
    [roleSetting, saveRole],
  );
  const setChecked = useCallback((data: PermissionSettingModel) => {
    setRoleSetting((prev) => {
      const permissionCopy = [...(prev.permissions || [])];
      if (data.isChecked) {
        permissionCopy.push({
          module: data.module,
          action: data.action,
          type: data.type,
          moduleId: data.moduleId,
          moduleParentId: data.moduleParentId,
        });

        return {
          ...prev,
          permissions: Array.from(
            new Map(
              permissionCopy.map((item) => [`${item.module}-${item.action}-${item.type}-${item.moduleId}-${item.moduleParentId}`, item]),
            ).values(),
          ),
        };
      } else {
        const permissionNew = permissionCopy.filter(
          (obj) =>
            !(
              obj.action === data.action &&
              obj.module === data.module &&
              obj.type === data.type &&
              obj.moduleId === data.moduleId &&
              obj.moduleParentId === data.moduleParentId
            ),
        );
        return {
          ...prev,
          permissions: permissionNew,
        };
      }
    });
  }, []);

  const getChecked = useCallback(
    (data: PermissionSettingModel): boolean => {
      const isChecked =
        roleSetting.permissions?.some(
          (obj) =>
            obj.module === data.module &&
            obj.action === data.action &&
            obj.type === data.type &&
            obj.moduleId === data.moduleId &&
            obj.moduleParentId === data.moduleParentId,
        ) || false;
      const isExistsParrent = roleSetting.permissions?.some(
        (obj) =>
          obj.module === data.module &&
          obj.action === data.action &&
          obj.type === data.type &&
          obj.moduleId === data.moduleParentId &&
          !obj.moduleParentId,
      );
      if (isExistsParrent && !isChecked) {
        setChecked({ ...data, isChecked: true });
        return true;
      }
      return isChecked;
    },
    [roleSetting.permissions, setChecked],
  );

  const setCheckedAll = (module: PermissionModuleEnum, isChecked: boolean) => {
    const lstAllPermissionByModule = LIST_PERMISSION_BY_MODULE[module];
    setRoleSetting((prev) => {
      const permissionCopy = [...(prev.permissions?.filter((obj) => obj.module !== module) || [])];
      if (isChecked) {
        lstAllPermissionByModule.forEach((obj) => {
          permissionCopy.push({ module: module, action: obj, type: PermissionTypeEnum.MODULE });
        });
      }
      return { ...prev, permissions: permissionCopy };
    });
  };

  const getCheckedAll = useCallback(
    (module: PermissionModuleEnum) => {
      const permissionsSettingByAction = roleSetting.permissions?.filter((obj) => obj.module === module) || [];
      const lstAllPermissionByModule = LIST_PERMISSION_BY_MODULE[module];
      if (permissionsSettingByAction.length > 0) {
        return lstAllPermissionByModule.every((obj) => permissionsSettingByAction.some((item) => item.action === obj));
      }
      return false;
    },
    [roleSetting.permissions],
  );

  const tableProps: KanbanTableProps<User> = useMemo(() => {
    return {
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },

      serverside: {
        totalRows: usersByRoleId?.data?.totalElements || 0,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      columns: userColumns,
      data: usersByRoleId?.data?.content || [],
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <ActionIcon
              variant='transparent'
              color='red'
              onClick={() =>
                deleteUserIntoRoleMutate(
                  { roleId: id || 0, userId: data?.id || 0 },
                  {
                    confirm: getDefaultDeleteConfirmMessage(data?.userName),
                  },
                )
              }>
              <IconTrash width={20} height={24} />
            </ActionIcon>
          );
        },
      },
      tableWrapperClasses: classes.tableRoleOfUser,
    };
  }, [deleteUserIntoRoleMutate, id, tableAffected, usersByRoleId?.data?.content, usersByRoleId?.data?.totalElements]);

  const moduleSort = useMemo(() => {
    return Object.entries(LIST_MODULE_BY_GROUP)
      .sort(([, modulesA], [, modulesB]) => modulesA[0].localeCompare(modulesB[0]))
      .reduce(
        (acc, [group, modules]) => {
          acc[group as PermissionGroupModuleEnum] =
            group === PermissionGroupModuleEnum.EXECUTION
              ? modules
              : modules
                  .slice()
                  .sort((a: PermissionModuleEnum, b: PermissionModuleEnum) => PERMISSION_MODULE_LABEL[a].localeCompare(PERMISSION_MODULE_LABEL[b]));
          return acc;
        },
        {} as typeof LIST_MODULE_BY_GROUP,
      );
  }, []);

  return (
    <>
      <KanbanInput
        maxLength={100}
        label='Role Name'
        required
        onChange={(data) => {
          setRoleSetting((prev) => ({ ...prev, name: data.target.value }));
        }}
        value={roleSetting.name}
      />
      <KanbanTextarea
        maxLength={300}
        label='Description'
        onChange={(data) => {
          setRoleSetting((prev) => ({ ...prev, description: data.target.value }));
        }}
        value={roleSetting.description}
      />
      <KanbanAccordion
        variant='separated'
        pb={'xl'}
        onChange={(value) => {
          setViewUser(value === '0');
        }}
        data={[
          {
            content: <Table {...tableProps} />,
            title: 'List users has current role',
            icon: {
              component: IconLicense,
            },
          },
        ]}
      />
      <KanbanTitle>Permission</KanbanTitle>
      <Space h='lg' />

      <Grid>
        <Grid.Col span={3}>
          <Card withBorder h={'100%'}>
            {Object.values(PermissionGroupModuleEnum)
              .sort((a, b) => a.localeCompare(b))
              .map((module, index) => (
                <KanbanText key={index} fw={700}>
                  <NavLink
                    key={index}
                    m={5}
                    onClick={() => setGroupModuleSelected(module)}
                    label={module}
                    active={module === groupModuleSelected}
                    rightSection={<IconChevronRight size='0.8rem' stroke={1.5} />}
                  />
                </KanbanText>
              ))}
          </Card>
        </Grid.Col>
        <Grid.Col span={9}>
          <Card withBorder h={'100%'}>
            {moduleSort[groupModuleSelected].map((module, index) => (
              <>
                {module === PermissionModuleEnum.RUN_EXECUTION && (
                  <RunExecutionPermission key={`RUN_EXECUTION-${index}`} userRoles={roleSetting} onChange={setChecked} />
                )}
                {module !== PermissionModuleEnum.RUN_EXECUTION && (
                  <>
                    <KanbanText key={index} pb={10} pt={20} fw={700} c={'blue'}>
                      {PERMISSION_MODULE_LABEL[module]}
                    </KanbanText>
                    <SimpleGrid cols={{ base: 1, sm: 2, lg: 5 }} spacing='md'>
                      <KanbanCheckbox
                        label='All'
                        checked={getCheckedAll(module)}
                        onChange={(data) => {
                          setCheckedAll(module, data.target.checked);
                        }}
                      />
                      {LIST_PERMISSION_BY_MODULE[module].map((action, index) => (
                        <KanbanCheckbox
                          key={index}
                          label={PERMISSION_ACTION_LABEL[action]}
                          checked={getChecked({
                            module: module,
                            action: action,
                            type: PermissionTypeEnum.MODULE,
                          })}
                          onChange={(data) => {
                            setChecked({
                              module: module,
                              action: action,
                              type: PermissionTypeEnum.MODULE,
                              isChecked: data.target.checked,
                            });
                          }}
                        />
                      ))}
                    </SimpleGrid>
                  </>
                )}
              </>
            ))}
          </Card>
        </Grid.Col>
      </Grid>
    </>
  );
});
SettingRole.displayName = 'SettingRole';
export default SettingRole;
