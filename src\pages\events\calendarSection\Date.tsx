import React from 'react';
import { AmountOfTask } from '@core/schema/AmountOfTask';
import dayjs, { Dayjs } from 'dayjs';
import { TaskTypeEnum } from '@common/constants/TaskConstants';
import TaskAmount from './TaskAmount';
import { Flex, Stack } from '@mantine/core';

interface Props {
  taskCount?: AmountOfTask;
  isSmallView: boolean;
  date: Dayjs;
}

const Date = ({ date, isSmallView, taskCount }: Props) => {
  const isToday = date.isSame(dayjs(), 'day');
  return (
    <Stack justify='center' align={isSmallView ? 'center' : 'left'}>
      <Flex
        align='center'
        justify='center'
        style={{
          width: isSmallView ? '26px' : '32px',
          height: isSmallView ? '26px' : '32px',
          backgroundColor: isToday ? 'var(--mantine-primary-color-4)' : undefined,
          color: isToday ? 'var(--mantine-color-default)' : undefined,
          borderRadius: 'var(--mantine-radius-xl)',
        }}>
        {date.date()}
      </Flex>
      <Flex direction={isSmallView ? 'row' : 'column'} gap='sm'>
        <TaskAmount isSmallView={isSmallView} taskAmount={taskCount?.amountOfTask} taskType={TaskTypeEnum.TASK} />
        <TaskAmount isSmallView={isSmallView} taskAmount={taskCount?.amountOfHanoverTask} taskType={TaskTypeEnum.SHIFT_HANDOVER_TASK} />
      </Flex>
    </Stack>
  );
};

export default React.memo(Date);
