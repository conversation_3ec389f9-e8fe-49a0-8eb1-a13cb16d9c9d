
.day {
  border-top: 1px solid var(--mantine-color-default-border);
  border-left: 1px solid var(--mantine-color-default-border);
  flex: 1;
}
.dayOfWeekTitle{
  font-size: var(--mantine-font-size-md);
  font-weight: 700;
  line-height: var(--mantine-line-height-md);
  color: var(--mantine-primary-color-7)
}
.dayOfWeek{
  font-size: var(--mantine-font-size-md);
  font-weight: 500;
  line-height: var(--mantine-line-height-md);
  
}
.weekend{
  color: var(--mantine-color-red-7)
}
.viewModeTitle{
  font-size: var(--mantine-font-size-md);
  font-weight: 600;
  line-height: var(--mantine-line-height-md);
  color: var(--mantine-primary-color-7)
}
.calendarWrapper{
  user-select: none; /* CSS3 (little to no support) */
  -ms-user-select: none; /* IE 10+ */
  -moz-user-select: none; /* <PERSON><PERSON><PERSON> (Firefox) */
  -webkit-user-select: none;
}