import React, { useCallback, useEffect } from 'react';
import { KanbanButton, KanbanIconButton, KanbanTooltip } from 'kanban-design-system';
import useMutate from '@core/hooks/useMutate';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from 'react-hook-form';
import useFetch from '@core/hooks/useFetch';
import { VariableApi } from '@api/VariableApi';
import { Variable } from '@core/schema/Variable';
import { IconEdit, IconEye } from '@tabler/icons-react';
import Modal from '@components/Modal';
import ExeuctionVariableForm from './VariableForm';
import { Box } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { VariableModel, VariableModelSchema } from '@models/VariableModel';

const DEFAULT_VARIABLE_VALUE: VariableModel = {
  name: '',
  description: '',
  value: '',
  hidden: false,
};

interface Props {
  variable: Variable;
  onUpdateSuccess: () => void;
}

const UpdateVariableButton = ({ onUpdateSuccess, variable }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const hasEditPermission = isAnyPermissions([AclPermission.variableEdit]);
  const form = useForm<VariableModel>({ defaultValues: DEFAULT_VARIABLE_VALUE, resolver: zodResolver(VariableModelSchema) });
  const { getValues, reset } = form;
  const { data: executionGroupData } = useFetch(VariableApi.findById(variable.id), { enabled: opened });
  const onClose = useCallback(() => {
    close();
    reset();
  }, [close, reset]);
  const { mutate } = useMutate(VariableApi.createOrUpdate, {
    successNotification: 'Update Execution Variable successfully.',
    onSuccess: () => {
      onUpdateSuccess();
      onClose();
    },
  });

  useEffect(() => {
    if (executionGroupData?.data) {
      reset({
        ...executionGroupData.data,
        value: executionGroupData.data?.hidden ? '' : executionGroupData.data?.value,
      });
    }
  }, [executionGroupData, reset]);
  const onSaveClick = useCallback(() => {
    mutate(getValues());
  }, [getValues, mutate]);
  const { formState } = form;
  return (
    <>
      <KanbanTooltip label='Edit'>
        <KanbanIconButton variant='transparent' size={'sm'} onClick={open}>
          {hasEditPermission ? <IconEdit /> : <IconEye />}
        </KanbanIconButton>
      </KanbanTooltip>
      <Modal
        size='xl'
        opened={opened}
        onClose={onClose}
        title={hasEditPermission ? 'Update Variable' : 'View Variable'}
        actions={
          hasEditPermission ? (
            <KanbanButton onClick={onSaveClick} disabled={!formState.isValid}>
              Save
            </KanbanButton>
          ) : undefined
        }>
        <Box p='xs'>
          <ExeuctionVariableForm form={form} readonly={!hasEditPermission} />
        </Box>
      </Modal>
    </>
  );
};

export default UpdateVariableButton;
