import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import { DatabaseThresholdConfigModel } from '@models/DatabaseThresholdConfigModel';
import { SimpleGrid } from '@mantine/core';
import { optionConditionOperators } from '@common/constants/ConditionOperatorEnum';

interface Props {
  form: UseFormReturn<DatabaseThresholdConfigModel>;
  isViewMode?: boolean;
}
export const CONDITION_VALUE_MIN = -1;
export const CONDITION_VALUE_MAX = 1_000_000_000;

const ConditionSession = ({ form, isViewMode }: Props) => {
  const { control } = form;
  return (
    <SimpleGrid cols={2}>
      <Controller
        name='conditionOperator'
        control={control}
        render={({ field, fieldState }) => (
          <KanbanSelect
            label='Condition operator'
            required
            disabled={isViewMode}
            data={optionConditionOperators}
            {...field}
            error={fieldState.error?.message}
          />
        )}
      />
      <Controller
        name='conditionValue'
        control={control}
        render={({ field, fieldState }) => (
          <KanbanNumberInput
            disabled={isViewMode}
            label='Condition value'
            required
            max={CONDITION_VALUE_MAX}
            min={CONDITION_VALUE_MIN}
            clampBehavior='strict'
            allowDecimal={false}
            {...field}
            onChange={(value) => {
              const val = Number(value) || 0;
              field.onChange(val);
            }}
            error={fieldState.error?.message}
          />
        )}
      />
    </SimpleGrid>
  );
};

export default ConditionSession;
