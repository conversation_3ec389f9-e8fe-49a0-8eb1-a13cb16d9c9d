
.editorContainer {
  display: flex;
  flex-direction: column;
  padding: 0.1rem; 
  border-radius: 0.05rem;
  box-shadow: 0 0.1rem 0.3rem var(--mantine-color-gray-5); 
  border: 0.1rem solid var(--mantine-color-dark-4); 
  transition: border-color 0.2s;

  &:focus-within {
    border-color: var(--mantine-color-blue-6); 
    box-shadow: 0 0 0 0.1rem var(--mantine-color-blue-6);
  }
}

.editor {
  padding: 0.4rem 1rem; 
  border-radius: 0.3rem;
  background-color: var(--mantine-color-white);
  border: 0.1rem solid var(--mantine-color-gray-4); 
  font-size: 0.875rem;
  color: 	var(--mantine-color-dark-6);
  outline: none;
  resize: none;
}

.paragraph {
  margin: 0;
  line-height: 1.5;
  color: inherit;
}

.mentionNode {
  color: var(--mantine-color-blue-6); 
  background-color: var(--mantine-color-white); 
  padding: 0.2rem 0.5rem;
  border-radius: 0.5rem;
}
