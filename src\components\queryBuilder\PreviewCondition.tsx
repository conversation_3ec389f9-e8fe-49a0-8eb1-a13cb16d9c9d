import { RuleGroupType, RuleType } from 'react-querybuilder';
import { useMemo } from 'react';
import { OPERATOR_LABELS, QueryBuilderOperatorEnum } from './QueryBuilderOperatorEnum';
import { KanbanText } from 'kanban-design-system';
import React from 'react';
import { groupToString, QueryBuilderField } from '.';

type Props = {
  fields: QueryBuilderField[];
  operators: string[];
  value?: RuleGroupType<RuleType<string, string, any, string>, string>;
};

export const PreviewCondition: React.FC<Props> = ({ fields, operators, value }) => {
  const operatorOptions = useMemo(
    () =>
      operators.map((operator) => ({
        name: operator,
        label: OPERATOR_LABELS[operator as QueryBuilderOperatorEnum],
      })),
    [operators],
  );

  return <KanbanText>{value && groupToString(value, fields, operatorOptions)}</KanbanText>;
};
