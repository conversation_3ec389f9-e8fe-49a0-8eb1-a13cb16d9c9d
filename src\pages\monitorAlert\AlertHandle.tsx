import React, { useContext, useEffect, useState } from 'react';
import { Box, Center, Flex, Loader, Stack, Text } from '@mantine/core';
import { ColumnType, KanbanButton, KanbanTabs, KanbanTextarea } from 'kanban-design-system';
import { Alert, Note } from '@core/schema';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { isEmpty, trim } from 'lodash';
import { NoteApi } from '@api/NoteApi';
import { dateToString } from '@common/utils/DateUtils';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import classes from './MonitorAlert.module.css';
import Table from '@components/table';
import { COMMENT_MAX_LENGTH } from './Constants';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { AlertGroupApi } from '@api/AlertGroupApi';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import InfiniteScroll from '@components/InfiniteScroll';
import { MonitorAlertPageContext } from './MonitorAlertPageContext';
import TeamsSendMessage from '@pages/admins/teams/TeamsSendMessage';

const Columns: ColumnType<Note>[] = [
  { name: 'createdBy', title: 'User', width: '15%' },
  { name: 'time', title: 'Time', customRender: (_, record) => dateToString(record.createdDate, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS), width: '35%' },
  { name: 'content', title: 'Comment' },
];

export enum AlertRightPanelTabEnum {
  COMMENT = 'COMMENT',
  TEAMS = 'TEAMS',
  SDP = 'SDP',
  WLA = 'WLA',
}
const AlertHandle = () => {
  const { reloadData, selectedAlertGroups, setSelectedAlertGroups } = useContext(MonitorAlertPageContext);
  const singleAlertGroupSelected = selectedAlertGroups.length === 1;
  const alertGroupId = selectedAlertGroups.filter((alertGroup) => alertGroup.alertAmount > 1).at(0)?.alertGroupId;
  const { data: notes, refetch } = useFetch(NoteApi.findAllByAlertGroupId(selectedAlertGroups.at(0)?.alertGroupId || 0), {
    showLoading: false,
    enabled: singleAlertGroupSelected,
  });
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [alertsTeams, setAlertsTeams] = useState<Alert[]>([]);
  const [currentTab, setCurrentTab] = useState<AlertRightPanelTabEnum>(AlertRightPanelTabEnum.COMMENT);
  const [comment, setComment] = useState('');
  const { isPending, mutate: addCommentMutate } = useMutate(NoteApi.saveAll, {
    successNotification: { message: 'Create comment successfully' },
    errorNotification: { message: 'Create comment failed' },
    onSuccess: () => {
      refetch();
      setComment('');
    },
  });

  const { isPending: isCloseAlertPending, mutate: closeAlertMutate } = useMutate(AlertGroupApi.close, {
    successNotification: { enable: false },
    errorNotification: (error) => ({ title: 'Close alerts error.', message: error.message }),
    onSuccess: (response) => {
      const { data: { errorCloseAlertGroupIds = [], errorReason, successCloseAlertGroupIds = [] } = {} } = response;
      if (!isEmpty(errorCloseAlertGroupIds)) {
        NotificationError({ title: `Close ${errorCloseAlertGroupIds.length} alert failed.`, message: errorReason });
      }
      if (!isEmpty(successCloseAlertGroupIds)) {
        NotificationSuccess({ message: `Close ${successCloseAlertGroupIds.length} alert success.` });
        reloadData();
        setSelectedAlertGroups((prev) => prev.filter((alertGroup) => !successCloseAlertGroupIds.includes(alertGroup.id)));
      }
    },
    confirm: {
      title: 'Confirm close alert!',
      children: 'Are you sure to close this Alert?',
    },
  });
  const { fetchNextPage, flatData, hasNextPage, isFetching } = useInfiniteFetch(
    AlertGroupApi.findAllByAlertGroupId(alertGroupId || 0, DEFAULT_PAGINATION_REQUEST),
    {
      showLoading: false,
      enabled: !!alertGroupId,
    },
  );

  useEffect(() => {
    if (alertGroupId) {
      setAlerts(flatData);
    } else {
      setAlerts(selectedAlertGroups);
    }
    setAlertsTeams(selectedAlertGroups);
  }, [alertGroupId, selectedAlertGroups, flatData]);

  if (isEmpty(selectedAlertGroups)) {
    return (
      <Center bg='var(--mantine-color-gray-light)' h='100%'>
        <Box bg='var(--mantine-color-blue-light)' p='xs' style={{ borderRadius: 'var(--mantine-radius-xs)' }}>
          No alert selected
        </Box>
      </Center>
    );
  }
  return (
    <Stack gap={15} h='100%' style={{ overflowY: 'auto' }}>
      <Box>
        <Flex justify='space-between' align='center'>
          <Text classNames={{ root: classes.selectedAlertTitle }} c='primary.5'>
            Selected Alerts
          </Text>
          <KanbanButton
            disabled={isCloseAlertPending || !isAnyPermissions([AclPermission.monitorAlertAcknowledge])}
            onClick={() => closeAlertMutate(selectedAlertGroups?.map((alert) => alert.alertGroupId))}>
            Acknowledge
          </KanbanButton>
        </Flex>

        <InfiniteScroll
          mah={singleAlertGroupSelected ? '400px' : '600px'}
          onScrollToBottom={() => {
            if (!isFetching && !!alertGroupId) {
              fetchNextPage();
            }
          }}>
          <Stack gap='sm'>
            {alerts.map((alert) => {
              return (
                <Box key={alert.id}>
                  <Text classNames={{ root: classes.alertContent }}>{alert.content}</Text>
                  <Text classNames={{ root: classes.alertOtherInfo }}>
                    Date: {dateToString(alert.createdDate, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}
                  </Text>
                  <Text classNames={{ root: classes.alertOtherInfo }}>Contact: {alert.recipient}</Text>
                </Box>
              );
            })}
          </Stack>
          {hasNextPage && (
            <Flex justify='center'>
              <Loader size={25} />
            </Flex>
          )}
        </InfiniteScroll>
      </Box>
      <KanbanTabs
        configs={{
          defaultValue: AlertRightPanelTabEnum.COMMENT,
          classNames: { list: classes.tabList, tab: classes.tabItem },
          onChange: (value) => {
            setCurrentTab(value as AlertRightPanelTabEnum);
          },
        }}
        tabs={{
          [AlertRightPanelTabEnum.COMMENT]: {
            content: (
              <Box>
                <KanbanTextarea
                  placeholder='Comment'
                  rows={4}
                  value={comment}
                  onChange={(event) => setComment(event.target.value)}
                  maxLength={COMMENT_MAX_LENGTH}
                />
                <Flex>
                  <KanbanButton
                    variant='outline'
                    mr={0}
                    fullWidth
                    onClick={() =>
                      addCommentMutate(selectedAlertGroups.map((alertGroup) => ({ alertGroupId: alertGroup.alertGroupId, content: trim(comment) })))
                    }
                    disabled={isPending || !trim(comment) || !isAnyPermissions([AclPermission.monitorAlertComment])}>
                    Add comment
                  </KanbanButton>
                </Flex>
              </Box>
            ),
            title: 'Comment',
          },
          [AlertRightPanelTabEnum.TEAMS]: {
            content: <TeamsSendMessage alerts={alertsTeams} />,
            title: 'Teams',
            disabled: !isAnyPermissions([AclPermission.teamsAlertSend]),
          },
          [AlertRightPanelTabEnum.SDP]: {
            content: <Box component='p' p={10}></Box>,
            title: 'SDP',
            disabled: true,
          },
          [AlertRightPanelTabEnum.WLA]: {
            content: (
              <Box component='p' p={10}>
                WLA
              </Box>
            ),
            title: 'WLA',
            // TODO: This feature not implment yet
            disabled: true,
          },
        }}
      />

      {singleAlertGroupSelected && AlertRightPanelTabEnum.COMMENT === currentTab && (
        <Table tableWrapperClasses={classes.commentTable} columns={Columns} data={notes?.data || []} maxHeight={500} />
      )}
    </Stack>
  );
};

export default AlertHandle;
