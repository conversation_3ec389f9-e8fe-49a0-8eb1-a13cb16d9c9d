import React, { useMemo } from 'react';
import Table from '@components/table';
import { ExecuteScript } from '@core/schema/ExecuteScript';
import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { KanbanTableProps } from 'kanban-design-system';

interface Props {
  executeScript: ExecuteScript;
  onPaginationChange: (page: number, size: number) => void;
}

const SQLResult = ({ executeScript, onPaginationChange }: Props) => {
  const tableViewListProps: KanbanTableProps<Record<string, any>> | null = useMemo(() => {
    const executeResult = executeScript.sqlExecutionResponse;
    return {
      columns: executeResult?.columns.map((e) => ({ name: e, title: e })) || [],
      data: executeResult?.result || [],
      showNumericalOrderColumn: true,
      sortable: {
        enable: false,
      },
      serverside: {
        totalRows: executeResult?.total || 0,
        onTableAffected: (dataSet) => {
          onPaginationChange(dataSet.page - 1, dataSet.rowsPerPage || 10);
        },
      },
      pagination: {
        enable: !executeResult?.nonQuery,
      },
    } as KanbanTableProps<Record<string, any>>;
  }, [executeScript.sqlExecutionResponse, onPaginationChange]);
  if (executeScript.type !== ExecutionTypeEnum.SQL) {
    return null;
  }
  return <Table {...tableViewListProps} maxHeight='750px' />;
};

export default SQLResult;
