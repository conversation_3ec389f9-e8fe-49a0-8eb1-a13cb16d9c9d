import Modal from '@components/Modal';
import { Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton, KanbanIconButton, KanbanTooltip } from 'kanban-design-system';
import React, { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { ExecutionGroupModel, ExecutionGroupModelSchema } from '@models/ExecutionModel';
import { IconEdit, IconEye } from '@tabler/icons-react';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import ExeuctionGroupForm from './ExecutionGroupForm';
import { zodResolver } from '@hookform/resolvers/zod';
import { AclPermission } from '@models/AclPermission';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';

const DEFAULT_EXECUTION_VALUE: ExecutionGroupModel = {
  name: '',
  description: '',
};

interface Props {
  executionGroupId: string;
  onUpdateSuccess: () => void;
}

const UpdateExecutionGroupButton = ({ executionGroupId, onUpdateSuccess }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const hasEditPermission = isAnyPermissions([AclPermission.executionGroupEdit]);
  const form = useForm<ExecutionGroupModel>({ defaultValues: DEFAULT_EXECUTION_VALUE, resolver: zodResolver(ExecutionGroupModelSchema) });
  const { getValues, reset } = form;
  const { data: executionGroupData } = useFetch(ExecutionGroupApi.findById(executionGroupId), {
    enabled: !!executionGroupId && opened,
  });
  const onClose = useCallback(() => {
    close();
    reset(DEFAULT_EXECUTION_VALUE);
  }, [close, reset]);
  const { mutate } = useMutate(ExecutionGroupApi.createOrUpdate, {
    successNotification: 'Update Execution Group successfully.',
    onSuccess: () => {
      onUpdateSuccess();
      onClose();
    },
  });

  useEffect(() => {
    if (executionGroupData?.data) {
      reset(executionGroupData.data);
    }
  }, [executionGroupData, reset]);
  const onSaveClick = useCallback(() => {
    mutate(getValues());
  }, [getValues, mutate]);
  const { formState } = form;
  return (
    <>
      <KanbanTooltip label='Edit'>
        <KanbanIconButton variant='transparent' size={'sm'} onClick={open}>
          {hasEditPermission ? <IconEdit /> : <IconEye />}
        </KanbanIconButton>
      </KanbanTooltip>
      <Modal
        size='xl'
        opened={opened}
        onClose={onClose}
        title={hasEditPermission ? 'Update Execution Group' : 'View Execution Group'}
        actions={
          hasEditPermission ? (
            <KanbanButton onClick={onSaveClick} disabled={!formState.isValid}>
              Save
            </KanbanButton>
          ) : undefined
        }>
        <Box p='xs'>
          <ExeuctionGroupForm form={form} readonly={!hasEditPermission} />
        </Box>
      </Modal>
    </>
  );
};

export default UpdateExecutionGroupButton;
