import { createPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { PaginationModel } from '@models/PaginationModel';
import { ExecutionGroupModel } from '@models/ExecutionModel';
import { ExecutionGroupSchema } from '@core/schema/ExecutionGroup';
import { z } from 'zod';

export class ExecutionGroupApi {
  static findAllWithPaging(pagination: PaginationModel) {
    return createRequest({
      url: `${BaseURL.executionGroup}/with-paging`,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(ExecutionGroupSchema)),
    });
  }
  static findAllWithPermission(pagination: PaginationModel) {
    return createRequest({
      url: `${BaseURL.executionGroup}/with-permission`,
      method: 'GET',
      params: pagination,
      schema: createResponseSchema(createPageSchema(ExecutionGroupSchema)),
    });
  }
  static findAllWithExecutionAmount() {
    return createRequest({
      url: `${BaseURL.executionGroup}/with-execution-amount`,
      method: 'GET',
      schema: createResponseSchema(z.array(ExecutionGroupSchema)),
    });
  }
  static findAll(searchParam?: { orderBy?: string }) {
    return createRequest({
      url: BaseURL.executionGroup,
      method: 'GET',
      params: searchParam,
      schema: createResponseSchema(z.array(ExecutionGroupSchema)),
    });
  }
  static findById(id: string) {
    return createRequest({
      url: `${BaseURL.executionGroup}/:id`,
      method: 'GET',
      pathVariable: { id },
      schema: createResponseSchema(ExecutionGroupSchema),
    });
  }
  static createOrUpdate(executionGroup: ExecutionGroupModel) {
    return createRequest({
      url: BaseURL.executionGroup,
      method: 'POST',
      data: executionGroup,
      schema: createResponseSchema(ExecutionGroupSchema),
    });
  }
  static deleteById(id: string) {
    return createRequest<string>({
      url: `${BaseURL.executionGroup}/:id`,
      method: 'DELETE',
      pathVariable: { id },
    });
  }
}
