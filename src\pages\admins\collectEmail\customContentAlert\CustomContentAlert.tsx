/* eslint-disable react/prop-types */
import Document from '@tiptap/extension-document';
import Mention from '@tiptap/extension-mention';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import { <PERSON><PERSON>onte<PERSON>, J<PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, useEditor } from '@tiptap/react';
import React, { useEffect, useRef } from 'react';
import { MentionList, MentionListActions, MentionListProps } from './MentionList';
import '@mantine/tiptap/styles.css';
import { KanbanText } from 'kanban-design-system';
import styles from './styles.module.scss';
import tippy, { GetReferenceClientRect, Instance, Props } from 'tippy.js';
import CharacterCount from '@tiptap/extension-character-count';
import { Flex } from '@mantine/core';
interface EditorProps {
  value: string;
  onChange: (content: string, value?: string) => void;
  label?: string;
  disabled: boolean;
  specialMentions?: { id: string; name: string }[];
  specialMentionOnly?: boolean;
}

function formatContentToString(content: JSONContent[]): string {
  return content
    .map((node) => {
      if (node.type === 'text' && node.text) {
        return node.text;
      } else if (node.type === 'mention' && node.attrs?.id && node.attrs?.label) {
        return `@${node.attrs.id}`;
      } else if (node.content && Array.isArray(node.content)) {
        return formatContentToString(node.content);
      }
      return '';
    })
    .join('');
}
function parseStringToJson(str: string) {
  try {
    return JSON.parse(str);
  } catch (e) {
    return {};
  }
}

const CONTENT_MAX_LENGTH = 2000;

export const CustomContentAlert: React.FC<EditorProps> = ({ disabled, label, onChange, specialMentionOnly = false, specialMentions, value }) => {
  const hasHydrated = useRef(false);

  const editor = useEditor({
    editable: !disabled,
    content: parseStringToJson(value),
    editorProps: {
      attributes: { class: styles.editor },
      handleDOMEvents: {
        paste: (view, event: ClipboardEvent) => {
          if (!editor) {
            return false;
          }
          const currentContentLength = editor.storage.characterCount.characters();
          const maxLength = CONTENT_MAX_LENGTH;
          const pastedText = event.clipboardData?.getData('text/plain') || '';
          const pastedLength = pastedText.length;
          if (currentContentLength + pastedLength > maxLength) {
            const remainingLength = maxLength - currentContentLength;
            if (remainingLength <= 0) {
              event.preventDefault();
              return true;
            }
            const truncatedText = pastedText.slice(0, remainingLength);
            const insertPos = view.state.selection.from;

            editor.commands.insertContentAt(insertPos, truncatedText);
            event.preventDefault();
            return true;
          }
          return false;
        },
      },
    },
    extensions: [
      Document,
      Paragraph.configure({
        HTMLAttributes: { class: styles.paragraph },
      }),
      Text,
      CharacterCount.configure({
        limit: CONTENT_MAX_LENGTH,
      }),
      Mention.configure({
        HTMLAttributes: { class: styles.mentionNode },
        suggestion: {
          render: () => {
            let component: ReactRenderer<MentionListActions, MentionListProps & React.RefAttributes<MentionListActions>> | undefined;
            let popup: Instance[] | undefined;
            return {
              onStart: (props) => {
                component = new ReactRenderer(MentionList, {
                  props: {
                    ...props,
                    specialMentions,
                    specialMentionOnly,
                  },
                  editor: props.editor,
                });
                if (!props.clientRect) {
                  return;
                }
                const rect: GetReferenceClientRect = () => {
                  const rect = props.clientRect && props.clientRect();
                  return rect ? rect : new DOMRect();
                };
                const optionalProps: Partial<Props> = {
                  getReferenceClientRect: rect,
                  appendTo: () => document.body,
                  content: component.element,
                  showOnCreate: true,
                  interactive: true,
                  trigger: 'manual',
                  placement: 'bottom-start',
                };
                popup = tippy('body', optionalProps);
              },
              onUpdate(props) {
                if (component) {
                  component.updateProps(props);
                }
                if (!props.clientRect || !popup) {
                  return;
                }
                const rect: GetReferenceClientRect = () => {
                  const rect = props.clientRect && props.clientRect();
                  return rect ? rect : new DOMRect();
                };
                popup[0].setProps({
                  getReferenceClientRect: rect,
                });
              },
              onKeyDown: (props) => {
                if (props.event.key === 'Escape') {
                  if (popup) {
                    popup[0].hide();
                  }
                  return true;
                }
                if (component && component.ref) {
                  return component.ref.onKeyDown(props);
                }
                return false;
              },
              onExit() {
                if (popup) {
                  popup[0].destroy();
                }
                component?.destroy();
              },
            };
          },
          command: ({ editor, props, range }) => {
            editor
              .chain()
              .focus()
              .deleteRange(range)
              .insertContent({ type: 'mention', attrs: { id: props.id, label: props.label } })
              .run();
          },
        },
      }),
    ],
    onUpdate: ({ editor }) => {
      if (disabled) {
        return;
      }
      const contentText = formatContentToString(editor.getJSON().content as JSONContent[]).trim();
      if (contentText.length > CONTENT_MAX_LENGTH) {
        return;
      }
      onChange(JSON.stringify(editor.getJSON()), contentText);
    },
  });
  useEffect(() => {
    if (editor && !hasHydrated.current && value) {
      editor.commands.setContent(parseStringToJson(value));
      hasHydrated.current = true;
    }
  }, [editor, value]);

  return (
    <div>
      {label && (
        <Flex direction={'row'}>
          <KanbanText fw={'500'} mb={'0.25rem'}>
            {label}
          </KanbanText>
          <KanbanText c={'red'}>*</KanbanText>
        </Flex>
      )}
      <EditorContent editor={editor} />
    </div>
  );
};
