import React from 'react';
import { KanbanButton, KanbanInput, KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { comboboxExportFileType, ExportFileTypeEnum } from '@common/constants/ExportFileTypeConstants';
import { useForm, zodResolver } from '@mantine/form';
import { ExportFileModelSchema, getInitExportFileRequest } from '@models/ExportFileModel';
import { LIMIT_ALERT_EXPORT_ROWS_LENGTH } from '@common/constants/ValidationConstant';
import { AlertFormFilterModel } from '@models/AlertModel';
import { columns } from '../AlertCommon';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { MAX_CHARACTER_FILE_NAME_LENGTH } from '@common/constants/ValidationConstant';
import Modal from '@components/Modal';
import useMutate from '@core/hooks/useMutate';
import { AlertApi } from '@api/AlertApi';
import MessageExportSuccess from '@components/MessageExportSuccess';

type ExportFileModalProps = {
  opened: boolean;
  onClose: () => void;
  tableAffected: AlertFormFilterModel;
};

const ExportFileModal: React.FC<ExportFileModalProps> = ({ onClose, opened, tableAffected }) => {
  const { getInputProps, isValid, reset, values } = useForm({
    validateInputOnChange: true,
    initialValues: getInitExportFileRequest(columns, LIMIT_ALERT_EXPORT_ROWS_LENGTH),
    validate: zodResolver(ExportFileModelSchema),
  });

  const { isPending, mutate: exportFile } = useMutate(AlertApi.export, {
    successNotification: { message: <MessageExportSuccess /> },
    onSuccess: () => handleClose(),
    showLoading: false,
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleExport = () => {
    exportFile({
      exportDataModel: { fileName: values.nameFile, extension: values.typeFile || ExportFileTypeEnum.CSV },
      attributes: values.attributes,
      paginationRequest: {
        ...tableAffected,
        nextCursor: undefined,
        pageSize: values.numberOfResults ? values.numberOfResults : LIMIT_ALERT_EXPORT_ROWS_LENGTH,
        serviceIds: tableAffected.services?.map((ele) => ele.id) || [],
        applicationIds: tableAffected.applications?.map((ele) => ele.id) || [],
      },
      numberOfResults: values.numberOfResults,
    });
  };

  return (
    <>
      <Modal
        size={'xl'}
        opened={opened}
        onClose={() => {
          handleClose();
        }}
        title={'Export File'}
        closeOnClickOutside={false}
        actions={
          <GuardComponent requirePermissions={[AclPermission.reportExport]}>
            <KanbanButton loading={isPending} disabled={!isValid()} onClick={handleExport}>
              Export
            </KanbanButton>
          </GuardComponent>
        }>
        <form>
          <KanbanSelect label='Type File' required={true} data={comboboxExportFileType} {...getInputProps('typeFile')} />
          <KanbanInput
            label='File Name'
            description={getMaxLengthMessage(MAX_CHARACTER_FILE_NAME_LENGTH)}
            value={values.nameFile}
            {...getInputProps('nameFile')}
            maxLength={MAX_CHARACTER_FILE_NAME_LENGTH}
          />
          <KanbanNumberInput
            placeholder={`leave blank to export ${LIMIT_ALERT_EXPORT_ROWS_LENGTH} results`}
            label='Number of Results'
            {...getInputProps('numberOfResults')}
            allowDecimal={false}
            allowNegative={false}
          />
        </form>
      </Modal>
    </>
  );
};
export default ExportFileModal;
