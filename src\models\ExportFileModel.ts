import { ExportFileTypeEnum } from '@common/constants/ExportFileTypeConstants';
import { SERVICE_APPLICATION_DESCRIPTION_REGEX } from '@common/constants/RegexConstant';

import { z } from 'zod';
import { ApplicationPaginationRequest } from './ApplicationModel';
import { ServicePaginationRequest } from './ServiceModel';
import { AlertPaginationRequest } from './AlertModel';
import { ColumnType } from 'kanban-design-system';
import { Alert, Application, Service } from '@core/schema';
import { FILE_NAME_EXPORT_REGEX_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { LIMIT_EXPORT_ROWS_LENGTH } from '@common/constants/ValidationConstant';
import { ExportDataModel } from './ExportDataModel';

export const CHARACTER_NAME_FILE_MAX_LENGTH = 20;

export type ExportFileRequest = {
  paginationRequest: ApplicationPaginationRequest | ServicePaginationRequest | AlertPaginationRequest;
  exportDataModel: ExportDataModel;
  attributes: AttributeInfoDto[];
  numberOfResults: number | undefined;
};

export type AttributeInfoDto = {
  position: number;
  attributeId: string;
  attributeName: string;
  width?: number;
};

export type ExportFileModel = {
  attributes: AttributeInfoDto[];
  typeFile: ExportFileTypeEnum | undefined;
  nameFile: string;
  numberOfResults: number | undefined;
};

export const ExportFileModelSchema = z.object({
  typeFile: z.nativeEnum(ExportFileTypeEnum),
  nameFile: z.string().trim().regex(SERVICE_APPLICATION_DESCRIPTION_REGEX, { message: FILE_NAME_EXPORT_REGEX_MESSAGE_ERROR }).optional(),
  numberOfResults: z.number().min(1).max(LIMIT_EXPORT_ROWS_LENGTH).optional(),
});

export const getInitExportFileRequest = (
  columns: ColumnType<Service>[] | ColumnType<Application>[] | ColumnType<Alert>[],
  numberOfResults: number = LIMIT_EXPORT_ROWS_LENGTH,
): ExportFileModel => {
  const attributes: AttributeInfoDto[] = columns.map((column, index) => {
    const width = typeof column.width === 'string' ? parseFloat(column.width) * 500 : typeof column.width === 'number' ? column.width : 5000;
    return {
      position: index,
      attributeName: column.title,
      attributeId: column.name,
      width: width,
    };
  });

  const createdDateAttribute: AttributeInfoDto = {
    position: attributes.length,
    attributeName: 'Created Date',
    attributeId: 'createdDate',
    width: 10000,
  };

  attributes.push(createdDateAttribute);

  return {
    attributes,
    typeFile: ExportFileTypeEnum.CSV,
    nameFile: '',
    numberOfResults,
  };
};
