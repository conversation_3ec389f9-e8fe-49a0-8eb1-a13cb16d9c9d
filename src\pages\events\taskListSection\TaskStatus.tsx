import { TaskStatusColor, TaskStatusEnum, TaskStatusLabel } from '@common/constants/TaskConstants';
import { Flex } from '@mantine/core';
import React from 'react';

interface Props {
  status: TaskStatusEnum;
}

const TaskStatus = ({ status }: Props) => {
  return (
    <Flex
      justify='center'
      align='center'
      px='var(--mantine-spacing-xs)'
      py='calc(var(--mantine-spacing-xs) / 2)'
      style={{
        ...TaskStatusColor[status],
        borderRadius: 'var(--mantine-radius-sm)',
        fontWeight: 500,
        fontSize: 14,
        width: 'fit-content',
        whiteSpace: 'pre',
      }}>
      {TaskStatusLabel[status]}
    </Flex>
  );
};

export default TaskStatus;
