import { User<PERSON><PERSON> } from '@api/UserApi';
import Table from '@components/table';
import useFetch from '@core/hooks/useFetch';
import { User } from '@core/schema';
import { ActionIcon, Tooltip } from '@mantine/core';
import { IconAccessible, IconAccessibleOff, IconTrash, IconUser, IconUserOff } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import { KanbanIconButton, KanbanTableProps, KanbanTableSelectHandleMethods, KanbanText, TableAffactedSafeType } from 'kanban-design-system';
import React, { useMemo, useRef, useState } from 'react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import useMutate from '@core/hooks/useMutate';
import { tableAffectedToPaginationRequestModel } from '@common/utils/TableAffectedUtils';
import { CreateUserModal } from './CreateUserModal';
import { UserDetailModal } from './UserDetailModal';
import { AclPermission } from '@models/AclPermission';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { getDirectState } from '@store';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import GuardComponent from '@components/GuardComponent';

const USER_PAGEGING_DEFAULT: TableAffactedSafeType<User> = { page: 1, search: '', isReverse: true };

const columns = [
  {
    title: 'User Name',
    name: 'userName',
    customRender: (data: string) => {
      return (
        <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
          {data}
        </KanbanText>
      );
    },
  },
  {
    title: 'Email',
    name: 'email',
  },
];

export const UserManagement = () => {
  const [currentDataSelected, setCurrentDataSelected] = useState<User[]>([]);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType<User>>(USER_PAGEGING_DEFAULT);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const { data: users, refetch: fetchListUsers } = useFetch(UserApi.findAll(tableAffectedToPaginationRequestModel(tableAffected)), {
    enabled: !!tableAffected,
    placeholderData: (prev) => prev,
  });
  const currentUser = getCurrentUser(getDirectState());

  const { mutate: activeUser } = useMutate(UserApi.active, {
    successNotification: (res) => {
      return {
        title: 'Active Success',
        message: `Active user ${res.data?.userName} Success`,
      };
    },
    confirm: { title: 'Active User!', children: <KanbanText>Are you sure active User?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      fetchListUsers();
    },
  });

  const { mutate: deActiveUser } = useMutate(UserApi.deActive, {
    successNotification: (res) => {
      return {
        title: 'Inactive User',
        message: `Inactive user ${res.data?.userName} Success`,
      };
    },
    confirm: { title: 'Inactive User!', children: <KanbanText>Are you sure inactive User?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      fetchListUsers();
    },
  });

  // setting admin
  const { mutate: setAdmin } = useMutate(UserApi.setAdmin, {
    successNotification: (res) => {
      return {
        title: 'Setting Admin',
        message: `Setting admin for user ${res.data?.userName} Success`,
      };
    },
    confirm: { title: 'Setting admin!', children: <KanbanText>Are you sure setting admin?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      fetchListUsers();
    },
  });

  const { mutate: unsetAdmin } = useMutate(UserApi.unsetAdmin, {
    successNotification: (res) => {
      return {
        title: 'Setting Admin',
        message: `Unset admin for user ${res.data?.userName} Success`,
      };
    },
    confirm: { title: 'Setting Admin!', children: <KanbanText>Are you sure unset admin?</KanbanText>, textConfirm: 'Confirm' },
    onSuccess: () => {
      fetchListUsers();
    },
  });

  const { mutate: deleteByIdMutate } = useMutate(UserApi.deleteById, {
    successNotification: {
      title: 'Delete user',
      message: `Delete user Success`,
    },
    onSuccess: () => {
      fetchListUsers();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const { mutate: deleteBatchMutate } = useMutate(UserApi.deleteByIdIn, {
    successNotification: {
      title: 'Delete user',
      message: `Delete user Success`,
    },
    onSuccess: () => {
      fetchListUsers();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const tableProps: KanbanTableProps<User> = useMemo(() => {
    return {
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },
      onRowClicked: (row) => {
        <UserDetailModal userId={row.id} />;
      },
      selectableRows: {
        enable: isAnyPermissions([AclPermission.userManageDelete]),
        onDeleted(rows) {
          deleteBatchMutate(rows.map((x) => x.id));
        },
        crossPageSelected: {
          rowKey: 'id',
          selectedRows: currentDataSelected,
          setSelectedRows: setCurrentDataSelected,
        },
      },
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },

      serverside: {
        totalRows: users?.data?.totalElements || 0,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      columns: columns,
      data: users?.data?.content || [],
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              {isAnyPermissions([AclPermission.userManageEdit]) && currentUser.userInfo?.userName !== data.userName && (
                <Tooltip label={data.isActive ? 'InActive User' : 'Active User'}>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      data.isActive ? deActiveUser(data.id) : activeUser(data.id);
                    }}>
                    {data?.isActive ? <IconUser /> : <IconUserOff />}
                  </KanbanIconButton>
                </Tooltip>
              )}
              {isAnyPermissions([AclPermission.userManageEdit]) && currentUser.userInfo?.userName !== data.userName && (
                <Tooltip label={data.isAdmin ? 'Unset admin' : 'Setting admin'}>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      data.isAdmin ? unsetAdmin(data.id) : setAdmin(data.id);
                    }}>
                    {data?.isAdmin ? <IconAccessible /> : <IconAccessibleOff />}
                  </KanbanIconButton>
                </Tooltip>
              )}
              {isAnyPermissions([AclPermission.userManageEdit]) && <UserDetailModal userId={data.id} />}
              <GuardComponent requirePermissions={[AclPermission.userManageDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.userName),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [
    currentDataSelected,
    users?.data?.totalElements,
    users?.data?.content,
    deleteBatchMutate,
    tableAffected,
    deleteByIdMutate,
    currentUser.userInfo?.userName,
    deActiveUser,
    activeUser,
    unsetAdmin,
    setAdmin,
  ]);

  return (
    <>
      <HeaderTitleComponent
        title='User Management'
        rightSection={isAnyPermissions([AclPermission.userManageCreate]) && <CreateUserModal fetchListUsers={fetchListUsers} />}
      />
      <Table ref={tableRef} {...tableProps} />
    </>
  );
};
export default UserManagement;
