import React, { useCallback, useMemo, useState } from 'react';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { SortType } from '@common/constants/SortType';
import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { DEFAULT_DEBOUNCE_TIME } from '@components/InfiniteScrollAccordion';
import Table from '@components/table';
import useFetch from '@core/hooks/useFetch';
import { Box, Flex } from '@mantine/core';
import { PaginationModel } from '@models/PaginationModel';
import { ColumnType, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import { ExecutionGroup } from '@core/schema/ExecutionGroup';
import UpdateExecutionGroupButton from './UpdateExecutionGroupButton';
import CreateExecutionGroupButton from './CreateExecutionGroupButton';
import DeleteExecutionGroupButton from './DeleteExecutionGroupButton';
import { AclPermission } from '@models/AclPermission';
import equal from 'fast-deep-equal';

export const COLUMNS: ColumnType<ExecutionGroup>[] = [
  {
    title: 'Name',
    name: 'name',
    width: '40%',
  },
  {
    title: 'Description',
    name: 'description',
    width: '50%',
  },
];

const ExecutionGroupConfig = () => {
  const [tableAffected, setTableAffected] = useState<PaginationModel>(DEFAULT_PAGINATION_REQUEST);
  const { data: executionGroupData, refetch } = useFetch(ExecutionGroupApi.findAllWithPaging(tableAffected), { placeholderData: (prev) => prev });
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<ExecutionGroup>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );

  const tableViewListRolesProps: KanbanTableProps<ExecutionGroup> = useMemo(() => {
    return {
      columns: COLUMNS,
      data: executionGroupData?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: executionGroupData?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.executionGroupView, AclPermission.executionGroupEdit]}>
                <UpdateExecutionGroupButton executionGroupId={data.id} onUpdateSuccess={refetch} />
              </GuardComponent>

              <GuardComponent requirePermissions={[AclPermission.executionGroupDelete]}>
                <DeleteExecutionGroupButton executionGroup={data} onDeleteSuccess={refetch} />
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [executionGroupData?.data?.content, executionGroupData?.data?.totalElements, handleUpdateTablePagination, refetch, tableAffected]);

  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='List of execution group'
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <GuardComponent requirePermissions={[AclPermission.executionGroupCreate]}>
              <CreateExecutionGroupButton onCreateSuccess={refetch} />
            </GuardComponent>
          </Flex>
        }
      />
      <Table {...tableViewListRolesProps} />
    </Box>
  );
};

export default ExecutionGroupConfig;
