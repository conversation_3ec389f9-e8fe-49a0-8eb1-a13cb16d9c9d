import React, { useMemo } from 'react';
import { Box, Text, Tooltip } from '@mantine/core';

interface TruncatedTextProps {
  text: string;
  isJson?: boolean;
}

interface JSONContent {
  type?: string;
  text?: string;
  attrs?: { label?: string };
  content?: JSONContent[];
}

export function renderTruncatedJSON(node: JSONContent): React.ReactNode[] {
  const nodes: React.ReactNode[] = [];

  if (node.type === 'text' && node.text) {
    nodes.push(<Text component='span'>{node.text.replace(/ /g, '\u00A0')}</Text>);
    return nodes;
  }

  if (node.type === 'mention' && node.attrs?.label) {
    const mention = `@${node.attrs.label}`;
    nodes.push(
      <Text key={`m-${node.attrs.label}`} c='var(--mantine-color-blue-6)' component='span'>
        {mention}
      </Text>,
    );
    return nodes;
  }

  if (node.content?.length) {
    for (const child of node.content) {
      const childNodes = renderTruncatedJSON(child);
      nodes.push(...childNodes);
    }
  }
  return nodes;
}

const TruncatedText: React.FC<TruncatedTextProps> = ({ isJson, text }) => {
  const parsedJson = useMemo(() => {
    try {
      return isJson ? (JSON.parse(text) as JSONContent) : null;
    } catch {
      return null;
    }
  }, [isJson, text]);

  const content = parsedJson ? renderTruncatedJSON(parsedJson) : text;

  return (
    <Box w={300}>
      <Tooltip label={<Box style={{ whiteSpace: 'pre-wrap', maxWidth: 900, wordBreak: 'break-word' }}>{content}</Box>} withArrow withinPortal>
        <Text truncate='end'>{content}</Text>
      </Tooltip>
    </Box>
  );
};

export default TruncatedText;
