/* eslint-disable react/display-name */
import { SuggestionKeyDownProps, SuggestionProps } from '@tiptap/suggestion';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import React from 'react';
import { CustomObjectApi } from '@api/CustomObjectApi';
import useFetch from '@core/hooks/useFetch';
import { Card, ScrollArea } from '@mantine/core';
import { MentionItem } from './MentionItem';
import { useDebounceCallback } from 'kanban-design-system';
import { DEFAULT_DEBOUNCE_TIME } from '@common/constants/ValidationConstant';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST } from '@pages/admins/groupConfig/Constants';

export interface MentionListProps extends SuggestionProps {
  specialMentions?: { id: string; name: string }[];
  specialMentionOnly: boolean;
}

export interface MentionListActions {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
}
export const MentionList = forwardRef<MentionListActions, MentionListProps>(({ command, query, specialMentionOnly, specialMentions }, ref) => {
  const [tableAffected, setTableAffected] = useState(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST);
  const { data: listCustomObject, refetch: refetchList } = useFetch(CustomObjectApi.findAll(tableAffected), {
    enabled: !specialMentionOnly,
    placeholderData: (prev) => prev,
    showLoading: false,
  });

  const debounceFunc = useDebounceCallback((query) => setTableAffected((prev) => ({ ...prev, name: query })), DEFAULT_DEBOUNCE_TIME);

  useEffect(() => {
    if (!specialMentionOnly) {
      debounceFunc(query);
    }
  }, [debounceFunc, query, refetchList, specialMentionOnly]);

  const allItems = useMemo(() => {
    const lowerQuery = query.toLowerCase();

    if (specialMentionOnly && specialMentions) {
      return specialMentions.filter((mention) => mention.name.toLowerCase().includes(lowerQuery));
    }

    const filteredItems = listCustomObject?.data?.content || [];

    const filteredSpecials = specialMentions?.filter((mention) => mention.name.toLowerCase().includes(lowerQuery)) || [];

    return [...filteredSpecials, ...filteredItems];
  }, [listCustomObject, specialMentions, query, specialMentionOnly]);

  // Handle selection of an item
  const handleCommand = (index: number) => {
    const selectedItem = allItems[index];
    if (selectedItem) {
      command({ id: selectedItem.id, label: selectedItem.name });
    }
  };

  const [hoverIndex, setHoverIndex] = useState(0);
  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      const { key } = event;

      if (key === 'ArrowUp') {
        setHoverIndex((prev) => Math.max(prev - 1, 0));
        return true;
      }

      if (key === 'ArrowDown') {
        setHoverIndex((prev) => Math.min(prev + 1, allItems.length - 1));
        return true;
      }

      if (key === 'Enter') {
        handleCommand(hoverIndex);
        return true;
      }

      return false;
    },
  }));

  if (allItems.length === 0) {
    return null;
  }

  return (
    <Card>
      <ScrollArea.Autosize mah={300}>
        {allItems.map((item, index) => (
          <MentionItem key={item.id} isActive={hoverIndex === index} onMouseEnter={() => setHoverIndex(index)} onClick={() => handleCommand(index)}>
            {item.name}
          </MentionItem>
        ))}
      </ScrollArea.Autosize>
    </Card>
  );
});
