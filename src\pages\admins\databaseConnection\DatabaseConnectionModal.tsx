import React, { useMemo } from 'react';
import {
  KanbanButton,
  KanbanInput,
  KanbanTextarea,
  KanbanSelect,
  KanbanNumberInput,
  KanbanRadio,
  KanbanTooltip,
  KanbanIconButton,
} from 'kanban-design-system';
import { useForm, zodResolver } from '@mantine/form';
import useMutate from '@core/hooks/useMutate';

import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { DatabaseConnectionApi } from '@api/DatabaseConnectionApi';
import { DatabaseConnection } from '@core/schema/DatabaseConnection';
import { DatabaseConnectionModel, DatabaseConnectionModelSchema } from '@models/DatabaseConnectionModel';
import { DATABASE_ORACLE_CONNECT_TYPE, DATABASE_TYPE } from '@common/constants/DatabaseConnectionConstants';
import { IconEdit, IconPlus } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Flex } from '@mantine/core';
import {
  DATABASE_DESCRIPTION_MAX_LENGTH,
  DATABASE_HOST_MAX_LENGTH,
  DATABASE_NAME_MAX_LENGTH,
  DATABASE_PASSWORD_MAX_LENGTH,
  DATABASE_PORT_MAX_LENGTH,
  DATABASE_PORT_MIN_LENGTH,
  DATABASE_SID_OR_SERVICE_MAX_LENGTH,
  DATABASE_USERNAME_MAX_LENGTH,
} from '@common/constants/ValidationConstant';
import Modal from '@components/Modal';

type DatabaseConnectionModalProps = {
  refetchList: () => void;
  data?: DatabaseConnection;
};

const DatabaseConnectionModal: React.FC<DatabaseConnectionModalProps> = ({ data, refetchList }) => {
  const isUpdateMode = !!data;

  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const dataInitForm: DatabaseConnectionModel = {
    id: data?.id,
    name: data?.name || '',
    description: data?.description,
    type: data?.type || DATABASE_TYPE.ORACLE,
    host: data?.host || '',
    port: data?.port || 0,
    sid: data?.sid,
    serviceName: data?.serviceName,
    userName: data?.userName || '',
    password: data?.password,
    oracleConnectType: data?.oracleConnectType || DATABASE_ORACLE_CONNECT_TYPE.SID,
    databaseName: data?.databaseName || '',
  };

  const { mutate: saveMutate } = useMutate(DatabaseConnectionApi.save, {
    successNotification: isUpdateMode ? `Update database connection successfully` : 'Create database connection successfully',
    onSuccess: () => {
      setValues(dataInitForm);
      closeModal();
      refetchList();
    },
  });

  const { mutate: testConnectionMutate } = useMutate(DatabaseConnectionApi.testConnection, {
    successNotification: 'Connection successfully',
  });

  const { getInputProps, isValid, setFieldValue, setValues, values } = useForm({
    initialValues: dataInitForm,
    validate: zodResolver(DatabaseConnectionModelSchema),
    validateInputOnChange: true,
  });

  const handleSave = () => {
    if (isValid()) {
      saveMutate(DatabaseConnectionModelSchema.parse(values));
    }
  };
  const oracleConnectionTypeRadio = useMemo(() => {
    return [
      {
        value: DATABASE_ORACLE_CONNECT_TYPE.SID,
        label: (
          <KanbanInput
            disabled={values.oracleConnectType === DATABASE_ORACLE_CONNECT_TYPE.SERVICE_NAME}
            key={'sid'}
            description={getMaxLengthMessage(DATABASE_SID_OR_SERVICE_MAX_LENGTH)}
            maxLength={DATABASE_SID_OR_SERVICE_MAX_LENGTH}
            required
            name='sid'
            label={'SID'}
            placeholder={'SID'}
            {...getInputProps('sid')}
          />
        ),
      },
      {
        value: DATABASE_ORACLE_CONNECT_TYPE.SERVICE_NAME,
        label: (
          <KanbanInput
            disabled={values.oracleConnectType === DATABASE_ORACLE_CONNECT_TYPE.SID}
            key={'serviceName'}
            description={getMaxLengthMessage(DATABASE_SID_OR_SERVICE_MAX_LENGTH)}
            maxLength={DATABASE_SID_OR_SERVICE_MAX_LENGTH}
            required
            name='serviceName'
            label={'Service Name'}
            placeholder={'Service Name'}
            {...getInputProps('serviceName')}
          />
        ),
      },
    ];
  }, [getInputProps, values.oracleConnectType]);

  return (
    <>
      {!!data && (
        <KanbanTooltip label='Edit'>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              setValues(dataInitForm);
              openModal();
            }}>
            <IconEdit />
          </KanbanIconButton>
        </KanbanTooltip>
      )}

      {!data && (
        <HeaderTitleComponent
          title='Database connections'
          rightSection={
            <Flex direction='row' gap='xs' align='center'>
              <KanbanButton
                size='xs'
                onClick={() => {
                  setValues(dataInitForm);
                  openModal();
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </Flex>
          }
        />
      )}
      <Modal
        size={'xl'}
        opened={openedModal}
        onClose={() => {
          closeModal();
        }}
        title={isUpdateMode ? `Update database connection ${data.name}` : 'Create new'}
        actions={
          <KanbanButton onClick={handleSave} disabled={!isValid() || !values.password}>
            Save
          </KanbanButton>
        }>
        <form>
          <KanbanInput
            required
            label='Connection Name'
            description={getMaxLengthMessage(DATABASE_NAME_MAX_LENGTH)}
            placeholder='Enter partner name'
            {...getInputProps('name')}
            maxLength={DATABASE_NAME_MAX_LENGTH}
          />
          <KanbanTextarea
            description={getMaxLengthMessage(DATABASE_DESCRIPTION_MAX_LENGTH)}
            maxLength={DATABASE_DESCRIPTION_MAX_LENGTH}
            placeholder='Description'
            label='Description'
            {...getInputProps('description')}
          />
          <KanbanSelect
            name='type'
            placeholder='Database type'
            label='Database type'
            required={true}
            allowDeselect={false}
            data={[DATABASE_TYPE.ORACLE, DATABASE_TYPE.Microsoft_SQL]}
            {...getInputProps('type')}
            onChange={(val) => {
              getInputProps('type').onChange(val);
              if (DATABASE_TYPE.ORACLE === val) {
                setFieldValue('databaseName', '');
              } else {
                setFieldValue('oracleConnectType', undefined);
              }
            }}
          />
          <KanbanInput
            description={getMaxLengthMessage(DATABASE_HOST_MAX_LENGTH)}
            maxLength={DATABASE_HOST_MAX_LENGTH}
            required
            label='Host'
            placeholder='Enter host'
            {...getInputProps('host')}
          />
          <KanbanNumberInput
            max={DATABASE_PORT_MAX_LENGTH}
            min={DATABASE_PORT_MIN_LENGTH}
            required
            label='Port'
            description='Please enter a port number within the valid range 0 to 65535.'
            placeholder='Enter port'
            {...getInputProps('port')}
          />
          <KanbanInput
            required
            label='User Name'
            description={getMaxLengthMessage(DATABASE_USERNAME_MAX_LENGTH)}
            placeholder='Enter user name'
            {...getInputProps('userName')}
            maxLength={DATABASE_USERNAME_MAX_LENGTH}
          />
          <KanbanInput
            required
            label='Password'
            type='password'
            description={getMaxLengthMessage(DATABASE_PASSWORD_MAX_LENGTH)}
            placeholder='Enter password'
            {...getInputProps('password')}
            maxLength={DATABASE_PASSWORD_MAX_LENGTH}
          />
          {DATABASE_TYPE.ORACLE === values.type ? (
            <KanbanRadio
              group={{
                ...getInputProps('oracleConnectType'),
                name: 'oracleConnectType',
                withAsterisk: true,
                defaultValue: DATABASE_ORACLE_CONNECT_TYPE.SID,
                display: 'column',
                onChange: (val) => {
                  getInputProps('oracleConnectType').onChange(val);
                  setFieldValue('serviceName', '');
                  setFieldValue('sid', '');
                },
              }}
              radios={oracleConnectionTypeRadio}
            />
          ) : (
            <KanbanInput
              required
              label='Database name'
              description={getMaxLengthMessage(DATABASE_PASSWORD_MAX_LENGTH)}
              placeholder='Enter database name'
              {...getInputProps('databaseName')}
              maxLength={DATABASE_PASSWORD_MAX_LENGTH}
            />
          )}
        </form>
        <KanbanButton
          disabled={!isValid()}
          onClick={() => {
            const data = DatabaseConnectionModelSchema.safeParse(values);
            if (data.success) {
              testConnectionMutate(data.data);
            }
          }}>
          Test connection
        </KanbanButton>
      </Modal>
    </>
  );
};

export default DatabaseConnectionModal;
