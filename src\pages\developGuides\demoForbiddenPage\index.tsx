import { AclPermission } from 'models/AclPermission';
import GuardComponent from 'components/GuardComponent';
import React from 'react';

export const DemoForbiddenPage = () => {
  return <>This is admin page</>;
};

export const DemoForbiddenComponentPage = () => {
  return (
    <>
      This is normal page, Component below just admin can see:
      <GuardComponent requirePermissions={[AclPermission.monitorAlertView]}>
        <div>This component admin can see</div>
      </GuardComponent>
      <br />
      Or hidden if unauthorize:
      <GuardComponent requirePermissions={[AclPermission.monitorAlertView]}>
        <div>This component admin can see</div>
      </GuardComponent>
    </>
  );
};

export default DemoForbiddenPage;
