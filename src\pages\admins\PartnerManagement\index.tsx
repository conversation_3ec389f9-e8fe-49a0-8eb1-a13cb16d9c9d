import React, { useState, useMemo, useRef, useCallback } from 'react';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { ActionIcon, Box, Flex } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { IconEdit } from '@tabler/icons-react';
import { SortType } from '@common/constants/SortType';
import { PaginationRequest } from '@api/Type';
import useMutate from '@core/hooks/useMutate';
import CreateOrUpdateModal from './CreateOrUpdateModal';
import { EmailPartnerApi } from '@api/EmailPartnerApi';
import { EmailPartner } from '@core/schema/EmailPartner';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { RawValuesChipDisplay } from '@components/RawValuesChipDisplay';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';

const columns: ColumnType<EmailPartner>[] = [
  {
    title: 'Name',
    name: 'name',
    width: '20%',
  },
  {
    name: 'addresses',
    title: 'Contact',
    width: '50%',
    customRender: (_, record: EmailPartner) => <RawValuesChipDisplay values={record.addresses} />,
  },
];
export const PartnerManagement = () => {
  const [tableAffected, setTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const [openedModalCreatePartner, { close: closeModalCreatePartner, open: openModalCreatePartner }] = useDisclosure(false);
  const [rowSelected, setRowSelected] = useState<EmailPartner | undefined>(undefined);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const { data: listEmailPartner, refetch: refetchList } = useFetch(EmailPartnerApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const { mutate: deleteByIdMutate } = useMutate(EmailPartnerApi.deleteById, {
    successNotification: 'Deleted successfully.!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<EmailPartner>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );

  const tableViewListRolesProps: KanbanTableProps<EmailPartner> = useMemo(() => {
    return {
      columns: columns,
      data: listEmailPartner?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listEmailPartner?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          handleUpdateTablePagination(dataSet);
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.emailPartnerEdit]}>
                <KanbanTooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setRowSelected(data);
                      openModalCreatePartner();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.emailPartnerDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [deleteByIdMutate, handleUpdateTablePagination, listEmailPartner?.data?.content, listEmailPartner?.data?.totalElements, openModalCreatePartner]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='Partner management'
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <GuardComponent requirePermissions={[AclPermission.emailPartnerCreate]}>
              <KanbanButton
                size='xs'
                onClick={() => {
                  setRowSelected(undefined);
                  openModalCreatePartner();
                }}
                leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <Table ref={tableRef} {...tableViewListRolesProps} />
      <CreateOrUpdateModal opened={openedModalCreatePartner} onClose={closeModalCreatePartner} emailPartner={rowSelected} refetchList={refetchList} />
    </Box>
  );
};
export default PartnerManagement;
