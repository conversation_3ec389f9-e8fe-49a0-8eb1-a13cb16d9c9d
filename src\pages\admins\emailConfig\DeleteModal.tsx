import React from 'react';
import { KanbanButton } from 'kanban-design-system';
import { EmailConfigApi } from '@api/EmailConfigApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import WarningAlertComponent from '@components/WarningAlertComponent';
import { DeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';

type DeleteModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  emailConfigId?: number;
  emailConfig?: string;
};

const DeleteModal: React.FC<DeleteModalProps> = ({ emailConfig, emailConfigId, onClose, opened, refetchList }) => {
  const { data: listCollectEmailConfig, refetch: refetch } = useFetch(EmailConfigApi.findAllByEmailConfigId(emailConfigId ?? 0), {
    enabled: !!emailConfigId && opened,
  });
  const { mutate: deleteByIdMutate } = useMutate(EmailConfigApi.deleteById, {
    successNotification: 'Deleted successfully!',
    onSuccess: () => {
      refetchList();
      refetch();
      onClose();
    },
  });
  const isDisabledButtonConfirm = listCollectEmailConfig?.data && listCollectEmailConfig.data.length > 0;
  return (
    <Modal
      size='xl'
      opened={opened}
      onClose={() => {
        onClose();
      }}
      title={'Delete email connection'}
      actions={
        <KanbanButton onClick={() => deleteByIdMutate(emailConfigId ?? 0)} disabled={isDisabledButtonConfirm}>
          Confirm
        </KanbanButton>
      }>
      {listCollectEmailConfig?.data && (
        <WarningAlertComponent
          mainEntity={`Email connection ${emailConfig}`}
          dependencyEntity='collect email configs'
          dependencies={listCollectEmailConfig?.data.map((e) => e.name)}
          color='red'
          isDeleted={true}
        />
      )}
      {!isDisabledButtonConfirm && <DeleteConfirmMessage name={emailConfig} />}
    </Modal>
  );
};

export default DeleteModal;
