import React from 'react';
import { <PERSON>, Button, ColorPicker, Popover } from '@mantine/core';
import { colorHighlightPicker } from './DataSelect';
import { IconHighlight } from '@tabler/icons-react';
import classes from './Template.module.scss';

type CustomColorPickerProps = {
  onChange: (color: string | undefined) => void;
};

const HighlightColorPicker: React.FC<CustomColorPickerProps> = ({ onChange }) => {
  return (
    <Popover position='bottom' withArrow withinPortal>
      <Popover.Target>
        <Box className={classes.mantineButton}>
          <IconHighlight size={30} />
        </Box>
      </Popover.Target>

      <Popover.Dropdown p='5px'>
        <Box>
          <ColorPicker
            size='8rem'
            format='hex'
            onChange={(color) => {
              onChange(color);
            }}
            withPicker={false}
            swatchesPerRow={5}
            swatches={colorHighlightPicker}
          />
          <Button size='xs' miw='8rem' variant='outline' color='gray' onClick={() => onChange(undefined)}>
            No color
          </Button>
        </Box>
      </Popover.Dropdown>
    </Popover>
  );
};

export default HighlightColorPicker;
