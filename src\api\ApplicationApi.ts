import { Application, ApplicationSchema, createPageSchema, createResponseSchema, ResponseData } from '@core/schema';
import { z } from 'zod';
import { BaseURL } from '@common/constants/BaseUrl';
import { ApplicationWithPrioritiesSchema } from '@core/schema/ApplicationWithPriorities';
import { ApplicationPaginationRequest } from '@models/ApplicationModel';
import { WebHookSchema } from '@core/schema/WebHook';
import { ExportFileRequest } from '@models/ExportFileModel';
import { createRequest } from './Utils';
import { AlertGroupStatusEnum } from '@common/constants/AlertGroupStatusConstant';
import { ApplicationDependenciesSchema } from '@core/schema/ApplicationDependencies';
import { ExportData } from '@core/schema/ExportData';

export class ApplicationApi {
  static findApplicationWithPriorityByAlertGroupStatusAndServiceId(alertGroupStatus: AlertGroupStatusEnum, serviceId: string) {
    return createRequest({
      url: `${BaseURL.application}/with-priorities`,
      method: 'GET',
      schema: createResponseSchema(z.array(ApplicationWithPrioritiesSchema)),
      params: {
        alertGroupStatus,
        serviceId,
      },
    });
  }
  static findAllByServiceIdIn(searchParams: ApplicationPaginationRequest) {
    return createRequest({
      url: BaseURL.application,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(ApplicationSchema)),
      params: searchParams,
    });
  }
  static findById(id: string) {
    return createRequest({
      url: `${BaseURL.application}/:id`,
      method: 'GET',
      schema: createResponseSchema(ApplicationSchema),
      pathVariable: {
        id,
      },
    });
  }
  static findAllWebhookById(id: string) {
    return createRequest({
      url: `${BaseURL.application}/:id/webhooks`,
      method: 'GET',
      schema: createResponseSchema(z.array(WebHookSchema)),
      pathVariable: {
        id,
      },
    });
  }
  static save(body: Application) {
    return createRequest<ResponseData<Application>>({
      url: BaseURL.application,
      method: 'POST',
      data: body,
    });
  }

  static deleteById(id: string) {
    return createRequest({
      url: `${BaseURL.application}/:id`,
      method: 'DELETE',
      pathVariable: {
        id,
      },
      schema: z.string(),
    });
  }
  static findAllDependenciesById(id: string) {
    return createRequest({
      url: `${BaseURL.application}/:id/dependencies`,
      method: 'GET',
      schema: createResponseSchema(ApplicationDependenciesSchema),
      pathVariable: {
        id,
      },
    });
  }

  static export(request: ExportFileRequest) {
    return createRequest<ExportData>({
      url: `${BaseURL.application}/export`,
      method: 'POST',
      data: request,
    });
  }
}
