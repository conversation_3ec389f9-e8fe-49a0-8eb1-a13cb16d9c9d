import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useLocalStorage } from 'kanban-design-system';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import classes from './MonitorAlert.module.css';
import { Flex, Loader, Text, Title } from '@mantine/core';
import { DEFAULT_ALERT_GROUP_PAGINATION_REQUEST, DEFAULT_TIME_INTERVAL_REFRESH, SELECTED_ALERT_ROW_COLOR } from './Constants';
import { dateToString } from '@common/utils/DateUtils';
import { AlertGroupApi } from '@api/AlertGroupApi';
import { AlertGroup } from '@core/schema/AlertGroup';
import { useInterval } from '@mantine/hooks';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import InfiniteScrollTable from '@components/dragTable/InfiniteScrollTable';
import { Column } from '@components/dragTable/Types';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { slice } from 'lodash';
import { MonitorAlertPageContext } from './MonitorAlertPageContext';

const TableCellValue = ({ content }: { content: React.ReactNode }) => {
  return <Text classNames={{ root: classes.tableCell }}>{content}</Text>;
};

const AlertTable = () => {
  const { reloadData, searchParams, selectedAlertGroups, setSelectedAlertGroups, sortEffect } = useContext(MonitorAlertPageContext);
  const [refetchInterval] = useLocalStorage(LocalStorageKey.TIME_INTERVAL_REFRESH_KEY, DEFAULT_TIME_INTERVAL_REFRESH);
  const { errorUpdateCount, fetchNextPage, flatData, isFetching } = useInfiniteFetch(
    AlertGroupApi.findAllByPostMethod(searchParams, {
      ...DEFAULT_ALERT_GROUP_PAGINATION_REQUEST,
      sortBy: sortEffect.sortBy,
      sortOrder: sortEffect.direction,
    }),
    {
      showLoading: false,
      placeholderData: (prev) => prev,
    },
  );

  const { start, stop } = useInterval(() => {
    if (selectedAlertGroups?.length === 0 && refetchInterval !== 0) {
      // refetch();
      reloadData();
    }
  }, refetchInterval * 1000);
  useEffect(() => {
    start();
    return stop;
  }, [start, stop]);
  useEffect(() => {
    setSelectedAlertGroups((prev) => prev.filter((alertGroup) => flatData.some((ele) => ele.id === alertGroup.id)));
  }, [flatData, setSelectedAlertGroups]);
  const [startSelectedIndex, setStartSelectedIndex] = useState<number>(-1);
  const selectRowHandler = useCallback(
    (record: AlertGroup, index: number, event: React.MouseEvent<HTMLTableRowElement, MouseEvent>) => {
      const isAlertGroup = record.alertAmount > 1;
      if (isAlertGroup) {
        setStartSelectedIndex(-1);
        if (selectedAlertGroups.some((ele) => ele.id === record.id)) {
          setSelectedAlertGroups([]);
        } else {
          setSelectedAlertGroups([record]);
        }
        return;
      }
      if (event.shiftKey) {
        if (startSelectedIndex === -1) {
          setStartSelectedIndex(index);
          setSelectedAlertGroups((prev) => [...prev, record].filter((alertGroup) => alertGroup.alertAmount <= 1));
        } else {
          const start = index > startSelectedIndex ? startSelectedIndex : index;
          const end = index > startSelectedIndex ? index : startSelectedIndex;
          setSelectedAlertGroups(slice(flatData, start, end + 1).filter((alertGroup) => alertGroup.alertAmount <= 1));
        }
        return;
      }
      if (event.ctrlKey) {
        setStartSelectedIndex(-1);
        const isSelected = selectedAlertGroups.some((alert) => alert.id === record.id);
        if (isSelected) {
          setSelectedAlertGroups((prev) => prev.filter((ele) => ele.id !== record.id));
        } else {
          setSelectedAlertGroups([...selectedAlertGroups, record].filter((alertGroup) => alertGroup.alertAmount <= 1));
        }
        return;
      }
      setStartSelectedIndex(index);
      setSelectedAlertGroups((prev) => (prev.length === 1 && prev.at(0)?.id === record.id ? [] : [record]));
    },
    [flatData, selectedAlertGroups, setSelectedAlertGroups, startSelectedIndex],
  );

  const COLUMNS = useMemo<Column<AlertGroup>[]>(
    () => [
      {
        title: 'Service name',
        id: 'serviceName',
        render: (record) => <TableCellValue content={record.serviceName} />,
        width: '14%',
        sortable: true,
      },
      {
        title: 'Application name',
        id: 'applicationName',
        render: (record) => (
          <Flex justify='space-between' align='center'>
            <TableCellValue content={record.applicationName} />
            {record.alertAmount && record.alertAmount >= 2 && (
              <Text bg='primary' className={classes.alertAmount}>
                {record.alertAmount}
              </Text>
            )}
          </Flex>
        ),
        width: '14%',
        sortable: true,
      },
      {
        title: 'Alert content',
        id: 'content',
        render: (record) => <TableCellValue content={record.content} />,
      },
      {
        title: 'Time',
        id: 'createdDate',
        render: (record) => <TableCellValue content={dateToString(record.createdDate, DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)} />,
        width: '13%',
        sortable: true,
      },
      {
        title: (
          <Flex justify='space-between' align='center'>
            <Title order={5}>Contact</Title>
            {isFetching && <Loader size={20} mr='lg' />}
          </Flex>
        ),
        id: 'recipient',
        render: (record) => <TableCellValue content={record.recipient} />,
        width: '16%',
        sortable: true,
      },
    ],
    [isFetching],
  );

  return (
    <InfiniteScrollTable
      columns={COLUMNS}
      data={flatData}
      onScrollToBottom={() => {
        if (errorUpdateCount < 1) {
          fetchNextPage();
        }
      }}
      sortEffect={sortEffect}
      customProps={{
        table: {
          className: classes.alertTable,
        },
        tbodyTr: (record, index) => ({
          style: {
            cursor: 'pointer',
            borderBottom: '1px solid var(--mantine-color-gray-4)',
            backgroundColor: selectedAlertGroups.some((alert) => alert.id === record.id) ? SELECTED_ALERT_ROW_COLOR : record.priorityColor,
          },
          onClick: (event) => selectRowHandler(record, index, event),
        }),
      }}
    />
  );
};

export default AlertTable;
