import { createCursorPageSchema, createResponseSchema } from '@core/schema';
import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { ExecutionHistorySchema } from '@core/schema/ExecutionHistory';
import { ExecutionHistoryCursor, ExecutionHistoryCursorSchema } from '@core/schema/ExecutionHistoryCursor';
import { CursorPagingRequestModel } from '@models/CursorPagingRequestModel';

export class ExecutionHistoryApi {
  static findAll(request: CursorPagingRequestModel<ExecutionHistoryCursor>) {
    return createRequest({
      url: BaseURL.executionHistory,
      method: 'GET',
      params: request,
      schema: createResponseSchema(createCursorPageSchema(ExecutionHistorySchema, ExecutionHistoryCursorSchema)),
    });
  }
  static findById(id: string) {
    return createRequest({
      url: `${BaseURL.executionHistory}/:id`,
      method: 'GET',
      pathVariable: {
        id,
      },
      schema: createResponseSchema(ExecutionHistorySchema),
    });
  }
}
