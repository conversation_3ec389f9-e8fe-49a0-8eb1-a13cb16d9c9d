import { USER_NAME_REGEX } from '@common/constants/RegexConstant';
import { REGEX_USER_NAME_ERROR } from '@core/message/MesageConstant';
import { z } from 'zod';
import { isEmpty } from 'lodash';

export type RefreshTokenRequest = {
  refreshToken: string;
};

export const LoginRequestSchema = z
  .object({
    userName: z.string().trim().min(1).regex(USER_NAME_REGEX, { message: REGEX_USER_NAME_ERROR }),
    password: z.string(),
    userLocal: z.boolean().default(false),
  })

  .superRefine((value, ctx) => {
    if (!value.userName || isEmpty(value.userName)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['userName'],
        message: 'Please enter your username',
      });
    }
    if (!value.password || isEmpty(value.password)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['password'],
        message: 'Please enter your password',
      });
    }
  });

export type LoginModel = z.infer<typeof LoginRequestSchema>;
