import { useDisclosure } from '@mantine/hooks';
import { KanbanButton, KanbanIconButton, KanbanTableSelectHandleMethods } from 'kanban-design-system';
import React, { useRef, useState } from 'react';
import RoleSetting, { SettingRoleHandle } from './RoleSetting';
import { IconCopyPlus, IconEdit, IconPlus } from '@tabler/icons-react';
import { Tooltip } from '@mantine/core';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { RoleModel } from '@models/RoleModel';
import Modal from '@components/Modal';

interface RoleSettingModalProps {
  tableRef: React.RefObject<KanbanTableSelectHandleMethods>;
  roleId?: number;
  isCopy?: boolean;
}

export const RoleSettingModal = (props: RoleSettingModalProps) => {
  const { isCopy, roleId, tableRef } = props;
  const [openedModalRoleSetting, { close: closeModalRoleSetting, open: openModalRoleSetting }] = useDisclosure(false);
  const roleSettingRef = useRef<SettingRoleHandle>(null);
  const [isValidData, setValidData] = useState<boolean>(false);
  const saveRole = () => {
    if (roleSettingRef.current) {
      roleSettingRef.current?.saveRoleSetting();
      tableRef.current?.deselectAll();
    }
  };
  return (
    <>
      {!roleId ? (
        <KanbanButton
          onClick={() => {
            openModalRoleSetting();
          }}
          leftSection={<IconPlus />}>
          Create Role
        </KanbanButton>
      ) : (
        <Tooltip label={isCopy ? 'Clone Role' : 'Edit Role'}>
          <KanbanIconButton
            variant='transparent'
            size={'sm'}
            onClick={() => {
              openModalRoleSetting();
            }}>
            {isCopy ? <IconCopyPlus /> : <IconEdit />}
          </KanbanIconButton>
        </Tooltip>
      )}

      <Modal
        size={'80%'}
        opened={openedModalRoleSetting}
        onClose={closeModalRoleSetting}
        title={isCopy ? 'Clone Role' : roleId ? 'Edit Role' : 'Create new Role'}
        actions={
          <GuardComponent requirePermissions={[AclPermission.roleManageEdit, AclPermission.roleManageCreate]}>
            <KanbanButton disabled={!isValidData} onClick={saveRole}>
              Save
            </KanbanButton>
          </GuardComponent>
        }>
        <RoleSetting
          ref={roleSettingRef}
          id={roleId}
          isCopy={isCopy}
          onChange={(data: RoleModel) => {
            const isValid = !!data.name && !!data.permissions && data.permissions.length > 0;
            setValidData(isValid);
          }}
          closeModalRoleSetting={closeModalRoleSetting}
        />
      </Modal>
    </>
  );
};
