/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import React from 'react';

import CustomSelect from '../components/CustomSelect';
import { UNITS } from '../ConstantsSelect';
import { DEFAULT_LOCALE_EN } from '../LocaleSelect';
import { MinutesProps } from '../TypesSelect';

export default function Minutes(props: MinutesProps) {
  const { className, clockFormat, disabled, filterOption, leadingZero, locale, mode, period, periodicityOnDoubleClick, readOnly, setValue, value } =
    props;

  return (
    <div>
      {period === 'hour'
        ? locale.prefixMinutesForHourPeriod !== '' && <span>{locale.prefixMinutesForHourPeriod || DEFAULT_LOCALE_EN.prefixMinutesForHourPeriod}</span>
        : locale.prefixMinutes !== '' && <span>{locale.prefixMinutes || DEFAULT_LOCALE_EN.prefixMinutes}</span>}

      <CustomSelect
        placeholder={
          period === 'hour'
            ? locale.emptyMinutesForHourPeriod || DEFAULT_LOCALE_EN.emptyMinutesForHourPeriod
            : locale.emptyMinutes || DEFAULT_LOCALE_EN.emptyMinutes
        }
        value={value}
        unit={UNITS[0]}
        setValue={setValue}
        locale={locale}
        className={className}
        disabled={disabled}
        readOnly={readOnly}
        leadingZero={leadingZero}
        clockFormat={clockFormat}
        period={period}
        periodicityOnDoubleClick={periodicityOnDoubleClick}
        mode={mode}
        filterOption={filterOption}
      />

      {period === 'hour' && locale.suffixMinutesForHourPeriod !== '' && (
        <span>{locale.suffixMinutesForHourPeriod || DEFAULT_LOCALE_EN.suffixMinutesForHourPeriod}</span>
      )}
    </div>
  );
}
