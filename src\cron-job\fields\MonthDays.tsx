/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import React, { useMemo } from 'react';

import CustomSelect from '../components/CustomSelect';
import { UNITS } from '../ConstantsSelect';
import { DEFAULT_LOCALE_EN } from '../LocaleSelect';
import { MonthDaysProps } from '../TypesSelect';

export default function MonthDays(props: MonthDaysProps) {
  const { className, disabled, filterOption, leadingZero, locale, mode, period, periodicityOnDoubleClick, readOnly, setValue, value, weekDays } =
    props;
  const noWeekDays = !weekDays || weekDays.length === 0;

  const placeholder = useMemo(() => {
    if (noWeekDays) {
      return locale.emptyMonthDays || DEFAULT_LOCALE_EN.emptyMonthDays;
    }
    return locale.emptyMonthDaysShort || DEFAULT_LOCALE_EN.emptyMonthDaysShort;
  }, [noWeekDays, locale.emptyMonthDaysShort, locale.emptyMonthDays]);

  const displayMonthDays = !readOnly || (value && value.length > 0) || ((!value || value.length === 0) && (!weekDays || weekDays.length === 0));

  if (!displayMonthDays) {
    return null;
  }

  return (
    <div>
      {locale.prefixMonthDays !== '' && <span>{locale.prefixMonthDays || DEFAULT_LOCALE_EN.prefixMonthDays}</span>}
      <CustomSelect
        placeholder={placeholder}
        value={value}
        setValue={setValue}
        unit={UNITS[2]}
        locale={locale}
        className={className}
        disabled={disabled}
        readOnly={readOnly}
        leadingZero={leadingZero}
        period={period}
        periodicityOnDoubleClick={periodicityOnDoubleClick}
        mode={mode}
        filterOption={filterOption}
      />
    </div>
  );
}
