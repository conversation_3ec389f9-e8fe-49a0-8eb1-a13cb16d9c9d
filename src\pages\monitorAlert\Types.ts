import { Application, Service } from '@core/schema';

export type TreeForm = {
  serviceId: string;
  applicationId: string;
};

export type FilterForm = {
  services: Service[];
  applications: Application[];
  recipient: string;
  content: string;
  alertPriorityConfigIds: string[];
};

export enum NavbarTabTypeEnum {
  TREE = 'TREE',
  FITLER = 'FILTER',
  SETTING = 'SETTINGs',
}

export type TimeIntervalRefresh = {
  value: number;
  label: string;
};
