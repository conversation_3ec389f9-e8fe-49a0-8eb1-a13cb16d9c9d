import React from 'react';
import { ActionIcon, Box, Flex, Paper, Stack, Transition } from '@mantine/core';
import { KanbanButton, KanbanInput, KanbanTooltip } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { IconChevronLeft, IconChevronRight, IconSearch } from '@tabler/icons-react';
import { SysLogFilterModel, SysLogFilterModelSchema } from '@models/SysLogFilterModel';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import FunctionAndActionFilter from './FunctionAndActionFilter';
import UserMultipleSelect from '@components/UserMultipleSelect';
import SysLogDateRangeFilter from './SysLogDateRangeFilter';
import { MESSAGE_SYS_LOG_MAX_LENGTH } from '@common/constants/ValidationConstant';

interface Props {
  onSearch: (sysLogFilter: SysLogFilterModel) => void;
  sysLogFilter: SysLogFilterModel;
}

const SysLogFilterForm = ({ onSearch, sysLogFilter }: Props) => {
  const [opended, { toggle }] = useDisclosure(true);
  const form = useForm({
    defaultValues: sysLogFilter,
    resolver: zodResolver(SysLogFilterModelSchema),
  });
  const { control, formState, getValues } = form;
  return (
    <Box bg='white'>
      <Transition mounted={opended} transition='slide-left' duration={150} timingFunction='ease'>
        {(transitionStyle) => (
          <Paper shadow='lg' p='sm' h='100%' w='350px' pos='absolute' top={0} right={0} style={{ ...transitionStyle, zIndex: 10 }}>
            <Stack gap='md'>
              <SysLogDateRangeFilter form={form} />
              <FunctionAndActionFilter form={form} />
              <Controller
                control={control}
                name='userNames'
                render={({ field: { onChange, value } }) => (
                  <UserMultipleSelect label='Log by' value={value} onChange={onChange} placeholder='Search user name' withSystemUser />
                )}
              />
              <Controller
                control={control}
                name='message'
                render={({ field: { onChange, value } }) => (
                  <KanbanInput label='Message' value={value} onChange={onChange} placeholder='Message' maxLength={MESSAGE_SYS_LOG_MAX_LENGTH} />
                )}
              />
              <Flex justify='flex-end'>
                <KanbanButton leftSection={<IconSearch />} onClick={() => onSearch(getValues())} mt='xs' disabled={!formState.isValid}>
                  Search
                </KanbanButton>
              </Flex>
            </Stack>

            <KanbanTooltip label='Close filter'>
              <ActionIcon onClick={toggle} pos='absolute' style={{ left: -20, top: '50%', transform: 'translateY(-50%)' }}>
                <IconChevronRight />
              </ActionIcon>
            </KanbanTooltip>
          </Paper>
        )}
      </Transition>
      <Transition mounted={!opended} transition='slide-left' duration={150} timingFunction='ease'>
        {(transitionStyle) => (
          <KanbanTooltip label='Open filter'>
            <ActionIcon
              onClick={toggle}
              pos='absolute'
              style={{ ...transitionStyle, zIndex: 10, right: -10, top: '50%', transform: 'translateY(-50%)' }}>
              <IconChevronLeft />
            </ActionIcon>
          </KanbanTooltip>
        )}
      </Transition>
    </Box>
  );
};

export default SysLogFilterForm;
