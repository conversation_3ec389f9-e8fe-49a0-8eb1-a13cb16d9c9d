import { CLEAR_WHEN_LOGOUT_KEYS } from '@common/constants/LocalStorageKeyConstants';
import { getConfigs } from 'core/configs/Configs';
import Keycloak from 'keycloak-js';

const configs = getConfigs();

let globalKeycloak: Keycloak;

function clearOnLogout() {
  CLEAR_WHEN_LOGOUT_KEYS.forEach((key) => localStorage.removeItem(key));
}

function createInstanceKeycloak(): Keycloak {
  if (!globalKeycloak) {
    globalKeycloak = new Keycloak({
      url: configs.keycloak.url,
      realm: configs.keycloak.realm,
      clientId: configs.keycloak.clientId,
    });
    globalKeycloak.onAuthLogout = clearOnLogout;
    globalKeycloak.onTokenExpired = () => {
      console.trace('token expired', globalKeycloak.token);
      globalKeycloak
        .updateToken(30)
        .then(() => {
          console.trace('token renew', globalKeycloak.token);
        })
        .catch((error) => {
          console.error(error);
          clearOnLogout();
        });
    };
  }
  return globalKeycloak;
}

const keycloak = createInstanceKeycloak();

const getKeyCloack = () => keycloak;

const doLogin = keycloak.login;

const doLogout = () => {
  keycloak.logout({
    redirectUri: configs.deployUrl,
  });
  clearOnLogout();
};

const getToken = () => keycloak.token;

const isLoggedIn = () => keycloak.authenticated;

const getUsername = () => keycloak.tokenParsed?.realm_access;

const hasRole = (roles: string[]) => roles.some((role: string) => keycloak.hasRealmRole(role));

const KeycloakService = {
  doLogin,
  doLogout,
  isLoggedIn,
  getToken,
  getUsername,
  hasRole,
  getKeyCloack,
};

export default KeycloakService;
