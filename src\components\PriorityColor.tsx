import React from 'react';
import { Box, MantineStyleProp } from '@mantine/core';

interface Props {
  backgroundColor: string;
  style?: Omit<MantineStyleProp, 'backgroundColor'>;
}

const PriorityColor = ({ backgroundColor, style }: Props) => {
  return (
    <Box
      style={{
        width: 40,
        height: 40,
        border: '1px solid gray',
        borderRadius: 'var(--mantine-radius-sm)',
        ...style,
        backgroundColor: backgroundColor,
      }}
    />
  );
};

export default PriorityColor;
