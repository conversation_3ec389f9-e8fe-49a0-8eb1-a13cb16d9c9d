import { Box } from '@mantine/core';
import { KanbanSelect, useLocalStorage } from 'kanban-design-system';
import React from 'react';
import { DEFAULT_TIME_INTERVAL_REFRESH, TIME_INTERVAL_REFRESHS } from '../Constants';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';

const Setting = () => {
  const [value, onChange] = useLocalStorage(LocalStorageKey.TIME_INTERVAL_REFRESH_KEY, DEFAULT_TIME_INTERVAL_REFRESH);

  return (
    <Box>
      <KanbanSelect
        name='interval'
        placeholder=''
        label='Time Interval Refresh'
        data={TIME_INTERVAL_REFRESHS.map((option) => ({ value: String(option.value), label: option.label }))}
        allowDeselect
        value={String(value)}
        onChange={(value) => {
          if (value) {
            onChange(parseInt(value));
          }
        }}
      />
    </Box>
  );
};

export default Setting;
