/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import React from 'react';

import CustomSelect from '../components/CustomSelect';
import { UNITS } from '../ConstantsSelect';
import { DEFAULT_LOCALE_EN } from '../LocaleSelect';
import { MonthsProps } from '../TypesSelect';

export default function Months(props: MonthsProps) {
  const { className, disabled, filterOption, humanizeLabels, locale, mode, period, periodicityOnDoubleClick, readOnly, setValue, value } = props;
  const optionsList = locale.months || DEFAULT_LOCALE_EN.months;

  return (
    <div>
      {locale.prefixMonths !== '' && <span>{locale.prefixMonths || DEFAULT_LOCALE_EN.prefixMonths}</span>}
      <CustomSelect
        placeholder={locale.emptyMonths || DEFAULT_LOCALE_EN.emptyMonths}
        optionsList={optionsList}
        grid={false}
        value={value}
        unit={{
          ...UNITS[3],
          // Allow translation of alternative labels when using "humanizeLabels"
          // Issue #3
          alt: locale.altMonths || DEFAULT_LOCALE_EN.altMonths,
        }}
        setValue={setValue}
        locale={locale}
        className={className}
        humanizeLabels={humanizeLabels}
        disabled={disabled}
        readOnly={readOnly}
        period={period}
        periodicityOnDoubleClick={periodicityOnDoubleClick}
        mode={mode}
        filterOption={filterOption}
      />
    </div>
  );
}
