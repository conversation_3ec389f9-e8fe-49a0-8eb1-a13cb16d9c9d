import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ActionIcon, Box, Flex, Tooltip } from '@mantine/core';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { sortBy } from 'lodash';
import DragTable from '@components/dragTable/DragTable';
import { IconTrash, IconSearch, IconEdit, IconEye, IconPlus } from '@tabler/icons-react';
import { Column, OnDragHandler } from '@components/dragTable/Types';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { KanbanButton, KanbanIconButton, KanbanInput, KanbanSwitch } from 'kanban-design-system';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { TABLE_INPUT_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { BaseModifyAlertConfig } from '@core/schema/ModifyAlertConfig';
import { ModifyAlertConfigApi } from '@api/ModifyAlertConfigApi';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { DEFAULT_PRIORITY } from '../priorityConfig/Constants';
import { DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST, FIELD_LABEL_MAP, ModifyAlertConfigAction } from './Constants';
import { createSearchParams, useNavigate } from 'react-router-dom';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import TruncatedText from './TruncatedText';
import { PreviewCondition } from '@components/queryBuilder/PreviewCondition';
import { CollectEmailOperatorEnum } from '@common/constants/CollectEmailConfigConstant';
import { RuleGroupType } from 'react-querybuilder';
import { QueryBuilderField } from '@components/queryBuilder';
import { baseFields } from '../collectEmail/Fields';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';

const ActionColumn = ({
  handleChangeURL,
  modifyAlertConfig,
  refetch,
}: {
  modifyAlertConfig: BaseModifyAlertConfig;
  refetch: () => void;
  handleChangeURL: (param: number, value: string | null) => void;
}) => {
  const { mutate: deleteMutate } = useMutate(ModifyAlertConfigApi.delete, {
    successNotification: 'Delete Alert config success',
    errorNotification: 'Delete Modify Alert config failed!',
    confirm: getDefaultDeleteConfirmMessage(),
    onSuccess: () => refetch(),
  });
  const { mutate: updateActiveMutate } = useMutate(ModifyAlertConfigApi.updateActive, {
    successNotification: `${modifyAlertConfig.active ? 'Inactive' : 'Active'} Modify Alert config success`,
    errorNotification: `${modifyAlertConfig.active ? 'Inactive' : 'Active'} Modify Alert config failed!`,
    onSuccess: () => refetch(),
  });

  return (
    <Flex gap='xs' align='center'>
      <GuardComponent requirePermissions={[AclPermission.modifyAlertConfigEdit, AclPermission.modifyAlertConfigView]}>
        <Tooltip label={modifyAlertConfig.active ? 'Active' : 'Inactive'}>
          <KanbanSwitch
            checked={modifyAlertConfig.active || false}
            onChange={
              isAnyPermissions([AclPermission.modifyAlertConfigEdit])
                ? (event) => updateActiveMutate({ id: modifyAlertConfig.id, active: event.target.checked })
                : undefined
            }
          />
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.modifyAlertConfigView]}>
        <Tooltip label='View Config'>
          <KanbanIconButton variant='transparent' size='sm' onClick={() => handleChangeURL(modifyAlertConfig.id, ModifyAlertConfigAction.VIEW)}>
            <IconEye />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.modifyAlertConfigEdit]}>
        <Tooltip label='Edit Config'>
          <KanbanIconButton variant='transparent' size='sm' onClick={() => handleChangeURL(modifyAlertConfig.id, ModifyAlertConfigAction.UPDATE)}>
            <IconEdit />
          </KanbanIconButton>
        </Tooltip>
      </GuardComponent>
      <GuardComponent requirePermissions={[AclPermission.modifyAlertConfigDelete]}>
        <ActionIcon
          variant='transparent'
          color='red'
          onClick={() =>
            deleteMutate(modifyAlertConfig.id, {
              confirm: getDefaultDeleteConfirmMessage(modifyAlertConfig.name),
            })
          }>
          <IconTrash width={20} height={24} />
        </ActionIcon>
      </GuardComponent>
    </Flex>
  );
};

const ModifyAlertConfigPage: React.FC = () => {
  const [searchInput, setSearchInput] = useState('');

  const { data, isFetching, refetch } = useFetch(ModifyAlertConfigApi.findAll());

  const modifyAlertConfigs = useMemo(() => sortBy(data?.data || [], 'position'), [data?.data]);

  const filteredConfigs = useMemo(() => {
    if (!searchInput) {
      return modifyAlertConfigs;
    }
    const keyword = searchInput.toLowerCase();
    return modifyAlertConfigs.filter(
      (cfg) =>
        cfg.name.toLowerCase().includes(keyword) ||
        cfg.description?.toLowerCase().includes(keyword) ||
        JSON.stringify(cfg.ruleGroup).toLowerCase().includes(keyword),
    );
  }, [modifyAlertConfigs, searchInput]);

  const [fields, setFields] = useState<QueryBuilderField[]>(baseFields);

  const { mutate: updatePositionMutate } = useMutate(ModifyAlertConfigApi.updatePosition, {
    successNotification: 'Update position success.',
    errorNotification: 'Update position failed!',
    onSuccess: () => refetch(),
    onError: () => refetch(),
  });

  const navigate = useNavigate();

  const handleChangeURL = useCallback(
    (param: number, value: string | null) => {
      navigate({
        pathname: `${ROUTE_PATH.MODIFY_ALERT_CONFIG}/${param}`,
        search: createSearchParams({ action: value || ModifyAlertConfigAction.CREATE }).toString(),
      });
    },
    [navigate],
  );

  const updatePositionHandler = useCallback<OnDragHandler<BaseModifyAlertConfig>>(
    (activeConfig, overConfig) => {
      if (activeConfig.position !== overConfig.position) {
        updatePositionMutate({
          modifyAlertConfigFromId: activeConfig.id,
          modifyAlertConfigToId: overConfig.id,
          fromPosition: activeConfig.position,
          toPosition: overConfig.position,
        });
      }
    },
    [updatePositionMutate],
  );

  // COLUMNS definition memoized
  const columns = useMemo<Column<BaseModifyAlertConfig>[]>(
    () => [
      { id: 'name', title: 'Name', render: 'name' },
      { id: 'description', title: 'Description', render: 'description' },
      {
        id: 'ruleGroup',
        title: 'Condition',
        width: '30%',
        sortable: false,
        render: (value) => {
          return (
            <PreviewCondition fields={fields} operators={Object.values(CollectEmailOperatorEnum)} value={(value as any).ruleGroup as RuleGroupType} />
          );
        },
      },
      {
        id: 'modifies',
        title: 'Modifies Field',
        render: (record) =>
          record.modifies?.map((modify, idx) => (
            <Flex
              direction='row'
              key={idx}
              align='center'
              gap='calc(var(--mantine-spacing-xs) / 2)'
              mb={idx === record.modifies.length - 1 ? 0 : 'sm'}
              style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
              <strong>{FIELD_LABEL_MAP[modify.fieldName as keyof typeof FIELD_LABEL_MAP] || modify.fieldName}:</strong>
              <TruncatedText text={modify.contentHtml || modify.fieldValue} isJson={modify.fieldName === 'content'} />
            </Flex>
          )),
      },
      {
        id: 'action',
        title: 'Action',
        render: (record) => <ActionColumn handleChangeURL={handleChangeURL} modifyAlertConfig={record} refetch={refetch} />,
      },
    ],
    [fields, handleChangeURL, refetch],
  );

  const { data: listCustomObject } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), {
    placeholderData: (prev) => prev,
  });

  useEffect(() => {
    if (listCustomObject?.data) {
      const customFields: QueryBuilderField[] = listCustomObject.data.content.map((obj) => ({
        name: obj.id.toString(),
        label: obj.name,
        operators: Object.values(QueryBuilderOperatorEnum),
      }));
      setFields((prev) => [...prev, ...customFields]);
    }
  }, [listCustomObject?.data]);

  return (
    <Box>
      <HeaderTitleComponent
        title='Modify Alert Config'
        rightSection={
          <Flex gap='sm'>
            <KanbanInput
              placeholder='Search'
              maxLength={TABLE_INPUT_MAX_LENGTH}
              leftSection={<IconSearch />}
              size='sm'
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
              }}
            />
            <GuardComponent requirePermissions={[AclPermission.modifyAlertConfigCreate]}>
              <KanbanButton leftSection={<IconPlus />} onClick={() => handleChangeURL(0, ModifyAlertConfigAction.CREATE)}>
                Create New
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />

      <DragTable
        disableDraggable={!isAnyPermissions([AclPermission.modifyAlertConfigEdit]) || !!searchInput || isFetching}
        columns={columns}
        data={filteredConfigs}
        onDragHandler={updatePositionHandler}
        showIndexColumn={false}
        dataKey={(record) => record.id}
        staticIds={[DEFAULT_PRIORITY]}
        position='bottom'
      />
    </Box>
  );
};

export default ModifyAlertConfigPage;
