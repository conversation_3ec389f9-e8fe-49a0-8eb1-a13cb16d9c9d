import { useDisclosure } from '@mantine/hooks';
import { ColumnType, KanbanIconButton, KanbanTabs, KanbanText, KanbanInput, KanbanCheckbox, KanbanButton } from 'kanban-design-system';
import React, { useEffect, useMemo, useState } from 'react';
import { ActionIcon, Alert, Flex, Pill, Tooltip } from '@mantine/core';
import { IconAlertCircle, IconEdit, IconTrash } from '@tabler/icons-react';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import Modal from '@components/Modal';
import useFetch from '@core/hooks/useFetch';
import { UserApi } from '@api/UserApi';
import { Role } from '@core/schema/Role';
import { Permission } from '@core/schema/Permission';
import { groupBy } from 'lodash';
import { PERMISSION_MODULE_LABEL, PermissionModuleEnum } from '@common/constants/PermissionModule';
import { PERMISSION_ACTION_LABEL } from '@common/constants/PermissionAction';
import Table from '@components/table';
import AddRoleToUserModal from './AddRoleToUserModal';
import useMutate from '@core/hooks/useMutate';
import { RoleApi } from '@api/RoleApi';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { UserModel, UserModelSchema } from '@models/UserModel';
import { Controller, useForm, UseFormReturn, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { IconEye, IconEyeOff } from '@tabler/icons-react';
import { DEFAULT_USER_DETAIL_VALUES } from './Constanst';

interface UserDetalModalProps {
  userId: number;
}

const COLUMNS: ColumnType<Role>[] = [
  {
    title: 'Role Name',
    name: 'name',
    customRender: (data) => {
      return (
        <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
          {data}
        </KanbanText>
      );
    },
  },
  {
    title: 'Description',
    name: 'description',
    customRender: (data, record) => {
      return (
        <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
          {record.description}
        </KanbanText>
      );
    },
  },
  {
    title: 'Permissions',
    name: 'permissions',
    customRender: (permissions: Permission[]) => {
      const groupedByModule = groupBy(permissions, 'module');
      return (
        <Pill.Group>
          {Object.entries(groupedByModule).map(([module, actions], index) =>
            (module as PermissionModuleEnum) !== PermissionModuleEnum.UNKNOWN ? (
              <Pill key={index}>
                {PERMISSION_MODULE_LABEL[module as PermissionModuleEnum]}
                {module !== PermissionModuleEnum.RUN_EXECUTION && `(${actions.map((action) => PERMISSION_ACTION_LABEL[action.action]).join(', ')})`}
              </Pill>
            ) : (
              <></>
            ),
          )}
        </Pill.Group>
      );
    },
  },
];

const UserPassword = ({ form }: { form: UseFormReturn<UserModel> }) => {
  const isUserLocal = useWatch({ control: form.control, name: 'isUserLocal' });
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const togglePasswordVisibility = () => setIsPasswordVisible(!isPasswordVisible);

  return (
    <>
      <Controller
        control={form.control}
        name='isUserLocal'
        render={({ field: { onChange, value } }) => (
          <KanbanCheckbox
            flex={1}
            label='Enable authentication local'
            checked={value}
            onChange={(event) => {
              onChange(event);
              form.setValue('password', '', { shouldValidate: true });
            }}
          />
        )}
      />

      <Controller
        control={form.control}
        name='password'
        render={({ field: { onChange, value }, fieldState }) => (
          <KanbanInput
            flex={2}
            required
            label='Password'
            disabled={!isUserLocal}
            type={isPasswordVisible ? 'text' : 'password'}
            value={value || ''}
            onChange={(event) => {
              onChange(event);
            }}
            error={fieldState.error?.message}
            rightSection={
              <KanbanIconButton onClick={togglePasswordVisibility} variant='transparent'>
                {isPasswordVisible ? <IconEye /> : <IconEyeOff />}
              </KanbanIconButton>
            }
          />
        )}
      />
    </>
  );
};

export const UserDetailModal = ({ userId }: UserDetalModalProps) => {
  const [openedModalUserDetails, { close: closeModalUserDetails, open: openModalUserDetails }] = useDisclosure(false);
  const { data: userDetail, refetch: fetchUserDetail } = useFetch(UserApi.findWithRolesById(userId), {
    enabled: openedModalUserDetails,
  });
  const [activeTab, setActiveTab] = useState<string>('general');
  const { mutate: deleteRoleFromUserMutate } = useMutate(RoleApi.deleteRoleFromUser, {
    successNotification: () => {
      return {
        title: 'Delete',
        message: `Delete role from user Success`,
      };
    },
    onSuccess: () => {
      fetchUserDetail();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });
  const [searchValue, setSearchValue] = useState('');

  const { mutate: updateUser } = useMutate(UserApi.save, {
    successNotification: (res) => {
      resetValues;
      return {
        title: 'Update User',
        message: `Update user ${res.data?.userName} Success`,
      };
    },
    onSuccess: () => {
      fetchUserDetail();
      resetValues();
    },
  });

  const form = useForm<UserModel>({
    defaultValues: DEFAULT_USER_DETAIL_VALUES,
    resolver: zodResolver(UserModelSchema),
    mode: 'onChange',
  });

  const tableData = useMemo<Role[]>(
    () =>
      userDetail?.data?.roles
        ?.filter(
          (role) =>
            role.name.toLowerCase().includes(searchValue?.toLowerCase()) || role.description?.toLowerCase()?.includes(searchValue?.toLowerCase()),
        )
        ?.sort((a, b) => a.name.localeCompare(b.name)) || [],
    [searchValue, userDetail?.data?.roles],
  );

  const resetValues = () => {
    form.reset();
    closeModalUserDetails();
  };

  useEffect(() => {
    if (userDetail?.data !== undefined && userDetail?.data) {
      form.reset({
        userName: userDetail?.data.userName ?? '',
        roleIds: userDetail?.data.roles?.map((role) => role.id) || [],
        id: userDetail.data.id ?? undefined,
        password: undefined,
        isUserLocal: userDetail.data.isUserLocal,
      });
    }
  }, [form, userDetail?.data]);

  return (
    <>
      <Tooltip label='Edit User'>
        <KanbanIconButton
          variant='transparent'
          size={'sm'}
          onClick={() => {
            openModalUserDetails();
          }}>
          <IconEdit />
        </KanbanIconButton>
      </Tooltip>
      <Modal
        size={'xl'}
        opened={openedModalUserDetails}
        onClose={resetValues}
        actions={
          userDetail?.data && (
            <GuardComponent requirePermissions={[AclPermission.userManageEdit]}>
              {activeTab === 'roles' ? (
                <AddRoleToUserModal userDetail={userDetail.data} />
              ) : (
                <KanbanButton
                  disabled={!form.formState.isValid}
                  onClick={() => {
                    if (form.formState.isValid) {
                      updateUser(UserModelSchema.parse(form.getValues()));
                    }
                  }}>
                  Save
                </KanbanButton>
              )}
            </GuardComponent>
          )
        }>
        <KanbanTabs
          configs={{
            defaultValue: activeTab,
            onChange: (val) => setActiveTab(val || 'general'),
          }}
          tabs={{
            general: {
              content: (
                <>
                  <Flex gap='md' justify='space-between'>
                    <KanbanInput name='userName' disabled label='User Name' value={userDetail?.data?.userName} flex={1} />
                    <KanbanInput name='email' disabled label='Email' value={userDetail?.data?.email} flex={2} />
                  </Flex>
                  <Flex gap='md' align-items='bottom'>
                    <UserPassword form={form} />
                  </Flex>
                </>
              ),
              title: 'General Info',
            },
            roles: {
              content: (
                <>
                  {userDetail?.data?.isAdmin && <Alert variant='light' color='blue' title='Current user is super admin' icon={<IconAlertCircle />} />}
                  <Flex justify='flex-end'>
                    <KanbanInput
                      maxLength={100}
                      placeholder='Search...'
                      value={searchValue}
                      onChange={(event) => setSearchValue(event.target.value)}
                      w={'30%'}
                    />
                  </Flex>
                  <Table
                    searchable={{
                      enable: true,
                    }}
                    sortable={{
                      enable: true,
                    }}
                    showNumericalOrderColumn
                    columns={COLUMNS}
                    data={tableData}
                    selectableRows={{ enable: false }}
                    showTopBar={false}
                    actions={{
                      customAction: (data) => {
                        return (
                          <ActionIcon
                            variant='transparent'
                            color='red'
                            onClick={() =>
                              deleteRoleFromUserMutate(
                                { roleId: data.id, userId: userDetail?.data?.id || 0 },
                                {
                                  confirm: getDefaultDeleteConfirmMessage(data.name),
                                },
                              )
                            }>
                            <IconTrash width={20} height={24} />
                          </ActionIcon>
                        );
                      },
                    }}
                  />
                </>
              ),

              title: 'Roles',
            },
          }}></KanbanTabs>
      </Modal>
    </>
  );
};
