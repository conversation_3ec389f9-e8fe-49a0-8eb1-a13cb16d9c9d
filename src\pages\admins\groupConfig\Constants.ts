import { CustomObjectSearchRequest } from '@api/Type';
import { AlertGroupConfigTypeEnum, AlertGroupOutputEnum } from '@common/constants/AlertGroupConfigConstants';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { QueryBuilderCombinatorEnum } from '@components/queryBuilder/QueryBuilderCombinatorEnum';
import { AlertGroupConfigModel } from '@models/AlertGroupConfigModel';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { QueryRuleGroupType, QueryRuleType } from '@core/schema/RuleGroupCondition';
import { ApplicationPaginationRequest } from '@models/ApplicationModel';
import { ServicePaginationRequest } from '@models/ServiceModel';
import { SortType } from '@common/constants/SortType';

export enum AlertGroupConfigAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  VIEW = 'VIEW',
}

export const DEFAULT_RULE: QueryRuleType = { field: 'content', operator: QueryBuilderOperatorEnum.CONTAINS, value: '' };

export const DEFAULT_GROUP_CONDITION: QueryRuleGroupType = {
  combinator: QueryBuilderCombinatorEnum.AND,
  rules: [DEFAULT_RULE],
};

export const DEFAULT_FORM_VALUE: AlertGroupConfigModel = {
  name: '',
  alertOutput: AlertGroupOutputEnum.LASTED_ALERT,
  type: AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE,
  customObjectIds: [],
  ruleGroups: [DEFAULT_GROUP_CONDITION],
  applications: [],
  services: [],
  description: '',
  customApplicationId: '',
  customRecipient: '',
  customContent: '',
  customPriorityConfigId: '',
  customServiceId: '',
};

export const DEFAULT_APPLICATION_PAGINATION_REQUEST: ApplicationPaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  name: '',
  withDeleted: false,
};

export const DEFAULT_SERVICE_PAGINATION_REQUEST: ServicePaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  withDeleted: false,
};

export const DEFAULT_GROUP_CONDTION_NAME = 'Group Condition';

export const DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST: CustomObjectSearchRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  size: 10000,
  withDeleted: false,
  name: '',
};
