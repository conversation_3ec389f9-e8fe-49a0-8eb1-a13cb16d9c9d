import { BaseURL } from '@common/constants/BaseUrl';
import { AlertPriorityConfigSchema, createResponseSchema } from '@core/schema';
import { z } from 'zod';
import { AlertGroupConfigRequest, AlertGroupConfigUpdatePositionRequest } from './Type';
import { AlertGroupConfigSchema } from '@core/schema/AlertGroupConfig';
import { createRequest } from './Utils';

export class AlertGroupConfigApi {
  static findAll(searchParam?: { withDeleted?: boolean; search?: string }) {
    return createRequest({
      url: BaseURL.groupConfig,
      method: 'GET',
      schema: createResponseSchema(z.array(AlertGroupConfigSchema)),
      params: searchParam,
    });
  }
  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.groupConfig}/:id`,
      method: 'GET',
      schema: createResponseSchema(AlertGroupConfigSchema),
      pathVariable: {
        id,
      },
    });
  }
  static save(data: AlertGroupConfigRequest) {
    return createRequest({
      url: BaseURL.groupConfig,
      method: 'POST',
      schema: createResponseSchema(AlertGroupConfigSchema),
      data,
    });
  }
  static delete(id: number) {
    return createRequest({
      url: `${BaseURL.groupConfig}/:id`,
      method: 'DELETE',
      schema: createResponseSchema(z.string()),
      pathVariable: {
        id,
      },
    });
  }
  static updatePosition(requestBody: AlertGroupConfigUpdatePositionRequest) {
    return createRequest({
      url: `${BaseURL.groupConfig}/position`,
      method: 'PUT',
      schema: createResponseSchema(z.array(AlertGroupConfigSchema)),
      data: requestBody,
    });
  }
  static updateActive({ active, id }: { id: number; active: boolean }) {
    return createRequest({
      url: `${BaseURL.groupConfig}/:id`,
      method: 'PUT',
      schema: createResponseSchema(AlertPriorityConfigSchema),
      pathVariable: {
        id,
      },
      params: {
        active,
      },
    });
  }
}
