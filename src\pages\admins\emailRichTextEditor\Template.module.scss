.controlsGroup {
  background-color: var(--mantine-color-violet-1);
}

.mantineButton {
  background-color: white;
  border: 1px solid #ccc;
  padding: 0.3rem 0.4rem;
  height: calc(1.625rem * var(--mantine-scale));
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: calc(var(--mantine-spacing-xs) / 2);
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.mantineButton:hover {
  border-color: #888;
}

.mantineButton svg {
  width: 1rem;
  height: 1rem;
  stroke: currentColor;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 1.5;
}

.mantineButton:focus {
  outline: 2px solid var(--mantine-color-primary-2);
  outline-offset: calc(var(--mantine-spacing-xs) / 2);
}