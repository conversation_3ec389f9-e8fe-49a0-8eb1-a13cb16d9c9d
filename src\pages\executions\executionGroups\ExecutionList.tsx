import { ExecutionApi } from '@api/ExecutionApi';
import useFetch from '@core/hooks/useFetch';
import { ExecutionGroup } from '@core/schema/ExecutionGroup';
import { Box, Center, Code, Flex, Loader, Text, Tooltip } from '@mantine/core';
import React, { useCallback } from 'react';
import classes from '../ExecutionGroups.module.scss';
import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { IconCode, IconDatabase } from '@tabler/icons-react';
import { useSearchParams } from 'react-router-dom';
import { EXECUTION_ID_SEARCH_PARAM_KEY } from '../Constants';

interface Props {
  executionGroup: ExecutionGroup;
  isSelected: boolean;
}

const ExecutionList = ({ executionGroup, isSelected }: Props) => {
  const { data: executionData, isLoading } = useFetch(ExecutionApi.findAllWithPermissionByExecutionGroupId({ executionGroupId: executionGroup.id }), {
    enabled: isSelected,
    showLoading: false,
  });
  const [searchParams, setSearchParams] = useSearchParams({ executionId: '' });
  const onExecutionSelect = useCallback(
    (executionId: string) => {
      setSearchParams({ [EXECUTION_ID_SEARCH_PARAM_KEY]: executionId });
    },
    [setSearchParams],
  );
  return (
    <Box className={classes.executionList}>
      {isLoading && (
        <Flex align='center' justify='center'>
          <Loader color='var(--mantine-color-gray-5)' type='dots' />
        </Flex>
      )}
      {!isLoading && executionData?.data?.length === 0 && (
        <Center p='xs' c='white'>
          <Code>No execution</Code>
        </Center>
      )}
      {executionData?.data?.map((execution) => {
        const isSelectedExecution = searchParams.get(EXECUTION_ID_SEARCH_PARAM_KEY) === execution.id;
        return (
          <Flex
            key={execution.id}
            gap='var(--mantine-spacing-xs)'
            align='center'
            justify='flex-start'
            className={classes.executionItem}
            onClick={() => onExecutionSelect(execution.id)}
            style={{ backgroundColor: isSelectedExecution ? 'var(--mantine-color-gray-3)' : undefined }}>
            <Box w={20} h={20}>
              {ExecutionTypeEnum.SQL === execution.type ? (
                <IconDatabase size={20} color='var(--mantine-color-blue-4)' />
              ) : (
                <IconCode size={20} color='var(--mantine-color-green-5)' />
              )}
            </Box>
            <Tooltip label={execution.name} multiline withArrow transitionProps={{ duration: 200 }}>
              <Text truncate size='15px'>
                {execution.name}
              </Text>
            </Tooltip>
          </Flex>
        );
      })}
    </Box>
  );
};

export default ExecutionList;
