export enum ConditionOperatorEnum {
  GREATER_THAN = 'GREATER_THAN',
  GREATER_THAN_OR_EQUAL = 'GREATER_THAN_OR_EQUAL',
  EQUAL = 'EQUAL',
  LESS_THAN = 'LESS_THAN',
  LESS_THAN_OR_EQUAL = 'LESS_THAN_OR_EQUAL',
}

export const optionConditionOperators = [
  { value: ConditionOperatorEnum.GREATER_THAN, label: 'Greater than' },
  { value: ConditionOperatorEnum.GREATER_THAN_OR_EQUAL, label: 'Greater than or equal' },
  { value: ConditionOperatorEnum.EQUAL, label: 'Equal' },
  { value: ConditionOperatorEnum.LESS_THAN, label: 'Less than' },
  { value: ConditionOperatorEnum.LESS_THAN_OR_EQUAL, label: 'Less than or equal' },
];
