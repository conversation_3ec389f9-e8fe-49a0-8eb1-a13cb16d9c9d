import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { ComboboxItem } from '@mantine/core';

export enum CollectEmailOperatorEnum {
  IS = QueryBuilderOperatorEnum.IS,
  IS_NOT = QueryBuilderOperatorEnum.IS_NOT,
  IS_ONE_OF = QueryBuilderOperatorEnum.IS_ONE_OF,
  IS_NOT_ONE_OF = QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
  CONTAINS = QueryBuilderOperatorEnum.CONTAINS,
  DOES_NOT_CONTAIN = QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
}

export enum CollectEmailContentTypeEnum {
  SAME_BODY = 'SAME_BODY',
  CUSTOM_CONTENT = 'CUSTOM_CONTENT',
}
export enum CollectEmailConfigAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  COPY = 'COPY',
  VIEW = 'VIEW',
}
export const optionIntervalTime: ComboboxItem[] = [
  {
    label: '15 seconds',
    value: '15',
  },
  {
    label: '30 seconds',
    value: '30',
  },
  {
    label: '60 seconds',
    value: '60',
  },
  {
    label: '120 seconds',
    value: '120',
  },
  {
    label: '300 seconds',
    value: '300',
  },
  {
    label: '600 seconds',
    value: '600',
  },
  {
    label: '1800 seconds',
    value: '1800',
  },
  {
    label: '2400 seconds',
    value: '2400',
  },
  {
    label: '3000 seconds',
    value: '3000',
  },
  {
    label: '3600 seconds',
    value: '3600',
  },
];
export const optionContentTypes: ComboboxItem[] = [
  {
    label: 'Same as text body',
    value: CollectEmailContentTypeEnum.SAME_BODY,
  },
  {
    label: 'Custom Content',
    value: CollectEmailContentTypeEnum.CUSTOM_CONTENT,
  },
];
