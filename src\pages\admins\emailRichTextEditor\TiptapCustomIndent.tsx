import { Extension, Command, RawCommands } from '@tiptap/core';
import { Transaction, TextSelection, AllSelection } from '@tiptap/pm/state';

export const Indent = Extension.create({
  name: 'indent',

  defaultOptions: {
    types: ['listItem', 'paragraph', 'heading'],
    minLevel: 0,
    maxLevel: 4,
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          indent: {
            renderHTML: (attributes) => {
              return attributes.indent > this.options.minLevel ? { 'data-indent': attributes.indent } : null;
            },
            parseHTML: (element) => {
              const indentLevel = Number(element.getAttribute('data-indent'));
              return indentLevel && indentLevel > this.options.minLevel ? indentLevel : null;
            },
          },
        },
      },
    ];
  },

  addCommands() {
    const setNodeIndentMarkup = (tr: Transaction, pos: number, delta: number) => {
      const node = tr.doc.nodeAt(pos);

      if (node) {
        const nextLevel = (node.attrs.indent || 0) + delta;
        const { maxLevel, minLevel } = this.options;
        const indent = nextLevel < minLevel ? minLevel : nextLevel > maxLevel ? maxLevel : nextLevel;

        if (indent !== node.attrs.indent) {
          const { indent: _, ...currentAttrs } = node.attrs;
          const nodeAttrs = indent > minLevel ? { ...currentAttrs, indent } : currentAttrs;
          return tr.setNodeMarkup(pos, node.type, nodeAttrs, node.marks);
        }
      }
      return tr;
    };

    const updateIndentLevel = (tr: Transaction, delta: number) => {
      const { doc, selection } = tr;

      if (doc && selection && (selection instanceof TextSelection || selection instanceof AllSelection)) {
        const { from, to } = selection;
        doc.nodesBetween(from, to, (node, pos) => {
          if (this.options.types.includes(node.type.name)) {
            tr = setNodeIndentMarkup(tr, pos, delta);
            return false;
          }

          return true;
        });
      }

      return tr;
    };

    return {
      // Define indent command
      indent:
        (): Command =>
        ({ dispatch, editor, state, tr }) => {
          const { selection } = state;
          tr = tr.setSelection(selection);
          tr = updateIndentLevel(tr, 4);

          if (tr.docChanged) {
            if (dispatch) {
              dispatch(tr);
            }
            return true;
          }

          editor.chain().focus().run();
          return false;
        },

      // Define outdent command
      outdent:
        (): Command =>
        ({ dispatch, editor, state, tr }) => {
          const { selection } = state;
          tr = tr.setSelection(selection);
          tr = updateIndentLevel(tr, 4);

          if (tr.docChanged) {
            if (dispatch) {
              dispatch(tr);
            }
            return true;
          }

          editor.chain().focus().run();
          return false;
        },
    } as Partial<RawCommands>;
  },
});
