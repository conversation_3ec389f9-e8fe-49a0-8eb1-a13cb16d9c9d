/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */

import React from 'react';

import CustomSelect from '../components/CustomSelect';
import { UNITS } from '../ConstantsSelect';
import { DEFAULT_LOCALE_EN } from '../LocaleSelect';
import { HoursProps } from '../TypesSelect';

export default function Hours(props: HoursProps) {
  const { className, clockFormat, disabled, filterOption, leadingZero, locale, mode, period, periodicityOnDoubleClick, readOnly, setValue, value } =
    props;

  return (
    <div>
      {locale.prefixHours !== '' && <span>{locale.prefixHours || DEFAULT_LOCALE_EN.prefixHours}</span>}

      <CustomSelect
        placeholder={locale.emptyHours || DEFAULT_LOCALE_EN.emptyHours}
        value={value}
        unit={UNITS[1]}
        setValue={setValue}
        locale={locale}
        className={className}
        disabled={disabled}
        readOnly={readOnly}
        leadingZero={leadingZero}
        clockFormat={clockFormat}
        period={period}
        periodicityOnDoubleClick={periodicityOnDoubleClick}
        mode={mode}
        filterOption={filterOption}
      />
    </div>
  );
}
