/**
 * This component is cloned from library 'react-js-cron' and may have modifications specific to this project.
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { MultiSelect } from '@mantine/core';
import { formatValue } from '../ConverterSelect';
import { CustomSelectProps } from '../TypesSelect';

export default function CustomSelect(props: CustomSelectProps) {
  const {
    clockFormat,
    disabled,
    filterOption = () => true,
    humanizeLabels,
    leadingZero,
    mode,
    optionsList,
    setValue,
    unit,
    value,
    ...otherProps
  } = props;

  const [selectedValues, setSelectedValues] = useState<string[]>(value ? value.map(String) : []);

  useEffect(() => {
    if (value) {
      setSelectedValues(value.map(String));
    }
  }, [value]);

  const options = useMemo(() => {
    if (optionsList) {
      return optionsList
        .map((option, index) => {
          const number = unit.min === 0 ? index : index + 1;
          return {
            value: number.toString(),
            label: option,
          };
        })
        .filter(filterOption);
    }

    return [...Array(unit.total)].map((_, index) => {
      const number = unit.min === 0 ? index : index + 1;
      return {
        value: number.toString(),
        label: formatValue(number, unit, humanizeLabels, leadingZero, clockFormat),
      };
    });
  }, [optionsList, leadingZero, humanizeLabels, clockFormat, unit, filterOption]);

  const handleChange = useCallback(
    (newValue: string[]) => {
      if (mode === 'single') {
        const numericValue = newValue.length > 0 ? [Number(newValue[0])] : [];
        setValue(numericValue);
        setSelectedValues(newValue);
      } else {
        const numericValues = newValue.map(Number);
        setValue(numericValues);
        setSelectedValues(newValue);
      }
    },
    [mode, setValue],
  );

  const handleClear = useCallback(() => {
    setValue([]);
    setSelectedValues([]);
  }, [setValue]);

  return (
    <MultiSelect
      multiple={mode !== 'single'}
      data={options}
      defaultValue={selectedValues}
      value={selectedValues}
      onChange={(value) => handleChange(value || [])}
      placeholder='Select values'
      disabled={disabled}
      onClear={handleClear}
      {...otherProps}
    />
  );
}
