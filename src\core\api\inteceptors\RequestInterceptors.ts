import type { InternalAxiosRequestConfig } from 'axios';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { v4 as uuid } from 'uuid';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
export const requestIdInterceptor = (config: InternalAxiosRequestConfig<any>) => {
  config.headers = config.headers ?? {};
  config.headers.set('Request-id', uuid());
  return config;
};
const excludedUrls = [ROUTE_PATH.LOGIN];
export const authenticationInterceptor = (config: InternalAxiosRequestConfig<any>) => {
  config.headers = config.headers ?? {};
  if (!excludedUrls.some((url) => config.url?.includes(url))) {
    const token = localStorage.getItem(LocalStorageKey.MONITOR_ACCESS_TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }

  return config;
};
