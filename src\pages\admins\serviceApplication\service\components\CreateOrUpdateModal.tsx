import React, { useEffect } from 'react';
import { KanbanButton } from 'kanban-design-system';
import { ServiceApi } from '@api/ServiceApi';
import useFetch from '@core/hooks/useFetch';
import { Service } from '@core/schema';
import { useForm, zodResolver } from '@mantine/form';
import { KanbanInput } from 'kanban-design-system';
import useMutate from '@core/hooks/useMutate';
import { useDisclosure } from '@mantine/hooks';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { ServiceModelSchema } from '@models/ServiceModel';
import Modal from '@components/Modal';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

type CreateOrUpdateModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  service?: Service;
};

const CHARACTER_NAME_SERVICE_MAX_LENGTH: number = 100;
const CHARACTER_DESCRIPTION_SERVICE_MAX_LENGTH: number = 300;
const DEFAULT_FORM_SERVICE = { id: '', name: '', description: '' };

const CreateOrUpdateModal: React.FC<CreateOrUpdateModalProps> = ({ onClose, opened, refetchList, service }) => {
  const isUpdateMode = !!service;
  const { data: serviceDetail } = useFetch(ServiceApi.findById(service?.id || ''), {
    enabled: isUpdateMode && opened,
  });
  const { data: dependencies } = useFetch(ServiceApi.findAllDependenciesById(service?.id || ''), {
    enabled: isUpdateMode && opened,
  });
  const dependencyConfig: DependencyItem[] = [
    {
      dependencyEntity: 'applications',
      dependencies: dependencies?.data?.applications ?? [],
    },
    {
      dependencyEntity: 'webhooks',
      dependencies: dependencies?.data?.webHooks ?? [],
    },
    {
      dependencyEntity: 'collect email configs',
      dependencies: dependencies?.data?.collectEmailConfigs ?? [],
    },
    {
      dependencyEntity: 'collect database configs',
      dependencies: dependencies?.data?.databaseCollects ?? [],
    },
    {
      dependencyEntity: 'alert group configs',
      dependencies: dependencies?.data?.alertGroupConfigs ?? [],
    },
    {
      dependencyEntity: 'maintenance time configs',
      dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
    },
    {
      dependencyEntity: 'modify alert configs',
      dependencies: dependencies?.data?.modifyAlertConfigs ?? [],
    },
    {
      dependencyEntity: 'database threshold configs',
      dependencies: dependencies?.data?.databaseThresholdConfigs ?? [],
    },
  ];
  const { mutate: saveMutate } = useMutate(ServiceApi.save, {
    successNotification: { enable: true, message: service ? `Update Service Successfully` : 'Create Service Successfully' },
    onSuccess: () => {
      setValues(DEFAULT_FORM_SERVICE);
      refetchList();
      onClose();
      if (openedModalConfirmUpdate) {
        closeModalConfirmUpdate();
      }
    },
    onError: () => {
      if (openedModalConfirmUpdate) {
        closeModalConfirmUpdate();
      }
    },
  });
  const [openedModalConfirmUpdate, { close: closeModalConfirmUpdate, open: openModalConfirmUpdate }] = useDisclosure(false);
  const { errors, getInputProps, isValid, setValues, values } = useForm({
    validateInputOnChange: true,
    initialValues: DEFAULT_FORM_SERVICE,
    validate: zodResolver(ServiceModelSchema),
  });
  const handleClickSaveButton = () => {
    if (isUpdateMode && isValid()) {
      openModalConfirmUpdate();
    } else if (isValid()) {
      saveMutate(ServiceModelSchema.parse(values));
    }
  };
  const isDisabledButtonSave = !isValid();
  useEffect(() => {
    if (!isUpdateMode) {
      setValues(DEFAULT_FORM_SERVICE);
      return;
    }
    if (serviceDetail?.data) {
      setValues({
        id: serviceDetail?.data?.id || '',
        name: serviceDetail?.data?.name || '',
        description: serviceDetail?.data?.description || '',
      });
    }
  }, [serviceDetail?.data, opened, setValues, service, isUpdateMode]);
  return (
    <>
      <Modal
        size={'xl'}
        opened={opened}
        onClose={() => {
          onClose();
          setValues(DEFAULT_FORM_SERVICE);
        }}
        title={service ? `Update Service ${service.name}` : 'Create Service'}
        actions={
          <KanbanButton onClick={handleClickSaveButton} disabled={isDisabledButtonSave}>
            Save
          </KanbanButton>
        }>
        <DependenciesWarningAlert mainEntity='Service' dependencyConfigs={dependencyConfig} isDeleted={false} />
        <form>
          <KanbanInput
            required
            label='Service Name'
            description={getMaxLengthMessage(CHARACTER_NAME_SERVICE_MAX_LENGTH)}
            value={values.name}
            {...getInputProps('name')}
            error={errors.name}
            maxLength={CHARACTER_NAME_SERVICE_MAX_LENGTH}
          />
          <KanbanInput
            label='Description'
            description={getMaxLengthMessage(CHARACTER_DESCRIPTION_SERVICE_MAX_LENGTH)}
            value={values.description}
            {...getInputProps('description')}
            error={errors.description}
            maxLength={CHARACTER_DESCRIPTION_SERVICE_MAX_LENGTH}
          />
        </form>
      </Modal>
      <Modal
        size='xl'
        opened={openedModalConfirmUpdate}
        onClose={closeModalConfirmUpdate}
        title={'Update Service'}
        actions={<KanbanButton onClick={() => saveMutate(ServiceModelSchema.parse(values))}>Confirm</KanbanButton>}>
        <DependenciesWarningAlert mainEntity='Service' dependencyConfigs={dependencyConfig} isDeleted={false} />
        Are you sure to update this item?
      </Modal>
    </>
  );
};

export default CreateOrUpdateModal;
