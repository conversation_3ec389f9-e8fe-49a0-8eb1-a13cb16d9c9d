import { KanbanButton } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { ColumnType, KanbanTable } from 'kanban-design-system';
import { Flex, Space } from '@mantine/core';
import React, { useMemo, useState } from 'react';
import useMutate from '@core/hooks/useMutate';
import { SuperiorsApi } from '@api/SuperiorsApi';

export const SqlExecutionPage = () => {
  const [query, setQuery] = useState('');
  const [password, setPassword] = useState('');
  const [countQuery, setCountQuery] = useState(0);
  const { data: currentResult, mutate: querySqlMutate } = useMutate(SuperiorsApi.querySql, {
    successNotification: { enable: false },
  });

  const columns: ColumnType<Record<string, any>>[] = useMemo(() => {
    if (!currentResult?.data || currentResult?.data?.isNonQuery || !currentResult?.data?.listColumns?.length) {
      return [];
    }
    const result: ColumnType<Record<string, any>>[] = currentResult?.data?.listColumns.map((x) => {
      return {
        name: x,
        title: x,
      };
    });
    return result;
  }, [currentResult?.data]);

  const listData: Record<string, any>[] = useMemo(() => {
    setCountQuery((prev) => prev + 1);
    if (!currentResult?.data || currentResult?.data?.isNonQuery || !currentResult?.data?.listDataMappings?.length) {
      return [];
    }

    const result: Record<string, any>[] = [];
    for (const item of currentResult.data.listDataMappings) {
      const currentItem: Record<string, any> = {};

      for (const currentRow of item.listSqlMappingColumnDatas) {
        currentItem[currentRow.column] = currentRow.value;
      }
      result.push(currentItem);
    }

    return result;
  }, [currentResult?.data]);

  const isValid = query.trim() && password.trim();

  const onSubmit = () => {
    if (!isValid) {
      return;
    }
    querySqlMutate({ sqlQuery: btoa(query.trim()), password: password.trim() });
  };

  return (
    <>
      <KanbanTextarea
        label='Sql query'
        placeholder='Type your query'
        value={query}
        autosize
        minRows={5}
        maxRows={10}
        onChange={(e) => {
          setQuery(e.target.value);
        }}
      />

      <KanbanInput
        label='Private password'
        type='password'
        value={password}
        onChange={(e) => {
          setPassword(e.target.value || '');
        }}
        inputContainer={(children) => {
          return (
            <Flex gap={'md'} align={'flex-end'}>
              <div
                style={{
                  flex: 1,
                }}>
                {children}
              </div>
              <KanbanButton
                disabled={!isValid}
                onClick={() => {
                  onSubmit();
                }}>
                Execute
              </KanbanButton>
            </Flex>
          );
        }}
      />

      <Space h={'lg'} />

      <KanbanTable key={countQuery} title='Result' columns={columns} data={listData} />
    </>
  );
};

export default SqlExecutionPage;
