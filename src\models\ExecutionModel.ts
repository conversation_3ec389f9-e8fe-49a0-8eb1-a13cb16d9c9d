import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { EXECUTION_SCRIPT_MAX_LENGTH, MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH } from '@common/constants/ValidationConstant';
import { trim } from 'lodash';
import { z, ZodIssueCode } from 'zod';

export const ExecutionModelSchema = z
  .object({
    id: z.string().optional(),
    name: z.string().trim().min(1).max(MAX_CHARACTER_NAME_LENGTH),
    description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
    type: z.nativeEnum(ExecutionTypeEnum),
    databaseConnectionId: z.number().optional(),
    executionGroupId: z.string().optional(),
    script: z.string().min(1).max(EXECUTION_SCRIPT_MAX_LENGTH),
  })
  .superRefine((data, ctx) => {
    if (ExecutionTypeEnum.SQL === data.type && !data.databaseConnectionId) {
      ctx.addIssue({
        message: 'Database connetion can not be empty',
        path: ['databaseConnectionId'],
        code: ZodIssueCode.custom,
      });
    }
    if (!trim(data.executionGroupId)) {
      ctx.addIssue({
        message: 'Execution Group can not be empty',
        path: ['executionGroupId'],
        code: ZodIssueCode.custom,
      });
    }
  });

export const ExecutionGroupModelSchema = z.object({
  id: z.string().optional(),
  name: z.string().trim().min(1).max(MAX_CHARACTER_NAME_LENGTH),
  description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
});

export type ExecutionModel = z.infer<typeof ExecutionModelSchema>;
export type ExecutionGroupModel = z.infer<typeof ExecutionGroupModelSchema>;
