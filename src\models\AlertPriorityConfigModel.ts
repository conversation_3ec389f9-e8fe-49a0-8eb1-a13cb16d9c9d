import {
  MAX_ALERT_PRIORITY_CONFIG_COLOR_LENGTH,
  MAX_ALERT_PRIORITY_CONFIG_NAME_LENGTH,
  MIN_STRING_LENGTH,
} from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const AlertPriorityConfigModelSchema = z.object({
  id: z.number().nullish(),
  name: z.string().trim().min(MIN_STRING_LENGTH).max(MAX_ALERT_PRIORITY_CONFIG_NAME_LENGTH),
  color: z.string().trim().max(MAX_ALERT_PRIORITY_CONFIG_COLOR_LENGTH),
});

export type AlertPriorityConfigModel = z.infer<typeof AlertPriorityConfigModelSchema>;
