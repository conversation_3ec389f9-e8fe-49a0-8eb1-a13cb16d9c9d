import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { PaginationRequest } from './Type';
import { createPageSchema, createResponseSchema } from '@core/schema';
import { ExportDataSchema } from '@core/schema/ExportData';

export class ExportDataApi {
  static download(id: string) {
    return createRequest<Blob>({
      url: `${BaseURL.exportData}/${id}`,
      method: 'GET',
      responseType: 'blob',
    });
  }
  static findAll(pagination: PaginationRequest) {
    return createRequest({
      url: BaseURL.exportData,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(ExportDataSchema)),
      params: pagination,
    });
  }
  static deleteById(id: string) {
    return createRequest<Blob>({
      url: `${BaseURL.exportData}/${id}`,
      method: 'DELETE',
    });
  }
}
