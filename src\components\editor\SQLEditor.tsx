import React, { useCallback } from 'react';
import { sql } from '@codemirror/lang-sql';
import { BaseEditorProps } from './Type';
import BaseEditor from './BaseEditor';
import { format } from 'sql-formatter';

const SQLEditor = (props: BaseEditorProps) => {
  const formatOnChange = useCallback(
    (value: string | undefined) => {
      if (!props.onChange) {
        return;
      }
      if (!value) {
        props.onChange(value);
        return;
      }
      try {
        props.onChange(
          format(value, {
            keywordCase: 'upper',
            language: 'plsql',
          }),
        );
      } catch (e: any) {
        props.onChange(value);
      }
    },
    [props],
  );
  return (
    <BaseEditor
      {...props}
      onChange={(value) => {
        props.onChange && props.onChange(value);
      }}
      extensions={[sql()]}
      onBlur={() => formatOnChange(props.value)}
    />
  );
};

export default SQLEditor;
