export const DATE_FORMAT = {
  FORMAT_DDMMYYYY: 'dd/MM/YYYY',
  FORMAT_YYYYMMDD: 'YYYYMMdd',
  FORMAT_DDMMYYYY_HHMMSS: 'DD/MM/YYYY HH:mm:ss',
  FORMAT_DD_MM_YYYY: 'dd-MM-YYYY',
  FORMAT_DD_MM_YYYY_HH_MM: 'dd/MM/YYYY HH:mm',
  FORMAT_YYYY_MM_DD_HH_MM: 'YYYY-MM-DD HH:mm',
  FORMAT_YYYY_MM_DD_HH_MM_SS: 'YYYY-MM-DD HH:mm:ss',
  DATE_FORMAT_PATTERN_OF_ATTRIBUTE: "EEE MMM dd YYYY HH:mm:ss 'GMT'Z (zzzz)",
  FORMAT_MM_DD_YYYY: ' MM/dd/YYYY',
  FORMAT_DD_MM_YYYY_HH_MM_A: 'DD/MM/YYYY hh:mm A',
} as const;

export type DateFormat = (typeof DATE_FORMAT)[keyof typeof DATE_FORMAT];
