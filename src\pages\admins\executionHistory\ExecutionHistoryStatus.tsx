import { ExecutionStatusEnum, ExecutionStatusInfo } from '@common/constants/ExecutionConstants';
import { Center, Text } from '@mantine/core';
import React from 'react';

const ExecutionHistoryStatus = ({ status }: { status: ExecutionStatusEnum }) => {
  const statusInfor = ExecutionStatusInfo[status];
  return (
    <Center
      py='calc(var(--mantine-spacing-xs) / 2)'
      px='var(--mantine-spacing-xs)'
      bg={`var(${statusInfor.bgColor})`}
      c={`var(${statusInfor.color})`}
      style={{ borderRadius: 'var(--mantine-radius-sm)' }}>
      <Text fw={500} fz={14}>
        {statusInfor.label}
      </Text>
    </Center>
  );
};

export default ExecutionHistoryStatus;
