import { BaseURL } from '@common/constants/BaseUrl';
import { createResponseSchema, NoteSchema } from '@core/schema';
import { z } from 'zod';
import { createRequest } from './Utils';

export class NoteApi {
  static findAllByAlertGroupId(alertGroupId: number) {
    return createRequest({
      url: BaseURL.note,
      method: 'GET',
      schema: createResponseSchema(z.array(NoteSchema)),
      params: {
        alertGroupId,
      },
    });
  }
  static saveAll(notes: { content: string; alertGroupId: number }[]) {
    return createRequest({
      url: BaseURL.note,
      method: 'POST',
      schema: createResponseSchema(z.array(NoteSchema)),
      data: {
        data: notes,
      },
    });
  }
}
