import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { Alert } from '@core/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Flex, InputLabel } from '@mantine/core';
import { TeamsSendMessageModel, TeamsSendMessageSchema } from '@models/TeamsModel';
import { KanbanButton, KanbanTagsInput, KanbanTextarea } from 'kanban-design-system';
import React, { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import classes from './TeamsSendMesssage.module.scss';
import { TeamsAlertConfigApi } from '@api/TeamsAlertConfigApi';
import useMutate from '@core/hooks/useMutate';
import Modal from '@components/Modal';
import { useDisclosure } from '@mantine/hooks';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { TEAMS_MESSAGE_CONTENT_MAX_LENGTH } from '@common/constants/ValidationConstant';

export type TeamsSendMessageProps = {
  alerts: Alert[];
};

/**
 * Processes a list of contacts in a special format and returns either a list of emails or group names.
 * Format: L1: email1, email2. L2: email3, email4. Group: group_name
 * Priority rules:
 * - If any contact has Group, only group names will be returned
 * - L1 is required
 * - All emails must have @mbbank.com.vn domain
 *
 * @param contacts List of contact strings to process
 * @returns List of unique emails or group names based on priority rules
 */
const processContacts = (contacts: string[]): string[] => {
  if (!contacts || contacts.length === 0) {
    return [];
  } // Store found groups

  const groups: string[] = []; // Store all valid email addresses
  const contactEmails: string[] = []; // Flag to indicate if any group was found
  let hasGroup = false; // Step 1: Analyze all elements in the contacts list

  for (const contactString of contacts) {
    // Skip empty strings
    if (!contactString || !contactString.trim()) {
      continue;
    }

    const normalizedContact = contactString.trim(); // Parse using regex patterns

    const primaryMatch = normalizedContact.match(/L1:\s*([\w\s@.,_-]+?)(?=\.\s*L2:|$|\.\s*Group:)/i);
    const secondaryMatch = normalizedContact.match(/L2:\s*([\w\s@.,_-]+?)(?=\.?\s*Group:|$)/i);
    const groupMatch = normalizedContact.match(/Group:\s*([\w\s@.,_-]+)$/i); // Check L1 (required)

    let hasPrimaryContact = false;
    let primaryContacts: string[] = [];

    if (groupMatch) {
      const groupsString = groupMatch[1].trim();
      if (groupsString) {
        // Mark that we found a group
        hasGroup = true; // Split groups by comma

        const extractedGroups = groupsString
          .split(',')
          .map((group) => group.trim())
          .filter((group) => group.length > 0); // Add to groups array

        groups.push(...extractedGroups);
      }
    } // Collect all primary contacts

    if (primaryMatch) {
      primaryContacts = primaryMatch[1]
        .split(',')
        .map((contact) => contact.trim())
        .filter((contact) => contact.endsWith('@mbbank.com.vn'));

      hasPrimaryContact = primaryContacts.length > 0;
    } // Skip this entry if no valid primary contact

    if (!hasPrimaryContact) {
      continue;
    } // Process groups if present

    contactEmails.push(...primaryContacts); // Collect all secondary contacts if present

    if (secondaryMatch) {
      const secondaryContacts = secondaryMatch[1]
        .split(',')
        .map((contact) => contact.trim())
        .filter((contact) => contact.endsWith('@mbbank.com.vn'));

      contactEmails.push(...secondaryContacts);
    }
  } // Step 2: Return results based on priority rules
  // If any group was found, return only unique groups

  if (hasGroup && groups.length > 0) {
    return [...new Set(groups)];
  } // If no groups were found, return all unique email addresses

  return [...new Set(contactEmails)];
};

const TeamsSendMessage = (props: TeamsSendMessageProps) => {
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const { alerts } = props;
  const [invalidEmails, setInvalidEmails] = useState<string[]>([]);
  const { mutate: sendMutate } = useMutate(TeamsAlertConfigApi.sendMessage, {
    successNotification: { enable: false },
    onSuccess: (data) => {
      if (data?.data) {
        handleWaiting(data?.data);
      }
    },
  });
  const {
    control,
    formState: { isValid },
    getValues,
    handleSubmit,
    reset,
    setValue,
    trigger,
  } = useForm<TeamsSendMessageModel>({
    resolver: zodResolver(TeamsSendMessageSchema),
    mode: 'all',
  }); //check show modal confirm

  const handleWaiting = useCallback(
    (teamsSendResult: TeamsSendMessageModel) => {
      if (!teamsSendResult.isSend && teamsSendResult?.invalidEmails?.length) {
        setInvalidEmails(teamsSendResult.invalidEmails);
        openModal();
      } else {
        NotificationSuccess({ message: 'Send message teams successfully' });
        reset();
      }
    },
    [openModal, reset],
  );

  useEffect(() => {
    if (alerts?.length) {
      const messages = alerts.map((item) => ` - ${item.content}`).join('\n');
      const contacts = alerts.map((item) => item.recipient).filter((recipient): recipient is string => recipient !== null);
      reset({
        message: messages,
        contacts: processContacts(contacts),
      });
    } else {
      reset({
        message: '',
        contacts: [],
      });
    }
    trigger();
  }, [alerts, reset, trigger]);

  const onSend = useCallback(
    (data: TeamsSendMessageModel) => {
      // Validation is handled by handleSubmit and zodResolver,
      // but we can keep this check for clarity or extra safety.
      if (isValid) {
        // The data passed to onSend by handleSubmit is already validated
        // according to the schema if validation is successful.
        // Calling parse again here is redundant unless you need to transform data differently.
        // For simplicity, we can just use the provided data directly.
        // const validatedData = TeamsSendMessageSchema.parse(data);
        sendMutate(data); // Use data directly from handleSubmit
      }
    },
    [isValid, sendMutate],
  );

  const handleResend = useCallback(() => {
    const contacts = getValues('contacts');
    const invalidEmailsSet = new Set(invalidEmails);
    const newContacts = contacts.filter((contact) => !invalidEmailsSet.has(contact));

    setValue('contacts', newContacts);
    handleSubmit(onSend)();
    closeModal();
  }, [closeModal, getValues, handleSubmit, invalidEmails, onSend, setValue]);

  return (
    <Box component='p' p={10}>
      <Controller
        name='message'
        control={control}
        render={({ field, fieldState: { error } }) => (
          <KanbanTextarea
            placeholder='Message'
            description={getMaxLengthMessage(TEAMS_MESSAGE_CONTENT_MAX_LENGTH)}
            rows={10}
            {...field}
            error={error?.message}
            maxLength={TEAMS_MESSAGE_CONTENT_MAX_LENGTH}
          />
        )}
      />
      <Controller
        name='contacts'
        control={control}
        render={({ field, fieldState: { error } }) => (
          <KanbanTagsInput
            leftSection={<InputLabel>To</InputLabel>}
            acceptValueOnBlur={true}
            allowDuplicates={false}
            scrollAreaProps={{
              type: 'auto',
              scrollbarSize: 4,
              offsetScrollbars: true,
            }}
            classNames={{
              wrapper: classes.tagsInputWrapper,
              input: classes.tagsInput,
            }}
            {...field}
            error={error?.message}
          />
        )}
      />
      <Flex>
        <KanbanButton variant='outline' mr={0} fullWidth disabled={!isValid} onClick={handleSubmit(onSend)}>
          Send Teams
        </KanbanButton>
      </Flex>
      <Modal
        size='xl'
        opened={openedModal}
        onClose={() => {
          closeModal();
        }}
        title={'Confirm send teams'}
        actions={<KanbanButton onClick={handleResend}>Confirm</KanbanButton>}>
        Have some contact fail: list email fail: {invalidEmails.join(',')}. Do you want to other contact?
      </Modal>
    </Box>
  );
};
export default TeamsSendMessage;
