import styles from './Partner.module.scss';
import React, { useState } from 'react';
import { Checkbox, Popover, Box, Divider } from '@mantine/core';
import { EmailPartner } from '@core/schema/EmailPartner';

type EmailPartnerSelectionProps = {
  listEmailPartner: EmailPartner[];
  selectedPartners: Record<number, Record<string, boolean>>;
  handlePartnerCheck: (partnerId: number) => void;
  handleAddressCheck: (partnerId: number, address: string) => void;
  allAddressesChecked: (partnerId: number, addresses: string[]) => boolean;
  selectedCount: (partnerId: number, addresses: string[]) => number;
};

const EmailPartnerSelection: React.FC<EmailPartnerSelectionProps> = ({
  allAddressesChecked,
  handleAddressCheck,
  handlePartnerCheck,
  listEmailPartner,
  selectedCount,
  selectedPartners,
}) => {
  const [partnerSelected, setPartnerSelected] = useState<number | null>();

  return (
    <Box my='sm' size='sm'>
      {listEmailPartner?.map((partner) => (
        <Popover key={partner.id} width={200} position='right-start' withArrow shadow='md' opened={partner.id === partnerSelected}>
          <Popover.Target>
            <Box mb='xs' onMouseEnter={() => setPartnerSelected(partner.id)} onMouseLeave={() => setPartnerSelected(null)}>
              <Checkbox
                checked={allAddressesChecked(partner.id, partner.addresses)}
                indeterminate={
                  selectedCount(partner.id, partner.addresses) > 0 && selectedCount(partner.id, partner.addresses) < partner.addresses.length
                }
                label={`${partner.name} (${selectedCount(partner.id, partner.addresses)}/${partner.addresses.length})`}
                onChange={() => handlePartnerCheck(partner.id)}
              />
            </Box>
          </Popover.Target>
          <Popover.Dropdown style={{ width: 'none' }}>
            <Box
              mb='xs/2'
              onMouseEnter={() => setPartnerSelected(partner.id)}
              onMouseLeave={() => setPartnerSelected(null)}
              className={styles.boxContainer}>
              {partner.addresses
                .sort((a, b) => a.localeCompare(b))
                .map((address, index) => (
                  <Box key={address}>
                    <Checkbox
                      flex={1}
                      key={address}
                      labelPosition='left'
                      label={address}
                      variant='outline'
                      radius='none'
                      checked={selectedPartners[partner.id]?.[address] || false}
                      onChange={() => handleAddressCheck(partner.id, address)}
                      mt='xs'
                      className={styles.checkboxContainer}
                      classNames={{
                        input: styles.inputStyle,
                        label: styles.labelStyle,
                        body: styles.bodyStyle,
                      }}
                    />
                    {index < partner.addresses.length - 1 && <Divider my='xs/2' />}
                  </Box>
                ))}
            </Box>
          </Popover.Dropdown>
        </Popover>
      ))}
    </Box>
  );
};

export default EmailPartnerSelection;
