import { createResponseSchema } from '@core/schema';
import { createRequest } from './Utils';
import { BaseURL } from '@common/constants/BaseUrl';
import { LoginModel } from '@models/LoginModel';
import { TokenDataShema } from '@core/schema/Token';
import { RefreshTokenDataShema } from '@core/schema/RefreshToken';

export class AuthApi {
  static login(request: LoginModel) {
    return createRequest({
      url: `${BaseURL.auth}/login`,
      method: 'POST',
      schema: createResponseSchema(TokenDataShema),
      data: request,
    });
  }
  static refreshToken(refreshToken: string) {
    return createRequest({
      url: `${BaseURL.auth}/refresh-token`,
      method: 'GET',
      schema: createResponseSchema(RefreshTokenDataShema),
      params: {
        refreshToken,
      },
    });
  }
  static logout(refreshToken: string) {
    return createRequest({
      url: `${BaseURL.auth}/logout`,
      method: 'GET',
      params: {
        refreshToken,
      },
    });
  }
}
