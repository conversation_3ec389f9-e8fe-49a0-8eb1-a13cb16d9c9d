import { AxiosError, type AxiosInterceptorManager, type AxiosResponse } from 'axios';
import { isNil } from 'lodash';
import { AxiosResponseApi } from '../Type';
import { logout } from '@common/utils/AuthenticateUtils';
export const handleResponseInterceptor = (response: AxiosInterceptorManager<AxiosResponse<any, any>>) => {
  response.use(
    (response: AxiosResponseApi<any>) => {
      if (!response.data.data && response.data.errorCode) {
        const axiosError = new AxiosError();
        axiosError.code = response.data.errorCode;
        axiosError.message = `${response.data.errorDescription}`;
        axiosError.response = response;
        return Promise.reject(axiosError);
      }
      return response;
    },
    (error: AxiosError) => {
      const response: any = error.response;
      if (response) {
        if (!isNil(response.data.errorCode)) {
          error.code = response.data.errorCode;
          error.message = `${response.data.errorDescription}`;
        }
      }
      if (error.status === 401) {
        logout();
      }
      return Promise.reject(error);
    },
  );
};
