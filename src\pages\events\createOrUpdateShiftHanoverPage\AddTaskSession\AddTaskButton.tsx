import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react';
import { KanbanButton, KanbanCheckbox, KanbanInput } from 'kanban-design-system';
import { Box, Flex, Pill, Stack, Title } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import Modal from '@components/Modal';
import { useDebouncedState, useDisclosure } from '@mantine/hooks';
import InfiniteScrollTable from '@components/dragTable/InfiniteScrollTable';
import { TaskApi } from '@api/TaskApi';
import { TaskStatusEnum, TaskTimeTypeEnum, TaskTypeEnum } from '@common/constants/TaskConstants';
import { Column } from '@components/dragTable/Types';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { Task } from '@core/schema/Task';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { FilterTaskModel } from '@models/FilterTaskModel';
import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import { DEFAULT_TASK_PAGE_SIZE } from '@pages/events/Contants';
import { TABLE_INPUT_MAX_LENGTH } from '@common/constants/ValidationConstant';

interface Props {
  selectedTasks: Task[];
  onSave: (tasks: Task[]) => void;
}

const DEFAULT_SEARCH_PARAM: FilterTaskModel = {
  statuses: [TaskStatusEnum.INPROGRESS, TaskStatusEnum.NEW],
  types: [TaskTypeEnum.TASK],
  assigneeUsers: [],
  creatorUsers: [],
  dateRanges: [],
  search: '',
};

const AddTaskButton = ({ onSave, selectedTasks }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const [currentSelectedTasks, setCurrentSelectedTasks] = useState<Task[]>([]);
  const [searchValue, setSearchValue] = useDebouncedState('', DEFAULT_DEBOUNCE_TIME);
  const { errorUpdateCount, fetchNextPage, flatData, isFetching } = useInfiniteCursorFetch(
    TaskApi.findAll({ ...DEFAULT_SEARCH_PARAM, search: searchValue }, { pageSize: DEFAULT_TASK_PAGE_SIZE }),
    {
      enabled: opened,
    },
  );
  useEffect(() => {
    setCurrentSelectedTasks(selectedTasks);
  }, [selectedTasks]);
  const columns = useMemo<Column<Task>[]>(
    () => [
      {
        id: 'checkbox',
        title: '',
        render: (task) => (
          <KanbanCheckbox
            mb={0}
            checked={currentSelectedTasks.some((ele) => task.id === ele.id)}
            onChange={(event) => {
              const checked = event.currentTarget.checked;
              if (checked) {
                setCurrentSelectedTasks((prev) => [...prev, task]);
              } else {
                setCurrentSelectedTasks((prev) => prev.filter((ele) => ele.id !== task.id));
              }
            }}
          />
        ),
      },
      {
        id: 'taskName',
        title: 'Task Name',
        render: 'name',
        width: '25%',
      },
      {
        id: 'description',
        title: 'Description',
        render: 'description',
        width: '35%',
      },
      {
        id: 'dateTime',
        title: 'Date time',
        render: (data) => (
          <Stack gap='calc(var(--mantine-spacing-xs) / 2)'>
            <Box>
              <span>Start: </span>
              {data.startTime?.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}
            </Box>
            {data.timeType === TaskTimeTypeEnum.FROM_TIME_TO_TIME && (
              <Box>
                <span>End: </span>
                {data.endTime?.format(DATE_FORMAT.FORMAT_DDMMYYYY_HHMMSS)}
              </Box>
            )}
          </Stack>
        ),
      },
      {
        id: 'assignee',
        title: 'Assignee',
        render: 'currentAssigneeUserName',
      },
    ],
    [currentSelectedTasks],
  );
  const onCloseModal = useCallback(() => {
    setCurrentSelectedTasks(selectedTasks);
    close();
  }, [close, selectedTasks]);
  return (
    <Flex align='center' justify='center'>
      <KanbanButton fullWidth={false} variant='outline' leftSection={<IconPlus size={20} />} onClick={open}>
        Add Task
      </KanbanButton>
      <Modal
        onClose={onCloseModal}
        opened={opened}
        size='60%'
        title='Create Task'
        actions={
          <KanbanButton
            onClick={() => {
              onSave(currentSelectedTasks);
              onCloseModal();
            }}>
            Save
          </KanbanButton>
        }>
        <KanbanInput placeholder='Search' onChange={(event) => setSearchValue(event.target.value)} maxLength={TABLE_INPUT_MAX_LENGTH} />
        {currentSelectedTasks?.length > 0 && (
          <Flex my='sm' gap='xs' wrap='wrap'>
            <Title order={5}>Selected tasks:</Title>
            {currentSelectedTasks.map((task) => (
              <Pill key={task.id} withRemoveButton onRemove={() => setCurrentSelectedTasks((prev) => prev.filter((ele) => task.id !== ele.id))}>
                {task.name}
              </Pill>
            ))}
          </Flex>
        )}
        <InfiniteScrollTable
          columns={columns}
          data={flatData || []}
          showIndexColumn={false}
          onScrollToBottom={() => {
            if (errorUpdateCount < 1) {
              fetchNextPage();
            }
          }}
          loading={isFetching}
        />
      </Modal>
    </Flex>
  );
};

export default AddTaskButton;
