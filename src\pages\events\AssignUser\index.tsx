import { Task<PERSON>pi } from '@api/TaskApi';
import { PaginationRequest } from '@api/Type';
import { UserApi } from '@api/UserApi';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import Modal from '@components/Modal';
import { SelectWithPage } from '@components/SelectWithPage';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import useMutate from '@core/hooks/useMutate';
import { Task } from '@core/schema/Task';
import { ComboboxItem } from '@mantine/core';
import { useDebouncedState, useDisclosure } from '@mantine/hooks';
import { AclPermission } from '@models/AclPermission';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { KanbanButton } from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { TaskStatusEnum } from '@common/constants/TaskConstants';

const USER_PAGEGING_DEFAULT: PaginationRequest = { page: 0, search: '', sortBy: 'userName' };

interface Props {
  task: Task;
  onAssignSuccess: (task: Task) => void;
}

const AssignUser = ({ onAssignSuccess, task }: Props) => {
  const currentUser = useSelector(getCurrentUser).userInfo;
  const [opended, { close, open }] = useDisclosure();
  const [searchValue, setSearchValue] = useDebouncedState('', DEFAULT_DEBOUNCE_TIME);

  const { fetchNextPage, flatData, isFetching } = useInfiniteFetch(UserApi.findAll({ ...USER_PAGEGING_DEFAULT, search: searchValue }), {
    showLoading: false,
    enabled: opended,
  });
  const userOptions = useMemo<ComboboxItem[]>(() => {
    return flatData.filter((ele) => ele.isActive).map((user) => ({ label: user.userName, value: user.userName }));
  }, [flatData]);
  const [selectedUser, setSelectedUser] = useState<ComboboxItem | undefined>(undefined);
  useEffect(() => {
    if (opended) {
      setSelectedUser({
        value: task.currentAssigneeUserName || '',
        label: task.currentAssigneeUserName || '',
      });
    }
  }, [opended, task.currentAssigneeUserName]);

  const hasAssignPermission = useMemo(() => isAnyPermissions([AclPermission.taskAssign]), []);

  const { mutate: assignTaskMutate } = useMutate(TaskApi.assignTask, {
    onSuccess: (data) => {
      close();
      setSelectedUser(undefined);
      if (data?.data) {
        onAssignSuccess(data.data);
      }
    },
    confirm:
      currentUser?.userName === selectedUser?.value
        ? {
            title: 'Assign task',
            children: 'Are you sure you want to take on this task?',
          }
        : undefined,
  });

  const onAssignButtonClick = useCallback(() => {
    if (hasAssignPermission) {
      open();
    } else {
      assignTaskMutate({ taskId: task.id, userName: currentUser?.userName || '' });
    }
  }, [assignTaskMutate, currentUser?.userName, hasAssignPermission, open, task.id]);

  if ((currentUser?.userName === task.currentAssigneeUserName && !hasAssignPermission) || TaskStatusEnum.DONE === task.status) {
    return null;
  }
  return (
    <>
      <KanbanButton onClick={onAssignButtonClick} size='md'>
        {hasAssignPermission ? 'Assign' : 'Assign to me'}
      </KanbanButton>
      <Modal
        onClose={() => {
          close();
          setSelectedUser(undefined);
        }}
        opened={opended}
        size='md'
        title='Assignee'
        actions={
          <KanbanButton
            disabled={!selectedUser?.value}
            onClick={() => {
              assignTaskMutate({ taskId: task.id, userName: selectedUser?.value || '' });
            }}>
            Confirm
          </KanbanButton>
        }>
        <SelectWithPage
          label='Assignee'
          required={true}
          value={selectedUser}
          options={userOptions}
          handleScrollToBottom={fetchNextPage}
          onSearch={(val) => {
            setSearchValue(val || '');
          }}
          onBlur={() => {
            setSearchValue('');
          }}
          isLoading={isFetching}
          onChange={(_, data) => {
            setSelectedUser(data);
          }}
        />
        <KanbanButton
          variant='white'
          size='compact-xs'
          onClick={() => setSelectedUser({ value: currentUser?.userName || '', label: currentUser?.userName || '' })}>
          Assign to me
        </KanbanButton>
      </Modal>
    </>
  );
};

export default AssignUser;
