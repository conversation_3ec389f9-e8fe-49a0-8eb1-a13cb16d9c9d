import { WEBHOOK_FIELD_TYPE } from '@common/constants/WebHookConstant';
import { z } from 'zod';

export const DatabaseCollectSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  connectionId: z.number(),
  sqlCommand: z.string(),
  createdDateField: z.string(),
  alertIdField: z.string(),
  interval: z.number(),
  serviceNameType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  serviceId: z.string().optional(),
  serviceName: z.string().optional(),
  serviceMapValue: z.string().optional(),
  applicationType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  applicationId: z.string().optional(),
  applicationName: z.string().optional(),
  applicationMapValue: z.string().optional(),
  alertMapValue: z.string(),
  priorityType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  priorityId: z.number().optional(),
  priorityMapValue: z.string().optional(),
  contactType: z.nativeEnum(WEBHOOK_FIELD_TYPE),
  contactMapValue: z.string().optional(),
  contactCustomValue: z.string().optional(),
  isActive: z.boolean(),
});

export type DatabaseCollect = z.infer<typeof DatabaseCollectSchema>;
