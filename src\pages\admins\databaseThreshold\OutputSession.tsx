import React, { useMemo } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Flex, SimpleGrid } from '@mantine/core';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import { DatabaseThresholdConfigModel } from '@models/DatabaseThresholdConfigModel';
import { CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import useFetch from '@core/hooks/useFetch';
import { DatabaseThresholdConfig } from '@core/schema/DatabaseThresholdConfig';
import { sortBy } from 'lodash';
import ServiceApplicationOutputSession from './ServiceApplicationOutputSession';
import { CustomContentAlert } from '../collectEmail/customContentAlert/CustomContentAlert';

interface Props {
  form: UseFormReturn<DatabaseThresholdConfigModel>;
  isViewMode: boolean;
  oldData?: DatabaseThresholdConfig;
}
export const SPECIAL_MENTIONS = [
  { id: 'count', name: 'Count Number' },
  { id: 'conditionValue', name: 'Threshold' },
];

const OutputSession = ({ form, isViewMode, oldData }: Props) => {
  const { control, setValue } = form;
  //API
  const { data: priorityConfigs } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });
  const priorityConfigOptions = useMemo(() => {
    return sortBy(
      priorityConfigs?.data?.map((config) => ({ value: `${config.id}`, label: config.name + (config.deleted ? ' (DELETED)' : '') })) || [],
      (option) => option.label.toLowerCase(),
    );
  }, [priorityConfigs?.data]);

  return (
    <Flex direction='column' gap='sm'>
      <ServiceApplicationOutputSession form={form} isViewMode={isViewMode} oldData={oldData} />
      <SimpleGrid cols={2}>
        <Controller
          name='recipient'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanInput
              label='Contact'
              disabled={isViewMode}
              maxLength={CHARACTER_CONTACT_ALERT_COLLECT_EMAIL_CONFIG_MAX_LENGTH}
              required
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Controller
          name='priorityId'
          control={control}
          render={({ field }) => (
            <KanbanSelect
              required
              disabled={isViewMode}
              data={priorityConfigOptions}
              searchable={true}
              label='Priority'
              {...field}
              onChange={(val) => field.onChange(Number(val))}
              value={field.value.toString()}
              autoChangeValueByOptions={false}
            />
          )}
        />
      </SimpleGrid>
      <SimpleGrid>
        <Controller
          name='contentJson'
          control={control}
          render={({ field }) => {
            return (
              <CustomContentAlert
                disabled={isViewMode}
                label='Alert content'
                value={field.value}
                onChange={(content, val) => {
                  field.onChange(content);
                  setValue('content', val as string, { shouldValidate: true });
                }}
                specialMentions={SPECIAL_MENTIONS}
                specialMentionOnly={true}
              />
            );
          }}
        />
      </SimpleGrid>
    </Flex>
  );
};

export default OutputSession;
