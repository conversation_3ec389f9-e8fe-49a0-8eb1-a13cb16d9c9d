import { CustomObjectTypeEnum } from '@common/constants/CustomObjectTypeConstant';
import { CUSTOM_OBJECT_INDEX_MAX } from '@common/constants/ValidationConstant';
import { isValidRegex } from '@common/utils/StringUtils';
import {
  CUSTOM_OBJECT_INDEX_TO_INDEX_INPUT_MESSAGE_ERROR,
  CUSTOM_OBJECT_KEYWORD_TO_KEYWORD_INPUT_MESSAGE_ERROR,
  CUSTOM_OBJECT_MATCH_REGEX_INPUT_MESSAGE_ERROR,
  CUSTOM_OBJECT_REGEX_INPUT_MESSAGE_ERROR,
} from '@core/message/MesageConstant';
import { z } from 'zod';

export const CustomObjectModelSchema = z
  .object({
    id: z.number(),
    name: z.string().trim().min(1),
    description: z.string().trim().optional(),
    type: z.nativeEnum(CustomObjectTypeEnum),
    regex: z.string().trim().min(1).refine(isValidRegex, CUSTOM_OBJECT_REGEX_INPUT_MESSAGE_ERROR).optional(),
    fromIndex: z.number().min(0).max(CUSTOM_OBJECT_INDEX_MAX).optional(),
    toIndex: z.number().min(0).max(CUSTOM_OBJECT_INDEX_MAX).optional(),
    fromKeyword: z.string().trim().min(1).optional(),
    toKeyword: z.string().trim().min(1).optional(),
  })
  .superRefine((data, ctx) => {
    if (data.type === CustomObjectTypeEnum.INDEX_TO_INDEX) {
      const fromIndex = data.fromIndex ?? 0;
      const toIndex = data.toIndex ?? 0;
      if (fromIndex >= toIndex) {
        ['fromIndex', 'toIndex'].forEach((ele) =>
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: CUSTOM_OBJECT_INDEX_TO_INDEX_INPUT_MESSAGE_ERROR,
            path: [ele],
          }),
        );
      }
    }
    if (data.type === CustomObjectTypeEnum.KEYWORD_TO_KEYWORD) {
      if (!data.fromKeyword || !data.toKeyword) {
        ['fromKeyword', 'toKeyword'].forEach((ele) =>
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: CUSTOM_OBJECT_KEYWORD_TO_KEYWORD_INPUT_MESSAGE_ERROR,
            path: [ele],
          }),
        );
      }
    }
    if (data.type === CustomObjectTypeEnum.REGEX) {
      if (!data.regex) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: CUSTOM_OBJECT_MATCH_REGEX_INPUT_MESSAGE_ERROR,
          path: ['regex'],
        });
      }
    }
  });

export type CustomObjectModel = z.infer<typeof CustomObjectModelSchema>;
