import { TelegramAlertConfigApi } from '@api/TelegramAlertConfigApi';
import { PaginationRequest } from '@api/Type';
import { DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME } from '@common/constants/PaginationRequestConstant';
import { TELEGRAM_GROUP_ID_REGEX } from '@common/constants/RegexConstant';
import { SortType } from '@common/constants/SortType';
import { TelegramAlertConfigTypeEnum } from '@common/constants/TelegramConstants';
import { TELEGRAM_GROUP_ID_LENGTH } from '@common/constants/ValidationConstant';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import Modal from '@components/Modal';
import Table from '@components/table';
import useFetch from '@core/hooks/useFetch';
import { TELEGRAM_GROUP_ID_REGEX_INPUT_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { TelegramAlertConfig } from '@core/schema/Telegram';
import { useDisclosure } from '@mantine/hooks';
import { IconSettings } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanInput,
  KanbanSwitch,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import React, { useCallback, useMemo, useRef, useState } from 'react';
type TelegramModalProps = {
  serviceId: string;
  serviceName: string;
  handleChangeAlertConfigDebounce: (config: TelegramAlertConfig, type: TelegramAlertConfigTypeEnum, isActiveChange: boolean) => void;
  getGroupChatId: (config: TelegramAlertConfig, type: TelegramAlertConfigTypeEnum) => string;
  getCheckedEnable: (config: TelegramAlertConfig, type: TelegramAlertConfigTypeEnum) => boolean;
};
export const TelegramModal = (props: TelegramModalProps) => {
  const { getCheckedEnable, getGroupChatId, handleChangeAlertConfigDebounce, serviceId, serviceName } = props;
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [applicationTableAffected, setApplicationTableAffected] = useState<PaginationRequest>(DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME);
  const tableAppRef = useRef<KanbanTableSelectHandleMethods>(null);
  const [groupIdError, setGroupIdError] = useState<Record<string, boolean>>({});

  const { data: listApplication } = useFetch(TelegramAlertConfigApi.findAllApplications(serviceId, applicationTableAffected), {
    enabled: openedModal,
    placeholderData: (prev) => prev,
  });

  const handleUpdateAppTablePagination = useCallback((data: TableAffactedSafeType<TelegramAlertConfig>) => {
    setApplicationTableAffected((state) => ({
      ...state,
      page: data.page - 1,
      size: data.rowsPerPage,
      sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME.sortBy,
      sortOrder: data.isReverse || !data.sortedBy ? SortType.ASC : SortType.DESC,
      search: data.search,
    }));
  }, []);

  const hasAnyFalseGroupIdError = useMemo(() => {
    return Object.values(groupIdError).some((value) => value);
  }, [groupIdError]);

  const columnsApp = useMemo((): ColumnType<TelegramAlertConfig>[] => {
    return [
      {
        title: 'Application Name',
        name: 'applicationName',
        width: '20%',
      },
      {
        title: 'Telegram group ID',
        name: 'groupId',
        customRender: (data, rowData) => {
          const rowDataCopy = { ...rowData };
          return (
            <KanbanInput
              pt={10}
              key={rowDataCopy.applicationId}
              maxLength={TELEGRAM_GROUP_ID_LENGTH}
              error={groupIdError[rowDataCopy.applicationId || ''] ? TELEGRAM_GROUP_ID_REGEX_INPUT_MESSAGE_ERROR : undefined}
              placeholder='Telegram group ID'
              defaultValue={getGroupChatId(rowData, TelegramAlertConfigTypeEnum.APPLICATION)}
              onChange={(data) => {
                const isValid = !data.target.value || TELEGRAM_GROUP_ID_REGEX.test(data.target.value);
                setGroupIdError((prev) => ({
                  ...prev,
                  [rowDataCopy.applicationId || '']: !isValid,
                }));
                if (isValid) {
                  rowDataCopy.groupChatId = data.target.value;
                  handleChangeAlertConfigDebounce(rowDataCopy, TelegramAlertConfigTypeEnum.APPLICATION, false);
                }
              }}
            />
          );
        },
      },
    ];
  }, [getGroupChatId, groupIdError, handleChangeAlertConfigDebounce]);

  const tableAppProps: KanbanTableProps<TelegramAlertConfig> = useMemo(() => {
    return {
      columns: columnsApp,
      data: listApplication?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listApplication?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(applicationTableAffected, dataSet)) {
            handleUpdateAppTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          const dataCopy = { ...data };
          return (
            <KanbanTooltip label='Enable Telegram'>
              <KanbanSwitch
                key={data.applicationId}
                placeholder='En'
                defaultChecked={getCheckedEnable(dataCopy, TelegramAlertConfigTypeEnum.APPLICATION)}
                onChange={(value) => {
                  dataCopy.isActive = value.target.checked;
                  handleChangeAlertConfigDebounce(dataCopy, TelegramAlertConfigTypeEnum.APPLICATION, true);
                }}
              />
            </KanbanTooltip>
          );
        },
      },
    };
  }, [
    applicationTableAffected,
    columnsApp,
    getCheckedEnable,
    handleChangeAlertConfigDebounce,
    handleUpdateAppTablePagination,
    listApplication?.data?.content,
    listApplication?.data?.totalElements,
  ]);

  return (
    <>
      <KanbanIconButton
        variant='transparent'
        size={'sm'}
        onClick={() => {
          openModal();
        }}>
        <IconSettings />
      </KanbanIconButton>
      <Modal
        size={'100%'}
        opened={openedModal}
        onClose={() => {
          closeModal();
        }}
        title={`Config application for service: ${serviceName}`}
        actions={
          <KanbanButton
            disabled={hasAnyFalseGroupIdError}
            onClick={() => {
              closeModal();
            }}>
            Save
          </KanbanButton>
        }>
        <Table ref={tableAppRef} {...tableAppProps} />
      </Modal>
    </>
  );
};
export default TelegramModal;
