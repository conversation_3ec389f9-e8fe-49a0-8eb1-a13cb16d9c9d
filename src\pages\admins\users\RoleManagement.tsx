import { <PERSON><PERSON><PERSON> } from '@api/RoleApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import useFetch from '@core/hooks/useFetch';
import { Role } from '@core/schema/Role';
import { ActionIcon, Tooltip } from '@mantine/core';
import { IconLockOff, IconLockOpen, IconTrash } from '@tabler/icons-react';
import { KanbanIconButton, KanbanTableProps, KanbanTableSelectHandleMethods, KanbanText } from 'kanban-design-system';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import useMutate from '@core/hooks/useMutate';
import { RoleSettingModal } from './RoleSettingModal';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
const columns = [
  {
    title: 'Role Name',
    name: 'name',
    customRender: (data: string) => {
      return (
        <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
          {data}
        </KanbanText>
      );
    },
  },
  {
    title: 'Description',
    name: 'description',
  },
];
export const RoleManagement = () => {
  const { data: roles, refetch: fetchRoles } = useFetch(RoleApi.findAll());

  const { mutate: deleteByIdMutate } = useMutate(RoleApi.deleteById, {
    successNotification: () => {
      return {
        title: 'Delete',
        message: `Delete role Success`,
      };
    },
    onSuccess: () => {
      fetchRoles();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const { mutate: deleteBatchMutate } = useMutate(RoleApi.deleteBatch, {
    successNotification: () => {
      return {
        title: 'Delete',
        message: `Delete roles Success`,
      };
    },
    onSuccess: () => {
      fetchRoles();
    },
  });

  const { mutate: activeMutate } = useMutate(RoleApi.activeById, {
    successNotification: () => {
      return {
        title: 'Active Role',
        message: `Active role success`,
      };
    },
    confirm: { title: 'Confirm active', textConfirm: 'Are you sure active role?' },
    onSuccess: () => {
      fetchRoles();
    },
  });

  const { mutate: inactiveMutate } = useMutate(RoleApi.inactiveById, {
    successNotification: () => {
      return {
        title: 'Inactive Role',
        message: `Inactive role success`,
      };
    },
    confirm: { title: 'Confirm inactive', textConfirm: 'Are you sure inactive role?' },
    onSuccess: () => {
      fetchRoles();
    },
  });

  const [currentDataSelected, setCurrentDataSelected] = useState<Role[]>([]);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  /**
   * custom search.
   */
  const onSearched = useCallback((datas: Role[], search: string): Role[] => {
    const lowerCaseSearch = search.toLowerCase();
    return datas.filter((item) => {
      const name = item.name || '';
      const description = item.description || '';
      if (name.toLowerCase().includes(lowerCaseSearch) || description.toLowerCase().includes(lowerCaseSearch)) {
        return true;
      }
      return false;
    });
  }, []);

  const tableProps: KanbanTableProps<Role> = useMemo(() => {
    return {
      searchable: {
        enable: true,
        onSearched: onSearched,
      },
      sortable: {
        enable: true,
      },
      onRowClicked: (row) => {
        <RoleSettingModal tableRef={tableRef} roleId={row.id} />;
      },
      selectableRows: {
        enable: isAnyPermissions([AclPermission.roleManageDelete]),
        onDeleted(rows) {
          deleteBatchMutate(rows.map((x) => x.id));
        },
        crossPageSelected: {
          rowKey: 'id',
          selectedRows: currentDataSelected,
          setSelectedRows: setCurrentDataSelected,
        },
      },
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },

      columns: columns,
      data: roles?.data || [],
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              {isAnyPermissions([AclPermission.roleManageEdit]) && (
                <Tooltip label={data.active ? 'Inactive role' : 'Active role'}>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      data.active ? inactiveMutate(data.id) : activeMutate(data.id);
                    }}>
                    {data.active ? <IconLockOpen /> : <IconLockOff />}
                  </KanbanIconButton>
                </Tooltip>
              )}
              {isAnyPermissions([AclPermission.roleManageCreate]) && <RoleSettingModal tableRef={tableRef} roleId={data.id} isCopy={true} />}
              {isAnyPermissions([AclPermission.roleManageEdit]) && <RoleSettingModal tableRef={tableRef} roleId={data.id} />}
              <GuardComponent requirePermissions={[AclPermission.roleManageDelete]}>
                <ActionIcon
                  variant='transparent'
                  color='red'
                  onClick={() =>
                    deleteByIdMutate(data.id, {
                      confirm: getDefaultDeleteConfirmMessage(data.name),
                    })
                  }>
                  <IconTrash width={20} height={24} />
                </ActionIcon>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [onSearched, currentDataSelected, roles?.data, deleteBatchMutate, inactiveMutate, activeMutate, deleteByIdMutate]);

  return (
    <>
      <HeaderTitleComponent
        title='Roles Management'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.roleManageCreate]}>
            <RoleSettingModal tableRef={tableRef} />
          </GuardComponent>
        }
      />
      <Table ref={tableRef} {...tableProps} />
    </>
  );
};
export default RoleManagement;
