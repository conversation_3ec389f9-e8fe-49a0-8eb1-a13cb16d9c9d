import Modal from '@components/Modal';
import { Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton, KanbanIconButton, KanbanTooltip } from 'kanban-design-system';
import React, { useCallback, useEffect } from 'react';
import ExecutionForm from './ExecutionForm';
import { useForm } from 'react-hook-form';
import { ExecutionModel, ExecutionModelSchema } from '@models/ExecutionModel';
import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { IconEdit, IconEye } from '@tabler/icons-react';
import { ExecutionApi } from '@api/ExecutionApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { zodResolver } from '@hookform/resolvers/zod';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';

const DEFAULT_EXECUTION_VALUE: ExecutionModel = {
  name: '',
  type: ExecutionTypeEnum.PYTHON,
  script: '',
  description: '',
};

interface Props {
  executionId: string;
  onUpdateSuccess: () => void;
}

const UpdateExecutionButton = ({ executionId, onUpdateSuccess }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const { data: executionData } = useFetch(ExecutionApi.findById(executionId), { enabled: opened });
  const form = useForm<ExecutionModel>({ defaultValues: DEFAULT_EXECUTION_VALUE, resolver: zodResolver(ExecutionModelSchema) });
  const hasExecutionEditPermission = isAnyPermissions([AclPermission.executionEdit]);

  const onClose = useCallback(() => {
    close();
  }, [close]);
  const { mutate } = useMutate(ExecutionApi.createOrUpdate, {
    successNotification: 'Update Execution successfully.',
    onSuccess: () => {
      onUpdateSuccess();
      onClose();
    },
  });
  const { formState } = form;
  useEffect(() => {
    if (executionData?.data) {
      form.reset(executionData.data);
    }
  }, [executionData, form]);
  const onSaveClick = useCallback(() => {
    mutate(form.getValues());
  }, [form, mutate]);

  return (
    <>
      <KanbanTooltip label='Edit'>
        <KanbanIconButton variant='transparent' size={'sm'} onClick={open}>
          {hasExecutionEditPermission ? <IconEdit /> : <IconEye />}
        </KanbanIconButton>
      </KanbanTooltip>
      <Modal
        size='xl'
        opened={opened}
        onClose={onClose}
        title={hasExecutionEditPermission ? 'Update Execution' : 'View Execution'}
        actions={
          hasExecutionEditPermission ? (
            <KanbanButton onClick={onSaveClick} disabled={!formState.isValid}>
              Save
            </KanbanButton>
          ) : undefined
        }>
        <Box p='xs'>
          <ExecutionForm form={form} fetchGroup={opened} readOnly={!hasExecutionEditPermission} />
        </Box>
      </Modal>
    </>
  );
};

export default UpdateExecutionButton;
