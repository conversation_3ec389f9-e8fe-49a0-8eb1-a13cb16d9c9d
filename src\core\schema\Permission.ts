import { PermissionActionEnum } from '@common/constants/PermissionAction';
import { PermissionModuleEnum } from '@common/constants/PermissionModule';
import { PermissionTypeEnum } from '@common/constants/PermissionType';
import { z } from 'zod';

export const PermissionSchema = z.object({
  module: z.nativeEnum(PermissionModuleEnum),
  action: z.nativeEnum(PermissionActionEnum),
  type: z.nativeEnum(PermissionTypeEnum).optional(),
  moduleId: z.string().optional(),
  moduleParentId: z.string().optional(),
});

export type Permission = z.infer<typeof PermissionSchema>;
