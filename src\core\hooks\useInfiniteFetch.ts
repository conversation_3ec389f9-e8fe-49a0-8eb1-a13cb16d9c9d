import { RequestConfig } from '../api/BaseApi';
import { InfiniteData, QueryKey, UseInfiniteQueryOptions } from '@tanstack/react-query';
import { Page, ResponseData } from '@core/schema';
import { PaginationRequest } from '@api/Type';
import { NotificationConfig } from './AppConfigTypes';
import useInfiniteFetchCore from './useInfiniteFetchCore';
import { useMemo } from 'react';
type AppConfig<TQueryFnData> = {
  showLoading?: boolean;
  errorNotification?: (error: Error) => NotificationConfig;
  withSignal?: boolean;
} & Omit<
  UseInfiniteQueryOptions<TQueryFnData, Error, InfiniteData<TQueryFnData, number>, TQueryFnData, QueryKey, number>,
  'queryKey' | 'queryFn' | 'getNextPageParam' | 'getPreviousPageParam' | 'initialPageParam'
>;
function useInfiniteFetch<TQueryFnData extends ResponseData<Page<any>>, SearchParams extends PaginationRequest>(
  requestConfig: RequestConfig<TQueryFnData, SearchParams>,
  appConfig?: AppConfig<TQueryFnData>,
) {
  const getNextPageParam = (lastPage: TQueryFnData) => {
    if (lastPage.data?.last) {
      return undefined;
    }
    return (lastPage.data?.number || 0) + 1;
  };
  const getPreviousPageParam = (lastPage: TQueryFnData) => {
    if (lastPage.data?.first) {
      return undefined;
    }
    return (lastPage.data?.number || 0) - 1;
  };
  const getPageParam = (requestConfig: RequestConfig<TQueryFnData, SearchParams>) => {
    return requestConfig.params?.page || 0;
  };

  const queryResult = useInfiniteFetchCore<TQueryFnData, SearchParams>(
    requestConfig,
    { getNextPageParam, getPreviousPageParam, getPageParam },
    appConfig,
  );

  const { data } = queryResult;
  const flatData: NonNullable<TQueryFnData['data']>['content'] = useMemo(
    () => data?.pages?.flatMap((page) => page.data?.content ?? []) ?? [],
    [data?.pages],
  );

  return { ...queryResult, flatData };
}

export default useInfiniteFetch;
