import { ColorPicker, Flex, Stack, Text } from '@mantine/core';
import { KanbanInput, KanbanNumberInput } from 'kanban-design-system';
import React, { useCallback, useMemo } from 'react';
import { formatRGB, parseRGB } from './Utils';
import { AlertPriorityConfigModel } from '@models/AlertPriorityConfigModel';
import { UseFormReturnType } from '@mantine/form';
import { DEFAULT_PRIORITY, RBG_INPUT_MAX_LENGTH, RBG_MAX_VALUE, RGB_MIN_VALUE } from './Constants';
import classes from './AlertPriorityConfig.module.css';
import { MAX_ALERT_PRIORITY_CONFIG_NAME_LENGTH } from '@common/constants/ValidationConstant';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';
import useFetch from '@core/hooks/useFetch';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

interface Props {
  form: UseFormReturnType<AlertPriorityConfigModel>;
}

const RGBInput = ({ onChange, value }: { value: number; onChange: (number: number) => void }) => {
  return (
    <KanbanNumberInput
      min={RGB_MIN_VALUE}
      max={RBG_MAX_VALUE}
      maxLength={RBG_INPUT_MAX_LENGTH}
      value={value}
      defaultValue={0}
      allowDecimal={false}
      allowNegative={false}
      styles={{ input: { padding: 'calc(var(--mantine-spacing-xs) / 2)' } }}
      onValueChange={({ formattedValue }) => onChange(parseInt(formattedValue || '0'))}
    />
  );
};

export const ColorInput = ({ form }: Props) => {
  const { getInputProps, setFieldValue } = form;
  const onColorNumberChange = useCallback(
    (r: number, g: number, b: number) => {
      setFieldValue('color', formatRGB(r, g, b));
    },
    [setFieldValue],
  );
  const [r, g, b] = useMemo(() => parseRGB(form.values.color), [form.values.color]);
  return (
    <Flex gap='md' p='sm'>
      <ColorPicker
        format='rgb'
        styles={{
          sliderOverlay: { borderRadius: 'var(--mantine-radius-xs)' },
          wrapper: { display: 'flex', flexDirection: 'column' },
        }}
        {...getInputProps('color')}
        style={{ flex: 1 }}
      />
      <Stack style={{ flex: 1 }}>
        <Flex gap='xs'>
          <Text>RGB</Text>
          <RGBInput value={r} onChange={(value) => onColorNumberChange(value, g, b)} />
          <RGBInput value={g} onChange={(value) => onColorNumberChange(r, value, b)} />
          <RGBInput value={b} onChange={(value) => onColorNumberChange(r, g, value)} />
        </Flex>
        <Flex w='100%' h={60} bg={form.values.color} justify='space-evenly' p='sm' align='center'>
          <Text className={classes.exampleWhiteText}>Text</Text>
          <Text className={classes.exampleBlackText}>Text</Text>
        </Flex>
      </Stack>
    </Flex>
  );
};

const PriorityConfigForm = ({ form }: Props) => {
  const { getInputProps, values } = form;
  const { data: dependencies } = useFetch(AlertPriorityConfigApi.findAllDependenciesById(values.id || 0), {
    enabled: !!values.id,
  });
  const dependencyConfig: DependencyItem[] = useMemo(() => {
    return [
      {
        dependencyEntity: 'webhooks',
        dependencies: dependencies?.data?.webHooks ?? [],
      },
      {
        dependencyEntity: 'collect email configs',
        dependencies: dependencies?.data?.collectEmailConfigs ?? [],
      },
      {
        dependencyEntity: 'collect database configs',
        dependencies: dependencies?.data?.databaseCollects ?? [],
      },
      {
        dependencyEntity: 'alert group configs',
        dependencies: dependencies?.data?.alertGroupConfigs ?? [],
      },
      {
        dependencyEntity: 'maintenance time configs',
        dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
      },
      {
        dependencyEntity: 'modify alert configs',
        dependencies: dependencies?.data?.modifyAlertConfigs ?? [],
      },
      {
        dependencyEntity: 'database threshold configs',
        dependencies: dependencies?.data?.databaseThresholdConfigs ?? [],
      },
    ];
  }, [
    dependencies?.data?.webHooks,
    dependencies?.data?.collectEmailConfigs,
    dependencies?.data?.databaseCollects,
    dependencies?.data?.alertGroupConfigs,
    dependencies?.data?.maintenanceTimeConfigs,
    dependencies?.data?.modifyAlertConfigs,
    dependencies?.data?.databaseThresholdConfigs,
  ]);
  return (
    <>
      <DependenciesWarningAlert mainEntity='Alert priority config' dependencyConfigs={dependencyConfig} isDeleted={false} />
      <Flex direction={'column'}>
        <KanbanInput
          name='name'
          placeholder='Priority Name'
          label='Priority Name'
          required
          maxLength={MAX_ALERT_PRIORITY_CONFIG_NAME_LENGTH}
          disabled={values.id === DEFAULT_PRIORITY}
          {...getInputProps('name')}
        />
        <ColorInput form={form} />
      </Flex>
    </>
  );
};

export default PriorityConfigForm;
