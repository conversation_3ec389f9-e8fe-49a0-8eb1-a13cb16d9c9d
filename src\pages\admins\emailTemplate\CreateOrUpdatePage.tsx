import React, { useEffect, useState } from 'react';
import { KanbanButton, KanbanInput, KanbanTagsInput } from 'kanban-design-system';
import { Accordion, Box, Flex, InputLabel } from '@mantine/core';
import { IconArrowBack, IconPlus } from '@tabler/icons-react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { EmailTemplateModel, EmailTemplateModelSchema } from '@models/EmailTemplateModel';
import { useForm, zodResolver } from '@mantine/form';
import { EmailTemplateApi } from '@api/EmailTemplateApi';
import useMutate from '@core/hooks/useMutate';
import useFetch from '@core/hooks/useFetch';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import {
  MAX_CHARACTER_NAME_LENGTH,
  MAX_DESCRIPTION_LENGTH,
  MAX_EMAIL_ADDRESSES,
  MAX_EMAIL_CHARACTER_LENGTH,
  MAX_EMAIL_SUBJECT_CHARACTER_LENGTH,
} from '@common/constants/ValidationConstant';
import { FileStorage } from '@core/schema/FileStorage';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import MultiFileInput from '../emailRichTextEditor/MultiFileInput';
import TextEditorTemplate from '../emailRichTextEditor/TextEditorTemplate';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { EmailTemplateConfigAction } from '.';

const DEFAULT_TEMPLATE: EmailTemplateModel = {
  name: '',
  to: [],
  id: undefined,
  content: '',
  subject: '',
  cc: [],
  fileStorages: [],
  files: [],
};

const CreateOrUpdatePage = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const idNumber = Number(id);
  const isUpdateMode = !!idNumber;
  const navigate = useNavigate();
  const { data: emailTemplateDetail } = useFetch(EmailTemplateApi.findById(idNumber), {
    enabled: isUpdateMode,
  });
  const [remainingFiles, setRemainingFiles] = useState<FileStorage[]>(emailTemplateDetail?.data?.fileStorages || []);
  const [files, setFiles] = useState<File[]>([]);
  const [currentTab, setCurrentTab] = useState<string>(EmailTemplateConfigAction.CREATE);
  const isViewMode = currentTab === EmailTemplateConfigAction.VIEW;

  const { mutate: saveMutate } = useMutate(EmailTemplateApi.save, {
    successNotification: isUpdateMode ? `Update template successfully` : 'Create template successfully',
    onSuccess: () => {
      navigate(ROUTE_PATH.EMAIL_TEMPLATE);
    },
  });

  const { getInputProps, isValid, setFieldValue, setValues, validate, values } = useForm({
    initialValues: DEFAULT_TEMPLATE,
    validate: zodResolver(EmailTemplateModelSchema),
    validateInputOnChange: true,
  });

  const handleEditorChange = (newContent: string) => {
    setFieldValue('content', newContent);
  };
  useEffect(() => {
    if (!isUpdateMode) {
      return;
    }
    if (emailTemplateDetail?.data) {
      setValues({
        id: emailTemplateDetail?.data?.id || 0,
        name: emailTemplateDetail?.data?.name || '',
        description: emailTemplateDetail?.data?.description || '',
        cc: emailTemplateDetail?.data?.cc || [],
        to: emailTemplateDetail?.data?.to || [],
        subject: emailTemplateDetail?.data?.subject || '',
        content: emailTemplateDetail?.data?.content || '',
      });
      setRemainingFiles(emailTemplateDetail?.data?.fileStorages || []);
    }
  }, [setValues, isUpdateMode, emailTemplateDetail?.data]);

  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);

  const handleFilesChange = (newFiles: File[], remainingFiles: FileStorage[]) => {
    setFiles(newFiles);
    setRemainingFiles(remainingFiles);
  };
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title={isViewMode ? `View Email Template` : idNumber ? `Edit Email Template` : `Create Email Template`}
        rightSection={
          <Flex direction='row' gap='xs' align='center'>
            <KanbanButton
              variant={'light'}
              size='xs'
              onClick={() => {
                navigate(ROUTE_PATH.EMAIL_TEMPLATE);
              }}
              leftSection={<IconArrowBack />}>
              Cancel
            </KanbanButton>
            {!isViewMode && (
              <GuardComponent requirePermissions={[AclPermission.emailTemplateEdit, AclPermission.emailTemplateCreate]}>
                <KanbanButton
                  disabled={!isValid()}
                  size='xs'
                  onClick={() => {
                    if (!validate().hasErrors) {
                      saveMutate({
                        ...EmailTemplateModelSchema.parse(values),
                        files: files,
                        fileStorages: remainingFiles,
                      });
                    }
                  }}
                  leftSection={<IconPlus />}>
                  Save
                </KanbanButton>
              </GuardComponent>
            )}
          </Flex>
        }
      />
      <Accordion multiple defaultValue={['item-1', 'item-2']}>
        <Accordion.Item aria-expanded={true} value='item-1'>
          <Accordion.Control>General Information</Accordion.Control>
          <Accordion.Panel>
            <KanbanInput disabled={isViewMode} maxLength={MAX_CHARACTER_NAME_LENGTH} required label='Template Name' {...getInputProps('name')} />
            <KanbanInput disabled={isViewMode} maxLength={MAX_DESCRIPTION_LENGTH} label='Description' {...getInputProps('description')} />
          </Accordion.Panel>
        </Accordion.Item>
        <Accordion.Item value='item-2'>
          <Accordion.Control>Email Template</Accordion.Control>
          <Accordion.Panel>
            <KanbanTagsInput
              disabled={isViewMode}
              leftSection={<InputLabel>To</InputLabel>}
              acceptValueOnBlur={true}
              maxTags={MAX_EMAIL_ADDRESSES}
              maxLength={MAX_EMAIL_CHARACTER_LENGTH}
              allowDuplicates={true}
              {...getInputProps('to')}
            />
            <KanbanTagsInput
              disabled={isViewMode}
              leftSection={<InputLabel>CC</InputLabel>}
              maxTags={MAX_EMAIL_ADDRESSES}
              acceptValueOnBlur={true}
              maxLength={MAX_EMAIL_CHARACTER_LENGTH}
              allowDuplicates={true}
              {...getInputProps('cc')}
            />
            <KanbanInput
              disabled={isViewMode}
              leftSectionWidth={70}
              required
              leftSection={<InputLabel required>Subject</InputLabel>}
              maxLength={MAX_EMAIL_SUBJECT_CHARACTER_LENGTH}
              {...getInputProps('subject')}
            />
            <MultiFileInput isViewMode={isViewMode} onFilesChange={handleFilesChange} fileStorages={remainingFiles} />
            <TextEditorTemplate isViewMode={isViewMode} value={values.content} onChange={handleEditorChange} />
          </Accordion.Panel>
        </Accordion.Item>
      </Accordion>
    </Box>
  );
};
export default CreateOrUpdatePage;
