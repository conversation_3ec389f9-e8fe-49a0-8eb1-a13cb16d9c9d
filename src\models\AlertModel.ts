import type { AlertStatusEnum } from '@common/constants/AlertStatusConstant';
import { CursorPagingRequestModel } from './CursorPagingRequestModel';
import { AlertCursor } from '@core/schema/AlertCursor';
import { Application, Service } from '@core/schema';
import { AlertDurationCompareOperatorEnum, AlertDurationCompareUnitEnum } from '@pages/report/Constants';

export type AlertFormFilterModel = {
  content: string;
  recipient?: string;
  recipients?: string[];
  alertPriorityConfigIds: string[];
  statuses: AlertStatusEnum[];
  rangeDate?: string;
  fromDate?: string;
  toDate?: string;
  services?: Service[];
  applications?: Application[];
  alertGroupId?: number;
  closedBy?: string[];
  closedDuration?: number;
  closeDurationOperator?: AlertDurationCompareOperatorEnum;
  closeDurationUnit?: AlertDurationCompareUnitEnum;
} & CursorPagingRequestModel<AlertCursor>;

export type AlertPaginationRequest = Omit<AlertFormFilterModel, 'services' | 'applications'> & { serviceIds: string[]; applicationIds: string[] };
