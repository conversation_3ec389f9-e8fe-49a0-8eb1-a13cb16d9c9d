import { z } from 'zod';
import { createDateTimeSchema } from './Common';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { TaskShiftEnum, TaskStatusEnum, TaskTimeTypeEnum, TaskTypeEnum } from '@common/constants/TaskConstants';
import { TaskUserSchema } from './TaskUser';

const BaseTaskSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  startTime: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS).optional(),
  endTime: createDateTimeSchema(DATE_FORMAT.FORMAT_YYYY_MM_DD_HH_MM_SS).optional(),
  type: z.nativeEnum(TaskTypeEnum),
  timeType: z.nativeEnum(TaskTimeTypeEnum).optional(),
  shift: z.nativeEnum(TaskShiftEnum).optional(),
  status: z.nativeEnum(TaskStatusEnum),
  currentAssigneeUserName: z.string().optional(),
  createdBy: z.string().optional(),
  deleted: z.boolean().optional(),
});

export const TaskSchema = BaseTaskSchema.extend({
  childrenTasks: z.array(BaseTaskSchema).optional(),
  handoverUsers: z.array(TaskUserSchema).optional(),
});

export type Task = z.infer<typeof TaskSchema>;
